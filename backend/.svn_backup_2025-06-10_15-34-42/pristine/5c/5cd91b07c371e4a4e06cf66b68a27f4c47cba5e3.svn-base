<?php

/// \cond onlyria
/**
 * Ce fichier permet la synchronisation comme la version "sync.php" mais en version "bulk"
 * riashop génére un genre de dump des données zip pour que les traitements côté table soit plus rapide que sur la version normal
 *
 * Pour des raisons de performance, la génération du zip peut parfois prendre du temps et aboutir à un timeout de yuto qui est fixé à 60s.
 * C'est pourquoi le fichier Zip qui généré est généré à la volé est ensuite stocké sur le disque dur du serveur.
 * Un test est fait au début de l'appel pour vérifier sur le zip à pas déjà été générer et si c'est cas il va renvoyer cette version.
 * Le traitement machine est quasi instantané du coup.
 *
 * Attention dans le cas ou un objet de la meme classe est "lock" sur yuto, celui ci appelera la version en JSON (GET /devices/sync/)
 * pour faire beaucoup plus de controle d'intégrité.
 *
 *	\code
 *		GET /devices/sync_bulk/
 *	\endcode
 *
 * @param int $cls_id Obligatoire, identifiant de la classe.
 * @param int $db Facultatif, numéro de révision de la base de donnée local de Yuto
 * @param datetime $date Optionnel, date de dernière mise à jour minimale des objets à retourner.
 * @param string $seq Optionnel, numéro de séquence (timestamp) du dernier objet synchronisé dans le cas ou la data vient du couchdb
 *
 * @return zip Le fichier zip généré comprend plusieurs fichier :
 *		- header.json : (Obligatoire)
 *				contient les données informations sur le paquet comme la date de dernière synchronisation , le nombre d'objet
 *				\code{.json}
 *       		{
 * 					"last_date": Date de modification du dernier objet présent dans le paquet
 * 					"last_seq": Numéro de séquence (timestamp) du dernier objet présent dans le paquet
 * 					"count": Nombre d'élémént total à synchronisé sur cette classe
 * 					"count_in_package": Nombre d'élement dans le paquet
 * 					"count_checked_row": Nombre d'élément parcouru pour générer le paquet, utilisé pour debug seulement
 * 				}
 *				\endcode
 * 		- ids.json : (Obligatoire)
 * 				la liste des identifiants d'objets traité par ce paquet
 *				\code{.json}
 *       		{
 * 					[[id_0, id_1, id_2]] : tableau de tableau avec les 3 clés primaires de l'objet, dans le cas de produit par exemple nous n'avons que id_0
 * 				}
 *				\endcode
 *		- ids_del.json : (Obligatoire)
 *				la liste des identifiants d'objets supprimé par ce paquet
 *				\code{.json}
 *       		{
 * 					[[id_0, id_1, id_2]] : tableau de tableau avec les 3 clés primaires de l'objet, dans le cas de produit par exemple nous n'avons que id_0
 * 				}
 *				\endcode
 *		- obj.sql : (Obligatoire)
 *				les requetes sql pour l'ajout ou la modification des objets
 *		- obj_del.sql : (Obligatoire)
 *				les requetes sql pour la suppression des objets
 *		- field_values_del.sql :
 *				Fichier Facultatif: dépend de l'inclusion de champs avancé au niveau fichier devices.inc, function dev_devices_get_sync_data_callback
 *				les requetes sql pour la suppression des valeurs de champs avancés sur les objets du paquets
 *		- field_values.sql :
 *				Fichier Facultatif: dépend de l'inclusion de champs avancé au niveau fichier devices.inc, function dev_devices_get_sync_data_callback
 *				les requetes sql ajout des des valeurs de champs avancés sur les objets du paquets
 *		- field_models_del.sql :
 *				Fichier Facultatif: dépend de l'inclusion de champs avancé au niveau fichier devices.inc, function dev_devices_get_sync_data_callback
 *				les requetes sql de suppresion des liaisons avec le modèle de saisie
 *		- field_models.sql :
 *				Fichier Facultatif: dépend de l'inclusion de champs avancé au niveau fichier devices.inc, function dev_devices_get_sync_data_callback
 *				les requetes sql ajout des liaisons avec le modèle de saisie
 *		- note_values_del.sql :
 *				Fichier Facultatif: dépend de l'inclusion des notes au niveau fichier devices.inc, function dev_devices_get_sync_data_callback
 *				les requetes sql pour la suppression des notes sur les objets du paquets
 *		- note_values.sql :
 *				Fichier Facultatif: dépend de l'inclusion des notes au niveau fichier devices.inc, function dev_devices_get_sync_data_callback
 *				les requetes sql pour l'ajout des notes sur les objets du paquets
 *		- doc_values_del.sql :
 *				Fichier Facultatif: dépend de l'inclusion des documents au niveau fichier devices.inc, function dev_devices_get_sync_data_callback
 *				les requetes sql pour la suppression des liens entre documents et les objets du paquets
 *		- doc_values.sql :
 *				Fichier Facultatif: dépend de l'inclusion des documents au niveau fichier devices.inc, function dev_devices_get_sync_data_callback
 *				les requetes sql pour l'ajout des liens entre documents et les objets du paquets
 *		- relations_del.sql :
 *				Fichier Facultatif: dépend de l'inclusion des relations au niveau fichier devices.inc, function dev_devices_get_sync_data_callback
 *				les requetes sql pour la suppression des liens entre les objets
 *		- relations.sql :
 *				Fichier Facultatif: dépend de l'inclusion des relations au niveau fichier devices.inc, function dev_devices_get_sync_data_callback
 *				les requetes sql pour l'ajout des liens entre les objets
 *		- xxxxx_del.sql :
 *				Dans le cas de classe avec les "related" un fichier sera généré par relation pour supprimer
 *				toutes les données liées aux objets du paquet. Le nom du fichier dépend des données présente dedans.
 *		- xxxxx.sql :
 *				Dans le cas de classe avec les "related" un fichier sera généré par relation pour ajouter
 *				toutes les données liées aux objets du paquet. Le nom du fichier dépend des données présente dedans.
 *
 * 	Exemple de retour possible pour les Catégories de produits
 *		\code{.files}
 *       {
 * 			"header.json" : {"last_date":"2021-04-29 09:25:17","last_seq":"","count":"21","count_in_package":21,"count_checked_row":21}
 * 			"ids.json" : [["248615"],["239075"],["246234"],["239078"],["239074"],["239079"],["237208"]]
 * 			"ids_del.json" : [["248462"]]
 * 			"obj_del": Fichier delete from prd_categories
 * 			"obj" : Fichier replace into prd_categories
 * 			"hierarchy_del" : Fichier delete from prd_cat_hierarchy
 * 			"hierarchy" : Fichier replace into prd_cat_hierarchy
 * 			"sort_del" : Fichier delete from prd_categories_sort
 * 			"sort" :  Fichier replace into prd_categories_sort
 * 			"field_values_del" : Fichier delete from fld_object_values
 * 			"field_values" : Fichier replace into fld_object_values
 * 			"field_models_del" : Fichier delete from fld_object_models
 * 			"field_models" : Fichier replace into fld_object_models
 *       },
 *		\endcode
 *
 * Attention cette appel ne gère pas de json et renvoi une page blanche dans le cas d'une erreur ou pas de donnée
 *
 * Au niveau de Yuto, les fichiers sont traités toujours dans l'ordre suivant : le fichier "_del" et ensuite le fichier d'ajout / mise à jour.
 *
 * Aucun controle est fait du côté de yuto sur les données.
 *
 * Dans le cas d'ajout de colonne dans le schéma Sql il faut gérer les différences avec la variable $db_version.
 * En effet à chaque appel Yuto va donner le numéro de révision de sa base de donnée locale.
 */

use libphonenumber\PhoneNumberFormat;
use libphonenumber\PhoneNumberUtil;

// désactive certaines variables de restrictions sur le catalogue pour éviter certains soucis de mise à jour de produit
// (ex, le commercial n'a pas le droit d'avoir accès à un produit mais son client si)
$config['use_catalog_restrictions'] = 0;
$config['admin_catalog_hide_source_unpublished'] = 0;

$query_separator = '_$$$_';

switch( $method ){
	// récupère les élements à synchroniser
	case 'get':

		if( !isset($_GET['cls_id']) ){
			error_log("Le paramètre CLS_ID est manquant ou incorrect");
			exit;
		}
		$result = true;

		// chargement des données
		if( isset($_GET['seq']) ){
			$date_for_name=$_GET['seq'];
		}else{
			$date = isset($_GET['date']) && isdateheure($_GET['date']) ? $_GET['date'] : false;
			$date_for_name=$date;
		}

		if( !isset($date_for_name) || !$date_for_name ){
			$date_for_name = "none";
		}

		// on créer l'archive avec un nom temporaire et elle est renomer à la fin de l'ajout de tous les fichiers
		$db_version = isset($_GET['db']) && is_numeric($_GET['db']) ? $_GET['db'] : 1;

		$final_name = 'data-cls-'.$_GET['cls_id'].'-'.$config['tnt_id'].'-'.$config['dev_id'].'-'.$db_version.'-'.md5($date_for_name).'.zip';
		$tmp_name = 'tmp-'.$final_name;
		$zip_url = dirname(__FILE__).'/../upload-temp/'.$tmp_name;
		$zip_url_final = dirname(__FILE__).'/../upload-temp/'.$final_name;

		// dans le cas ou le fichier existe alors on le load pour l'envoyer directement dans ce cas la on le delete aussi enssuite.
		global $memcached;

		// Construit la clé memcached
		$ar_key = array(
			'fn' => 'api_sync_bulk_no_date',
			'url' => $zip_url_final
		);
		$mkey = implode( ':', $ar_key );
		$mget = $memcached->get( $mkey );

		if( $mget && $mget != '' && file_exists($zip_url_final) ){

			header("Content-type: application/octet-stream");
			header("Content-disposition: attachment; filename=data.zip");
			ob_clean();
			flush();
			readfile($zip_url_final);

			if( isset($date) ){
				dev_devices_tasks_set( $config['dev_id'], $_GET['cls_id'], date('Y-m-d H:i:s'), $date );
			}else{
				dev_devices_tasks_set( $config['dev_id'], $_GET['cls_id'], date('Y-m-d H:i:s'), false );
			}
			exit;

		}elseif( file_exists($zip_url_final) ){
			@unlink($zip_url_final);
		}

		// chargement des données
		if( isset($_GET['seq']) ){
			$data = dev_devices_get_sync_bulk($config['dev_id'],$_GET['cls_id'], $_GET['seq']);
		}else{
			$date = isset($_GET['date']) && isdateheure($_GET['date']) ? $_GET['date'] : false;
			$data = dev_devices_get_sync_bulk($config['dev_id'],$_GET['cls_id'], $date);
		}

		// récupère en premier lieu la liste des tous les objets embarqué par ce package
		// Cela permet de générer par la suite pour chaque donner liée les suppressions
		if( !isset($data['ids']) ){
			exit;
		}

		$filezip = array(); // liste des fichiers à créer
		parseData($filezip, $_GET['cls_id'], $data);

		$zip = new ZipArchive();
		if($zip->open($zip_url, ZIPARCHIVE::CREATE) == TRUE){
			foreach( $filezip as $file => $content ){
				$zip->addFromString($file, $content);
			}
			$zip->close();
		}

		@rename($zip_url, $zip_url_final);
		if( file_exists($zip_url_final) ){
			//envoi le zip
			header("Content-type: application/octet-stream");
			header("Content-disposition: attachment; filename=data.zip");
			ob_clean();
			flush();
			readfile($zip_url_final);

			$last_date = !empty($data['header']['last_date']) ? $data['header']['last_date'] : date('Y-m-d H:i:s');
			if( isset($date) ){
				dev_devices_tasks_set( $config['dev_id'], $_GET['cls_id'], $last_date, min($last_date, $date) );
			}else{
				dev_devices_tasks_set( $config['dev_id'], $_GET['cls_id'], $last_date, false );
			}

			if( !$mget && (!isset($_GET['date']) || $_GET['date'] == '') ){
				$memcached->set($mkey, true, 60*60); // 1 heure pour les requete sans date qui peuvent crash
			}elseif( !$mget ){
				$memcached->set($mkey, true, 60*10); // 10 minutes pour les requetes avec date
			}
			exit;
		}

		break;
}

function parseData(&$filezip, $cls, $data){
	global $config, $query_separator;

	$pas = 500;

	// les tablettes avec android 4.0 ( version 15 ) ne supporte pas les insert multiples
	if( $config['dev_os_version'] < 16 && $config['dev_brand']!='Apple' ){
		$pas = 1;
	}

	$db_version = isset($_GET['db']) && is_numeric($_GET['db']) ? $_GET['db'] : 1;

	if( isset($data['ids']) ){
		$all_obj_ids = $data['ids'];
		if( sizeof($all_obj_ids) <= 0 ){
			exit;
		}
		$filezip['ids.json'] = json_encode($all_obj_ids);
		unset($data['ids']);
	}
	if( isset($data['obj_del']) ){
		$filezip['ids_del.json'] = json_encode($data['obj_del']);
		//unset($data['ids_del']);
	}

	// pour chaque élément du tableau on ajoute un fichier
	foreach( $data as $k => $content ){
		if( $k == 'header' ){
			$filezip[$k.'.json'] = json_encode($content);
			continue;
		}
		if( isset($data['ids_del']) ){
			$filezip['ids_del.json'] = json_encode($data['ids_del']);
			unset($data['ids_del']);
		}

		$cols_del = array(); // TODO
		$cols = array(); // TODO -> value array escape, alias,
		$search_cols = array(); // TODO
		$vals = array(); // TODO
		$cpt=0; // TODO
		$sql_final = ""; // TODO
		$sql_final_search = ""; // TODO
		$is_searchable = false; // TODO
		$is_new_data = true; // TODO
		$is_id_alpha = false; // détermine si l'id est un alphanum
		$table_name = ""; // TODO
		$where_del = ""; // TODO
		$deleted = array(); // TODO
		$next = false; // TODO

		// en fonction de la classe les obj et les relateds sont différent
		switch($cls){
			case CLS_CATEGORY:
				switch($k){
					case 'obj_del':
						$table_name = 'prd_categories';
						$deleted = $content;
						$is_new_data = false;
						$cols_del = array('id' => 'cat_id');
						break;
					case 'obj':
						$table_name = 'prd_categories';
						$cols = array(
							'cat_id' => array('alias' => 'id'),
							'cat_name' => array('alias' => 'title', 'escape' => true ),
							'cat_desc' => array('alias' => 'desc', 'escape' => true ),
							'cat_parent_id' => array('alias' => 'parent_id', 'default'=>0),
							'cat_publish' => array('alias' => 'publish'),
							'cat_products' => array('alias' => 'products'),
							'cat_is_sync' => array('alias' => 'is_sync'),
							'cat_pos' => array('alias' => 'pos'),
							'cat_date_from' => array('alias' => 'date_from_en', 'escape' => true ),
							'cat_date_to' => array('alias' => 'date_to_en', 'escape' => true ),
							);
						break;
					case 'hierarchy':

						// parcours un premier coups les datas pour reformer le résultat
						$tmp = array();
						foreach( $content['obj'] as $o ){
							$tmp[] = array(
								'parent_id' => $o['is-parent'] ? $o['hry'] : $o['src'],
								'child_id' => $o['is-parent'] ? $o['src'] : $o['hry'],
								'depth' => $o['depth'],
								);
						}
						$content = $tmp;

						$table_name = 'prd_cat_hierarchy';
						$cols = array(
							'cat_parent_id' => array('alias' => 'parent_id'),
							'cat_child_id' => array('alias' => 'child_id'),
							'cat_parent_depth' => array('alias' => 'depth'),
							);
						$cols_del = array(
							'id' => 'cat_child_id'
							);
						break;
					case 'sort':
						$content = $content['obj'];
						$table_name = 'prd_categories_sort';
						$cols = array(
							'sort_cat_id' => array('alias' => 'cat_id'),
							'sort_type' => array('alias' => 'type'),
							'sort_dir' => array('alias' => 'dir', 'escape' => true ),
							'sort_is_recursive' => array('alias' => 'is_recursive'),
							);
						$cols_del = array(
							'id' => 'sort_cat_id'
							);
						break;
				}
				break;
			case CLS_PRODUCT:
				switch($k){
					case 'obj_del':
						$table_name = 'prd_products';
						$deleted = $content;
						$is_new_data = false;
						$is_searchable = true;
						$cols_del = array('id' => 'prd_id');
						break;
					case 'obj':
						$table_name = 'prd_products';
						$cols = array(
							'prd_id' => array('alias' => 'id'),
							'prd_ref' => array('alias' => 'ref', 'escape' => true ),
							'prd_name' => array('alias' => 'name', 'escape' => true ),
							'prd_title' => array('alias' => 'title', 'escape' => true ),
							'prd_desc' => array('alias' => 'desc', 'escape' => true ),
							'prd_desc_long' => array('alias' => 'desc-long', 'escape' => true ),
							'prd_publish' => array('alias' => 'publish'),
							'prd_publish_cat' => array('alias' => 'publish_cat'),
							'prd_date_published' => array('alias' => 'date_published', 'escape' => true, 'func' => 'dateheureparse' ),
							'prd_brd_id' => array('alias' => 'brd_id'),
							'prd_weight' => array('alias' => 'weight'),
							'prd_weight_net' => array('alias' => 'weight_net'),
							'prd_length' => array('alias' => 'length'),
							'prd_width' => array('alias' => 'width'),
							'prd_height' => array('alias' => 'height'),
							'prd_garantie' => array('alias' => 'garantie'),
							'prd_stock_livr' => array('alias' => 'stock_livr', 'escape' => true, 'func' => 'dateheureparse' ),
							'prd_date_created' => array('alias' => 'date_created', 'escape' => true, 'func' => 'dateheureparse' ),
							'prd_img_id' => array('alias' => 'img_id'),
							'prd_orderable' => array('alias' => 'orderable', 'default'=> 0),
							'prd_countermark' => array('alias' => 'countermark'),
							'prd_childonly' => array('alias' => 'childonly'),
							'prd_new' => array('alias' => 'new_rule', 'default'=> 0),
							'prd_sleep' => array('alias' => 'sleep'),
							'prd_taxcode' => array('alias' => 'taxcode', 'escape' => true),
							'prd_barcode' => array('alias' => 'barcode', 'escape' => true),
							'prd_selled' => array('alias' => 'selled'),
							'prd_purchase_avg' => array('alias' => 'purchase_avg'),
							'prd_nomenclature_type' => array('alias' => 'nomenclature_type'),
							'prd_sell_weight' => array('alias' => 'sell_weight'),
							'prd_ecotaxe' => array('alias' => 'ecotaxe'),
							'prd_selled_web' => array('alias' => 'selled_web'),
							'prd_follow_stock' => array('alias' => 'follow_stock'),
							'prd_sun_id' => array('alias' => 'sun_id'),
							);
						$search_cols = array(
							'sc_cls_id' => CLS_PRODUCT,
							'sc_obj_id_0' => 'id',
							'sc_obj_id_1' => 0,
							'sc_obj_id_2' => 0,
							'sc_ref' => 'ref',
							'sc_text_0' => 'name',
							'sc_text_1' => 'title',
							'sc_text_2' => 'brd_title',
							'sc_text_3' => 'desc',
							'sc_text_4' => 'desc-long',
							);
						if( $db_version >= 354 ){
							$search_cols['sc_ref_1'] = 'barcode';
						}
						break;
					case 'classify':
						$content = $content['obj'];
						$table_name = 'prd_classify';
						$cols = array(
							'cly_cat_id' => array('alias' => 'cat'),
							'cly_prd_id' => array('alias' => 'prd'),
							'cly_prd_pos' => array('alias' => 'pos'),
							);
						$cols_del = array(
							'id' => 'cly_prd_id'
							);
						break;
					case 'colisage':
						$table_name = 'prd_colisage_classify';
						parseData($filezip, CLS_PRD_COLISAGE, $content);
						$next = true;
						$cols_del = array(
							'id' => 'cly_prd_id'
							);
						break;
					case 'child':
						$content = $content['obj'];
						$table_name = 'prd_hierarchy';
						$cols = array(
							'prd_parent_id' => array('alias' => 'parent'),
							'prd_child_id' => array('alias' => 'child'),
							'prd_child_pos' => array('alias' => 'pos'),
							);
						$cols_del = array(
							'id' => 'prd_child_id'
							);
						break;
					case 'parent':
						$content = $content['obj'];
						$table_name = 'prd_hierarchy';
						$cols = array(
							'prd_parent_id' => array('alias' => 'parent'),
							'prd_child_id' => array('alias' => 'child'),
							'prd_child_pos' => array('alias' => 'pos'),
							);
						$cols_del = array(
							'id' => 'prd_parent_id'
							);
						break;
					case 'image':
						$content = $content['obj'];
						$table_name = 'prd_images';
						$cols = array(
							'img_id' => array('alias' => 'id'),
							'img_prd_id' => array('alias' => 'prd_id'),
							'img_pos' => array('alias' => 'pos'),
							);
						$cols_del = array(
							'id' => 'img_prd_id'
							);
						break;
					case 'prd-nomen':
						$content = $content['obj'];
						$table_name = 'prd_nomenclatures_products';
						$cols = array(
							'pnp_parent_id' => array('alias' => 'prd_id'),
							'pnp_child_id' => array('alias' => 'id'),
							'pnp_qte' => array('alias' => 'qte'),
							'pnp_price_ht' => array('alias' => 'price_ht'),
							);
						$cols_del = array(
							'id' => 'pnp_parent_id'
							);
						break;
					case 'options':
						$table_name = 'prd_nomenclatures_options';
						parseData($filezip, CLS_PRD_NOM_OPTION, $content);
						$next = true;
						$cols_del = array(
							'id' => 'prd_id'
							);
						break;
					case 'srv-unavailable':
						$content = $content['obj'];
						$table_name = 'prd_services_unavailable';
						$cols = array(
							'psu_prd_id' => array('alias' => 'prd_id'),
							'psu_srv_id' => array('alias' => 'srv_id'),
							);
						$cols_del = array(
							'id'	=> 'psu_prd_id'
							);
						break;
					case 'serials':
						$table_name = 'obj_serials';
						parseData($filezip, CLS_SERIALS, $content);
						$next = true;
						$cols_del = array(
							'id' => 'osl_obj_id_0'
							);
						$where_del = "osl_cls_id=".CLS_PRODUCT;
						break;
					case 'sto-schedule':
						$content = $content['obj'];
						$table_name = 'prd_stocks_schedule';
						$cols = array(
							'pss_id' => array('alias' => 'id'),
							'pss_prd_id' => array('alias' => 'prd_id'),
							'pss_date' =>  array('alias' => 'date', 'escape' => true),
							'pss_qte' => array('alias' => 'qte'),
							'pss_confirmed' => array('alias' => 'confirmed'),
							);
						$cols_del = array(
							'id' => 'pss_prd_id'
							);
						break;
				}
				break;
			case CLS_PRD_NOM_OPTION:
				switch($k){
					case 'obj':
						$k = 'options';
						$table_name = 'prd_nomenclatures_options';
						$cols = array(
							'prd_id' => array('alias' => 'id'),
							'prd_opt_id' => array('alias' => 'opt'),
							'prd_opt_qte' => array('alias' => 'qte'),
							'prd_opt_free' => array('alias' => 'free'),
							);
						break;
					case 'opt-name':
						$content = $content['obj'];
						$table_name = 'prd_options';
						$cols = array(
							'opt_id' => array('alias' => 'id'),
							'opt_name' => array('alias' => 'name', 'escape' => true),
							);
						break;
					case 'opt-prd':
						$content = $content['obj'];

						// a multualisé idéalement...
						$all_obj_ids = array();
						foreach($content as $o ){
							$all_obj_ids[] = array($o['prd']);
						}

						$table_name = 'prd_options_products';
						$cols = array(
							'opt_id' => array('alias' => 'opt'),
							'prd_id' => array('alias' => 'prd')
							);
						$cols_del = array(
							'id' => 'prd_id'
							);
						break;
				}
				break;
			case CLS_PRD_COLISAGE:
				switch($k){
					case 'obj':
						$k = 'colisage';
						$table_name = 'prd_colisage_classify';
						$cols = array(
							'cly_col_id' => array('alias' => 'col_id'),
							'cly_prd_id' => array('alias' => 'prd_id'),
							'cly_is_default' => array('alias' => 'is_default'),
							'cly_prd_ref' => array('alias' => 'prd_ref', 'escape' => true),
							'cly_prd_barcode' => array('alias' => 'prd_barcode', 'escape' => true),
							);
						break;
				}
				break;
			case CLS_PRICE:
				switch($k){
					case 'obj_del':
						$table_name = 'prc_prices';
						$deleted = $content;
						$is_new_data = false;
						$cols_del = array('id' => 'prc_id');
						break;
					case 'obj':
						$table_name = 'prc_prices';
						$cols = array(
							'prc_id' => array('alias' => 'id'),
							'prc_prd_id' => array('alias' => 'prd'),
							'prc_cat_id' => array('alias' => 'cat'),
							'prc_type_id' => array('alias' => 'type'),
							'prc_date_start' => array('alias' => 'date-start', 'escape' => true),
							'prc_date_end' => array('alias' => 'date-end', 'escape' => true),
							'prc_is_cumuled' => array('alias' => 'is-cumuled'),
							'prc_is_sync' => array('alias' => 'is-sync'),
							'prc_value' => array('alias' => 'value'),
							'prc_qte_min' => array('alias' => 'qte-min'),
							'prc_qte_max' => array('alias' => 'qte-max'),
							'prc_is_promotion' => array('alias' => 'is-promotion'),
							'prc_grp_id' => array('alias' => 'grp_id'),
							);

						if( $db_version >= 4973 ){
							$cols['prc_kind'] = array('alias' => 'kind');
							$cols['prc_priority'] = array('alias' => 'priority');
							$cols['prc_name'] = array('alias' => 'name', 'escape' => true);
						}
						break;
					case 'conditions':
						$content = $content['obj'];
						$table_name = 'prc_price_conditions';
						$cols = array(
							'ppc_fld_id' => array('alias' => 'fld'),
							'ppc_prc_id' => array('alias' => 'price'),
							'ppc_value' => array('alias' => 'value', 'escape' => true),
							'ppc_symbol' => array('alias' => 'symbol', 'escape' => true),
							);
						$cols_del = array(
							'id' => 'ppc_prc_id'
							);
						break;
				}
				break;
			case CLS_TVA:
				switch($k){
					case 'obj_del':
						$table_name = 'prc_tvas';
						$deleted = $content;
						$is_new_data = false;
						$cols_del = array('id' => 'ptv_id');
						break;
					case 'obj':
						$table_name = 'prc_tvas';
						$cols = array(
							'ptv_id' => array('alias' => 'id'),
							'ptv_tva_rate' => array('alias' => 'rate'),
							'ptv_prd_id' => array('alias' => 'prd'),
							'ptv_cac_id' => array('alias' => 'cac'),
							);
						if( $db_version >= 352 ){
							$cols['ptv_cnt_code'] = array('alias' => 'cnt_code', 'escape' => true);
						}
						break;
				}
				break;
			case CLS_STOCK:
				switch($k){
					case 'obj_del':
						$table_name = 'prd_stocks';
						$deleted = $content;
						$is_new_data = false;
						$cols_del = array('id' => 'sto_prd_id','id_1' => 'sto_dps_id');
						break;
					case 'obj':
						$table_name = 'prd_stocks';
						$cols = array(
							'sto_prd_id' => array('alias' => 'prd'),
							'sto_dps_id' => array('alias' => 'dps_id'),
							'sto_qte' => array('alias' => 'qte'),
							'sto_com' => array('alias' => 'com'),
							'sto_res' => array('alias' => 'res'),
							'sto_prepa' => array('alias' => 'prepa'),
							'sto_mini' => array('alias' => 'mini'),
							'sto_maxi' => array('alias' => 'maxi'),
							);
						break;
				}
				break;
			case CLS_PRD_RELATIONS:

				switch($k){
					case 'obj_del':
						$table_name = 'prd_relations';
						$deleted = $content;
						$is_new_data = false;
						$cols_del = array('id' => 'rel_src_id','id_1' => 'rel_dst_id','id_2' => 'rel_type_id');
						break;
					case 'obj':
						$table_name = 'prd_relations';
						$cols = array(
							'rel_src_id' => array('alias' => 'src_id'),
							'rel_dst_id' => array('alias' => 'dst_id'),
							'rel_type_id' => array('alias' => 'type_id'),
							'rel_pos' => array('alias' => 'pos'),
							);
						break;
				}
				break;
			case CLS_INVOICE:
				switch($k){
					case 'obj_del':
						$table_name = 'ord_invoices';
						$deleted = $content;
						$is_new_data = false;
						$cols_del = array('id' => 'inv_id');
						break;
					case 'obj':
						$table_name = 'ord_invoices';
						$cols = array(
							'inv_id' => array('alias' => 'id'),
							'inv_usr_id' => array('alias' => 'usr_id'),
							'inv_total_ht' => array('alias' => 'total_ht'),
							'inv_total_ttc' => array('alias' => 'total_ttc'),
							'inv_piece' => array('alias' => 'piece', 'escape' => true),
							'inv_ref' => array('alias' => 'ref', 'escape' => true),
							'inv_date' => array('alias' => 'date_en', 'escape' => true),
							'inv_date_modified' => array('alias' => 'date_modified', 'escape' => true),
							'inv_discount' => array('alias' => 'discount'),
							);
						break;
					case 'products':
						$table_name = 'ord_inv_products';
						parseData($filezip, CLS_INV_PRODUCT, $content);
						$next = true;
						$cols_del = array(
							'id' => 'prd_inv_id'
							);
						break;
					case 'serials':
						$table_name = 'obj_serials';
						parseData($filezip, CLS_SERIALS, $content);
						$next = true;
						$cols_del = array(
							'id' => 'osl_obj_id_0'
							);
						$where_del = "osl_cls_id=".CLS_INV_PRODUCT;
						break;
				}
				break;
			case CLS_INV_PRODUCT:
				switch($k){
					case 'obj':
						$k = 'products';
						$table_name = 'ord_inv_products';
						$cols = array(
							'prd_inv_id' => array('alias' => 'inv_id'),
							'prd_id' => array('alias' => 'id'),
							'prd_line_id' => array('alias' => 'line'),
							'prd_qte' => array('alias' => 'qte'),
							'prd_ref' => array('alias' => 'ref', 'escape' => true),
							'prd_name' => array('alias' => 'name', 'escape' => true),
							'prd_price_ht' => array('alias' => 'price_ht'),
							'prd_tva_rate' => array('alias' => 'tva_rate'),
							'prd_price_ttc' => array('alias' => 'price_ttc'),
							'prd_ord_id' => array('alias' => 'ord_id'),
							'prd_ecotaxe' => array('alias' => 'ecotaxe'),
							);

						if( $db_version >= 244 ){
							$cols['prd_purchase_avg'] = array('alias' => 'purchase_avg');
							$cols['prd_seller_id'] = array('alias' => 'seller_id');
							$cols['prd_pos'] = array('alias' => 'pos');
							$cols['prd_weight'] = array('alias' => 'weight_net');
							$cols['prd_price_brut_ht'] = array('alias' => 'price_brut_ht');
						}
						if( $db_version >= 4960 ){
							$cols['prd_group_id'] = array('alias' => 'group_id');
							$cols['prd_group_parent_id'] = array('alias' => 'group_parent_id');
						}
						break;
				}
				break;
			case CLS_PL:
				switch($k){
					case 'obj_del':
						$table_name = 'ord_pl';
						$deleted = $content;
						$is_new_data = false;
						$cols_del = array('id' => 'pl_id');
						break;
					case 'obj':
						$table_name = 'ord_pl';
						$cols = array(
							'pl_id' => array('alias' => 'id'),
							'pl_usr_id' => array('alias' => 'usr_id'),
							'pl_total_ht' => array('alias' => 'total_ht'),
							'pl_total_ttc' => array('alias' => 'total_ttc'),
							'pl_piece' => array('alias' => 'piece', 'escape' => true),
							'pl_ref' => array('alias' => 'ref', 'escape' => true),
							'pl_date' => array('alias' => 'date_en', 'escape' => true),
							);
						break;
					case 'products':
						$table_name = 'ord_pl_products';
						parseData($filezip, CLS_PL_PRODUCT, $content);
						$next = true;
						$cols_del = array(
							'id' => 'prd_pl_id'
							);
						break;
					case 'serials':
						$table_name = 'obj_serials';
						parseData($filezip, CLS_SERIALS, $content);
						$next = true;
						$cols_del = array(
							'id' => 'osl_obj_id_0'
							);
						$where_del = "osl_cls_id=".CLS_PL_PRODUCT;
						break;
				}
				break;
			case CLS_PL_PRODUCT:
				switch($k){
					case 'obj':
						$k = 'products';
						$table_name = 'ord_pl_products';
						$cols = array(
							'prd_pl_id' => array('alias' => 'pl_id'),
							'prd_id' => array('alias' => 'id'),
							'prd_line_id' => array('alias' => 'line'),
							'prd_qte' => array('alias' => 'qte'),
							'prd_ref' => array('alias' => 'ref', 'escape' => true),
							'prd_name' => array('alias' => 'name', 'escape' => true),
							'prd_price_ht' => array('alias' => 'price_ht'),
							'prd_tva_rate' => array('alias' => 'tva_rate'),
							'prd_price_ttc' => array('alias' => 'price_ttc'),
							'prd_ord_id' => array('alias' => 'ord_id'),
							'prd_ecotaxe' => array('alias' => 'ecotaxe'),
							);
						if( $db_version >= 4960 ){
							$cols['prd_group_id'] = array('alias' => 'group_id');
							$cols['prd_group_parent_id'] = array('alias' => 'group_parent_id');
						}
						break;
				}
				break;
			case CLS_BL:
				switch($k){
					case 'obj_del':
						$table_name = 'ord_bl';
						$deleted = $content;
						$is_new_data = false;
						$cols_del = array('id' => 'bl_id');
						break;
					case 'obj':
						$table_name = 'ord_bl';
						$cols = array(
							'bl_id' => array('alias' => 'id'),
							'bl_usr_id' => array('alias' => 'usr_id'),
							'bl_total_ht' => array('alias' => 'total_ht'),
							'bl_total_ttc' => array('alias' => 'total_ttc'),
							'bl_piece' => array('alias' => 'piece', 'escape' => true),
							'bl_ref' => array('alias' => 'ref', 'escape' => true),
							'bl_date' => array('alias' => 'date_en', 'escape' => true),
							'bl_state_id' => array('alias' => 'state_id'),
							'bl_srv_id' => array('alias' => 'srv_id')
							);
						if( $db_version >= 4910 ){
							$cols['bl_adr_invoices'] = array('alias' => 'adr_invoices');
							$cols['bl_adr_dlv_id'] = array('alias' => 'adr_dlv_id');

							$cols['bl_products'] = array('alias' => 'products');
							$cols['bl_need_sync'] = array('alias' => 'need_sync');
							$cols['bl_str_id'] = array('alias' => 'str_id');
							$cols['bl_pkg_id'] = array('alias' => 'pkg_id');
							$cols['bl_pay_id'] = array('alias' => 'pay_id');
							$cols['bl_pmt_id'] = array('alias' => 'pmt_id');
							$cols['bl_dps_id'] = array('alias' => 'dps_id');
							$cols['bl_wst_id'] = array('alias' => 'wst_id');
							$cols['bl_reseller_id'] = array('alias' => 'reseller_id');
							$cols['bl_reseller_contact_id'] = array('alias' => 'reseller_contact_id');
							$cols['bl_contact_id'] = array('alias' => 'contact_id');
							$cols['bl_seller_id'] = array('alias' => 'seller_id');
							$cols['bl_date_livr'] = array('alias' => 'date_livr', 'escape' => true);
							$cols['bl_dlv_notes'] = array('alias' => 'dlv_notes', 'escape' => true);
							$cols['bl_comments'] = array('alias' => 'comments', 'escape' => true);
						}
						break;
					case 'products':
						$table_name = 'ord_bl_products';
						parseData($filezip, CLS_BL_PRODUCT, $content);
						$next = true;
						$cols_del = array(
							'id' => 'prd_bl_id'
							);
						break;
					case 'serials':
						$table_name = 'obj_serials';
						parseData($filezip, CLS_SERIALS, $content);
						$next = true;
						$cols_del = array(
							'id' => 'osl_obj_id_0'
							);
						$where_del = "osl_cls_id=".CLS_BL_PRODUCT;
						break;
					case 'signature':
						$table_name = 'obj_signatures';
						parseData($filezip, CLS_SIGNATURES, $content);
						$next = true;
						$cols_del = array(
							'id' => 'sig_obj_id_0'
							);
						$where_del = "sig_cls_id = ".CLS_BL;
						break;

				}
				break;
			case CLS_SIGNATURES:
				switch($k){
					case 'obj':
						$k = 'signature';
						$table_name = 'obj_signatures';
						$cols = array(
							'sig_id' => array('alias' => 'id'),
							'sig_cls_id' => array('alias' => 'cls_id'),
							'sig_obj_id_0' => array('alias' => 'obj_id_0'),
							'sig_obj_id_1' => array('alias' => 'obj_id_1'),
							'sig_obj_id_2' => array('alias' => 'obj_id_2'),
							'sig_signature' => array('alias' => 'signature', 'escape' => true),
							'sig_usr_id' => array('alias' => 'usr_id'),
							'sig_firstname' => array('alias' => 'firstname', 'escape' => true),
							'sig_lastname' => array('alias' => 'lastname', 'escape' => true),
							'sig_function' => array('alias' => 'function', 'escape' => true),
							);
						break;
				}
				break;
			case CLS_BL_PRODUCT:
				switch($k){
					case 'obj':
						$k = 'products';
						$table_name = 'ord_bl_products';
						$cols = array(
							'prd_bl_id' => array('alias' => 'bl_id'),
							'prd_id' => array('alias' => 'id'),
							'prd_line_id' => array('alias' => 'line'),
							'prd_qte' => array('alias' => 'qte'),
							'prd_ref' => array('alias' => 'ref', 'escape' => true),
							'prd_name' => array('alias' => 'name', 'escape' => true),
							'prd_price_ht' => array('alias' => 'price_ht'),
							'prd_tva_rate' => array('alias' => 'tva_rate'),
							'prd_price_ttc' => array('alias' => 'price_ttc'),
							'prd_ord_id' => array('alias' => 'ord_id'),
							'prd_ecotaxe' => array('alias' => 'ecotaxe'),
							);
						if( $db_version >= 4910 ){
							$cols['prd_colis'] = array('alias' => 'colis', 'escape' => true);
							$cols['prd_date_livr'] = array('alias' => 'date_livr', 'escape' => true);
							$cols['prd_notes'] = array('alias' => 'notes', 'escape' => true);
							$cols['prd_date_created'] = array('alias' => 'date_created', 'escape' => true);
							$cols['prd_parent_id'] = array('alias' => 'parent_id');
							$cols['prd_childs_line_id'] = array('alias' => 'childs_line_id');
							$cols['prd_cod_id'] = array('alias' => 'cod_id');
							$cols['prd_purchase_avg'] = array('alias' => 'purchase_avg');
							$cols['prd_pos'] = array('alias' => 'pos');
							$cols['prd_col_id'] = array('alias' => 'col_id');
						}
						if( $db_version >= 4960 ){
							$cols['prd_group_id'] = array('alias' => 'group_id');
							$cols['prd_group_parent_id'] = array('alias' => 'group_parent_id');
						}
						break;
				}
				break;
			case CLS_RETURN:
				switch($k){
					case 'obj_del':
						$table_name = 'ord_returns';
						$deleted = $content;
						$is_new_data = false;
						$cols_del = array('id' => 'return_id');
						break;
					case 'obj':
						$table_name = 'ord_returns';
						$cols = array(
							'return_id' => array('alias' => 'id'),
							'return_user_id' => array('alias' => 'user_id'),
							'return_total_ht' => array('alias' => 'total_ht'),
							'return_total_ttc' => array('alias' => 'total_ttc'),
							'return_piece' => array('alias' => 'piece', 'escape' => true),
							'return_ref' => array('alias' => 'ref', 'escape' => true),
							'return_date' => array('alias' => 'date', 'escape' => true),
							'return_adr_invoices' => array('alias' => 'adr_invoices'),
							'return_adr_delivery' => array('alias' => 'adr_delivery'),
							'return_states_id' => array('alias' => 'states_id'),
							'return_ord_id' => array('alias' => 'ord_id'),
							'return_prepaid_label' => array('alias' => 'prepaid_label', 'escape' => true),
							'return_products' => array('alias' => 'products'),
							'return_need_sync' => array('alias' => 'need_sync'),
							'return_srv_id' => array('alias' => 'srv_id'),
							'return_str_id' => array('alias' => 'str_id'),
							'return_pkg_id' => array('alias' => 'pkg_id'),
							'return_dlv_notes' => array('alias' => 'dlv_notes' , 'escape' => true),
							'return_pay_id' => array('alias' => 'pay_id'),
							'return_pmt_id' => array('alias' => 'pmt_id'),
							'return_seller_id' => array('alias' => 'seller_id'),
							'return_dps_id' => array('alias' => 'dps_id'),
							'return_wst_id' => array('alias' => 'wst_id'),
							'return_contact_id' => array('alias' => 'contact_id'),
							'return_reseller_id' => array('alias' => 'reseller_id'),
							'return_reseller_contact_id' => array('alias' => 'reseller_contact_id')
							);
						break;
					case 'products':
						$table_name = 'ord_returns_products';
						parseData($filezip, CLS_RETURN_PRODUCTS, $content);
						$next = true;
						$cols_del = array(
							'id' => 'prd_return_id'
							);
						break;
					case 'serials':
						$table_name = 'obj_serials';
						parseData($filezip, CLS_SERIALS, $content);
						$next = true;
						$cols_del = array(
							'id' => 'osl_obj_id_0'
							);
						$where_del = "osl_cls_id=".CLS_RETURN_PRODUCTS;
						break;
					case 'signature':
						$table_name = 'obj_signatures';
						parseData($filezip, CLS_SIGNATURES, $content);
						$next = true;
						$cols_del = array(
							'id' => 'sig_obj_id_0'
							);
						$where_del = "sig_cls_id = ".CLS_RETURN;
						break;
				}
				break;
			case CLS_RETURN_PRODUCTS:
				switch($k){
					case 'obj':
						$k = 'products';
						$table_name = 'ord_returns_products';
						$cols = array(
							'prd_return_id' => array('alias' => 'ret'),
							'prd_id' => array('alias' => 'id'),
							'prd_line_id' => array('alias' => 'line_id'),
							'prd_qte' => array('alias' => 'qte'),
							'prd_ref' => array('alias' => 'ref', 'escape' => true),
							'prd_name' => array('alias' => 'name', 'escape' => true),
							'prd_price_ht' => array('alias' => 'price_ht'),
							'prd_tva_rate' => array('alias' => 'tva_rate'),
							'prd_price_ttc' => array('alias' => 'price_ttc'),
							'prd_mode_id' => array('alias' => 'mode'),
							'prd_reason_id' => array('alias' => 'reason'),
							'prd_reason_detail' => array('alias' => 'details', 'escape' => true),
							'prd_state_id' => array('alias' => 'state'),
							'prd_lot' => array('alias' => 'lot'),
							'prd_dlc' => array('alias' => 'dlc'),
							'prd_garanty' => array('alias' => 'garanty'),
							'prd_motif' => array('alias' => 'motif', 'escape' => true),
							'prd_ord_id' => array('alias' => 'ord_id'),
							'prd_cod_id' => array('alias' => 'cod_id'),
							'prd_ecotaxe' => array('alias' => 'ecotaxe'),
							'prd_date_livr' => array('alias' => 'date_livr'),
							'prd_notes' => array('alias' => 'notes', 'escape' => true),
							'prd_date_created' => array('alias' => 'date_created', 'escape' => true),
							'prd_parent_id' => array('alias' => 'parent_id'),
							'prd_childs_line_id' => array('alias' => 'childs_line_id'),
							'prd_purchase_avg' => array('alias' => 'purchase_avg'),
							'prd_pos' => array('alias' => 'pos'),
							'prd_col_id' => array('alias' => 'col_id')
							);
						if( $db_version >= 4960 ){
							$cols['prd_group_id'] = array('alias' => 'group_id');
							$cols['prd_group_parent_id'] = array('alias' => 'group_parent_id');
						}
						break;
				}
				break;
			case CLS_ORDER:
				switch($k){
					case 'obj_del':
						$table_name = 'ord_orders';
						$deleted = $content;
						$is_new_data = false;
						$is_searchable = true;
						$cols_del = array('id' => 'ord_id');
						break;
					case 'obj':
						$table_name = 'ord_orders';
						$cols = array(
							'ord_id' => array('alias' => 'id'),
							'ord_date' => array('alias' => 'date_en', 'escape' => true),
							'ord_products' => array('alias' => 'products'),
							'ord_total_ht' => array('alias' => 'total_ht'),
							'ord_total_ttc' => array('alias' => 'total_ttc'),
							'ord_adr_invoices' => array('alias' => 'adr_invoices'),
							'ord_adr_delivery' => array('alias' => 'adr_delivery'),
							'ord_state_id' => array('alias' => 'state_id'),
							'ord_piece' => array('alias' => 'piece', 'escape' => true),
							'ord_ref' => array('alias' => 'ref', 'escape' => true),
							'ord_date_livr' => array('alias' => 'ord_livr', 'escape' => true),
							'ord_usr_id' => array('alias' => 'user'),
							'ord_srv_id' => array('alias' => 'srv_id'),
							'ord_dlv_notes' => array('alias' => 'dlv-notes', 'escape' => true),
							'ord_comments' => array('alias' => 'comments', 'escape' => true),
							'ord_need_sync' => array('alias' => 'nsync'),
							'ord_pay_id' => array('alias' => 'pay_id'),
							'ord_pmt_id' => array('alias' => 'pmt_id'),
							'ord_seller_id' => array('alias' => 'seller_id'),
							'ord_date_archived' => array('alias' => 'date_archived', 'escape' => true),
							'ord_wst_id' => array('alias' => 'wst_id'),
							);
						$search_cols = array(
							'sc_cls_id' => CLS_ORDER,
							'sc_obj_id_0' => 'id',
							'sc_obj_id_1' => 0,
							'sc_obj_id_2' => 0,
							'sc_ref' => 'piece',
							'sc_text_0' => 'ref',
							'sc_text_1' => 'id',
							'sc_text_2' => '',
							'sc_text_3' => '',
							'sc_text_4' => '',
							);

						if( $db_version >= 290 ){
							$cols['ord_date_modified'] = array('alias' => 'date_modified', 'escape' => true);
						}
						if( $db_version >= 293 ){
							$cols['ord_str_id'] = array('alias' => 'str_id');
						}
						if( $db_version >= 295 ){
							$cols['ord_contact_id'] = array('alias' => 'contact_id');
						}
						if( $db_version >= 319 ){
							$cols['ord_dps_id'] = array('alias' => 'dps_id');
						}
						if( $db_version >= 351 ){
							$cols['ord_pkg_id'] = array('alias' => 'pkg_id');
						}

						if( $db_version >= 4905 ){
							$cols['ord_reseller_id'] = array('alias' => 'reseller_id');
							$cols['ord_reseller_contact_id'] = array('alias' => 'reseller_contact_id');
						}
						if( $db_version >= 4909 ){
							$cols['ord_currency'] = array('alias' => 'currency', 'escape' => true);
						}

						if( $db_version >= 5010 ){
							$search_cols['sc_ref'] = 'piece';
							$search_cols['sc_ref_1'] = 'ref';
							$search_cols['sc_ref_2'] = '';
							$search_cols['sc_text_0'] = 'id';
							$search_cols['sc_text_1'] = '';
							$search_cols['sc_text_2'] = '';
							$search_cols['sc_text_3'] = '';
							$search_cols['sc_text_4'] = '';
							$search_cols['sc_text_5'] = '';
						}

						break;
					case 'products':
						$table_name = 'ord_products';
						parseData($filezip, CLS_ORD_PRODUCT, $content);
						$next = true;
						$cols_del = array(
							'id' => 'prd_ord_id'
							);
						break;
					case 'signature':
						$content = $content['obj'];
						$table_name = 'ord_orders_signature';
						$cols = array(
							'sig_ord_id' => array('alias' => 'ord_id'),
							'sig_signature' => array('alias' => 'signature', 'escape' => true),
							'sig_usr_id' => array('alias' => 'usr_id', 'escape' => true),
							'sig_firstname' => array('alias' => 'firstname', 'escape' => true),
							'sig_lastname' => array('alias' => 'lastname', 'escape' => true),
							'sig_function' => array('alias' => 'function', 'escape' => true),
							);
						$cols_del = array(
							'id' => 'sig_ord_id'
							);
						break;
					case 'model_users':
						$content = $content['obj'];
						$table_name = 'ord_model_users';
						$cols = array(
							'omu_id' => array('alias' => 'id'),
							'omu_usr_id' => array('alias' => 'usr_id'),
							'omu_prc_id' => array('alias' => 'prc_id'),
							'omu_cnt_code' => array('alias' => 'cnt_code', 'escape' => true),
							'omu_cac_id' => array('alias' => 'cac_id'),
							'omu_prf_id' => array('alias' => 'prf_id'),
							'omu_ord_id' => array('alias' => 'ord_id'),
							'omu_is_allowed' => array('alias' => 'is_allowed'),
							);

						if( $db_version >= 4815 ){
							$cols['omu_fld_value'] = array('alias' => 'fld_value', 'escape' => true);
							$cols['omu_fld_id'] = array('alias' => 'fld_id');
						}
						$cols_del = array(
							'id' => 'omu_ord_id'
							);
						break;
					case 'models_position':
						$content = $content['obj'];
						$table_name = 'ord_models_position';
						$cols = array(
							'omd_ord_id' => array('alias' => 'ord_id'),
							'omd_pos' => array('alias' => 'pos')
							);
						$cols_del = array(
							'id' => 'omd_ord_id'
							);
						break;
					case 'states':
						$content = $content['obj'];
						$table_name = 'ord_orders_states';
						$cols = array(
							'oos_ord_id' => array('alias' => 'ord_id'),
							'oos_state_id' => array('alias' => 'state_id'),
							'oos_datetime' => array('alias' => 'date', 'escape' => true),
							'oos_usr_id' => array('alias' => 'usr_id')
							);
						$cols_del = array(
							'id' => 'oos_ord_id'
							);
						break;
					case 'installments':
						$content = $content['obj'];
						$table_name = 'ord_installments';
						$cols = array(
							'itm_id' => array('alias' => 'id', 'escape' => true),
							'itm_is_deadline' => array('alias' => 'is_deadline'),
							'itm_type_id' => array('alias' => 'type'),
							'itm_piece_id' => array('alias' => 'piece'),
							'itm_pay_id' => array('alias' => 'pay'),
							'itm_amount_total' => array('alias' => 'total'),
							'itm_amount_rest' => array('alias' => 'rest'),
							'itm_transaction_id' => array('alias' => 'transaction'),
							'itm_date_created' => array('alias' => 'date', 'escape' => true),
							'itm_date_expired' => array('alias' => 'expire', 'escape' => true),
							'itm_state' => array('alias' => 'state'),
							);

						if( $db_version >= 470 ){
							$cols['itm_amount_percent'] = array('alias' => 'amount_percent');
						}
						$cols_del = array(
							'id' => 'itm_piece_id'
							);
						$where_del = "itm_type_id=1";
						break;
					case 'serials':
						$table_name = 'obj_serials';
						parseData($filezip, CLS_SERIALS, $content);
						$next = true;
						$cols_del = array(
							'id' => 'osl_obj_id_0'
							);
						$where_del = "osl_cls_id=".CLS_ORD_PRODUCT;
						break;
				}
				break;
			case CLS_ORD_PRODUCT:
				switch($k){
					case 'obj':
						$k = 'products';
						$table_name = 'ord_products';
						$cols = array(
							'prd_ord_id' => array('alias' => 'ord_id'),
							'prd_id' => array('alias' => 'id'),
							'prd_line_id' => array('alias' => 'line'),
							'prd_qte' => array('alias' => 'qte'),
							'prd_ref' => array('alias' => 'ref', 'escape' => true),
							'prd_name' => array('alias' => 'name', 'escape' => true),
							'prd_price_ht' => array('alias' => 'price_ht'),
							'prd_tva_rate' => array('alias' => 'tva_rate'),
							'prd_price_ttc' => array('alias' => 'price_ttc'),
							'prd_ecotaxe' => array('alias' => 'ecotaxe'),
							'prd_date_livr' => array('alias' => 'date_livr_en', 'escape' => true),
							'prd_notes' => array('alias' => 'notes', 'escape' => true),
							'prd_date_created' => array('alias' => 'date_created', 'escape' => true),
							'prd_parent_id' => array('alias' => 'parent'),
							'prd_childs_line_id' => array('alias' => 'child-line'),
							'prd_cod_id' => array('alias' => 'cod'),
							);

						if( $db_version >= 293 ){
							$cols['prd_pos'] = array('alias' => 'prd_pos');
						}
						if( $db_version >= 448 ){
							$cols['prd_col_id'] = array('alias' => 'col_id');
						}
						if( $db_version >= 4914 ){
							$cols['prd_line_is_sync'] = array('alias' => 'line_is_sync');
						}
						if( $db_version >= 4960 ){
							$cols['prd_group_id'] = array('alias' => 'group_id');
							$cols['prd_group_parent_id'] = array('alias' => 'group_parent_id');
						}
						break;
					case 'prd-promo':
						$content = $content['obj'];
						$table_name = 'ord_products_promotions';
						$cols = array(
							'opm_ord_id' => array('alias' => 'ord_id'),
							'opm_prd_id' => array('alias' => 'prd_id'),
							'opm_line_id' => array('alias' => 'line_id'),
							'opm_cod_id' => array('alias' => 'cod_id'),
							);
						$cols_del = array(
							'id' => 'opm_ord_id'
							);
						break;
				}
				break;
			case CLS_DOCUMENT:
				switch($k){
					case 'obj_del':
						$table_name = 'doc_documents';
						$deleted = $content;
						$is_new_data = false;
						$cols_del = array('id' => 'doc_id');
						break;
					case 'obj':
						$table_name = 'doc_documents';
						$cols = array(
							'doc_id' => array('alias' => 'id'),
							'doc_type' => array('alias' => 'type_id'),
							'doc_filename' => array('alias' => 'filename', 'escape' => true),
							'doc_name' => array('alias' => 'name', 'escape' => true),
							'doc_desc' => array('alias' => 'desc', 'escape' => true),
							'doc_pos' => array('alias' => 'pos'),
							'doc_size' => array('alias' => 'size'),
							'doc_date_created' => array('alias' => 'date_created_en', 'escape' => true),
							);
						break;
				}
				break;
			case CLS_STORE:
				switch($k){
					case 'obj_del':
						$table_name = 'dlv_stores';
						$deleted = $content;
						$is_new_data = false;
						$cols_del = array('id' => 'str_id');
						break;
					case 'obj':
						$table_name = 'dlv_stores';
						$cols = array(
							'str_id' => array('alias' => 'id'),
							'str_name' => array('alias' => 'name', 'escape' => true),
							'str_title' => array('alias' => 'title', 'escape' => true),
							'str_url' => array('alias' => 'url', 'escape' => true),
							'str_desc' => array('alias' => 'desc', 'escape' => true),
							'str_address1' => array('alias' => 'address1', 'escape' => true),
							'str_address2' => array('alias' => 'address2', 'escape' => true),
							'str_zipcode' => array('alias' => 'zipcode', 'escape' => true),
							'str_city' => array('alias' => 'city', 'escape' => true),
							'str_country' => array('alias' => 'country', 'escape' => true),
							'str_manager' => array('alias' => 'manager', 'escape' => true),
							'str_phone' => array('alias' => 'phone', 'escape' => true),
							'str_fax' => array('alias' => 'fax', 'escape' => true),
							'str_email' => array('alias' => 'email', 'escape' => true),
							'str_date_modified' => array('alias' => 'date_modified', 'escape' => true),
							'str_website' => array('alias' => 'website', 'escape' => true),
							'str_publish' => array('alias' => 'publish'),
							'str_allow_delivery' => array('alias' => 'allow_delivery'),
							'str_latitude' => array('alias' => 'latitude'),
							'str_longitude' => array('alias' => 'longitude'),
							'str_is_sync' => array('alias' => 'is_sync'),
							);
						$search_cols = array(
							'sc_cls_id' => CLS_STORE,
							'sc_obj_id_0' => 'id',
							'sc_obj_id_1' => 0,
							'sc_obj_id_2' => 0,
							'sc_ref' => '',
							'sc_text_0' => 'name',
							'sc_text_1' => 'title',
							'sc_text_2' => 'zipcode',
							'sc_text_3' => 'city',
							'sc_text_4' => '',
							);
						if( $db_version >= 5010 ){
							$search_cols['sc_ref'] = '';
							$search_cols['sc_ref_1'] = 'name';
							$search_cols['sc_ref_2'] = 'title';
							$search_cols['sc_text_0'] = 'email';
							$search_cols['sc_text_1'] = 'zipcode';
							$search_cols['sc_text_2'] = 'city';
							$search_cols['sc_text_3'] = array('address1', 'address2');
							$search_cols['sc_text_4'] = '';
							$search_cols['sc_text_5'] = 'phone';
						}
						break;
					case 'brands':
						$content = $content['obj'];
						$table_name = 'dlv_stores_brands';
						$cols = array(
							'stb_str_id' => array('alias' => 'str_id'),
							'stb_brd_id' => array('alias' => 'brd_id'),
							'stb_sales' => array('alias' => 'sales')
							);
						$cols_del = array(
							'id' => 'stb_str_id'
							);
						break;
				}
				break;
			case CLS_WISHLISTS:
				switch($k){
					case 'obj_del':
						$table_name = 'gu_wishlists';
						$deleted = $content;
						$is_new_data = false;
						$cols_del = array('id' => 'gw_id');
						break;
					case 'obj':
						$table_name = 'gu_wishlists';
						$cols = array(
							'gw_id' => array('alias' => 'id'),
							'gw_name' => array('alias' => 'name', 'escape' => true),
							'gw_desc' => array('alias' => 'desc', 'escape' => true),
							'gw_publish' => array('alias' => 'publish'),
							'gw_is_system' => array('alias' => 'is_system'),
							'gw_url_alias' => array('alias' => 'url_alias', 'escape' => true),
							'gw_date_created' => array('alias' => 'date_created', 'escape' => true),
							'gw_date_modified' => array('alias' => 'date_modified', 'escape' => true),
							'gw_usr_id' => array('alias' => 'usr_id'),
							);
						break;
					case 'products':
						$content = $content['obj'];
						$table_name = 'gu_wishlists_products';
						$cols = array(
							'gwp_type_id' => array('alias' => 'type_id'),
							'gwp_prd_id' => array('alias' => 'prd_id'),
							'gwp_desc' => array('alias' => 'desc', 'escape' => true),
							'gwp_date_created' => array('alias' => 'date_created', 'escape' => true)
							);
						$cols_del = array(
							'id' => 'gwp_type_id'
							);
						break;
				}
				break;
			case CLS_IMAGE:
				switch($k){
					case 'obj_del':
						$table_name = 'img_images';
						$deleted = $content;
						$is_new_data = false;
						$cols_del = array('id' => 'img_id');
						break;
					case 'obj':
						$table_name = 'img_images';
						$cols = array(
							'img_id' => array('alias' => 'id'),
							'img_src_name' => array('alias' => 'src_name', 'escape' => true),
							'img_type' => array('alias' => 'type', 'escape' => true),
							);
						break;
				}
				break;
			case CLS_USER:
				switch($k){
					case 'obj_del':
						$table_name = 'gu_users';
						$deleted = $content;
						$is_new_data = false;
						$is_searchable = true;
						$cols_del = array('id' => 'usr_id');
						break;
					case 'obj':
						$table_name = 'gu_users';
						$cols = array(
							'usr_id' => array('alias' => 'id'),
							'usr_ref' => array('alias' => 'ref', 'escape' => true),
							'usr_email' => array('alias' => 'email', 'escape' => true),
							'usr_prf_id' => array('alias' => 'prf_id'),
							'usr_prc_id' => array('alias' => 'prc_id'),
							'usr_adr_invoices' => array('alias' => 'adr_invoices'),
							'usr_adr_delivery' => array('alias' => 'adr_delivery'),
							'usr_cac_id' => array('alias' => 'cac_id'),
							'usr_img_id' => array('alias' => 'img_id'),
							'usr_seller_id' => array('alias' => 'seller_id'),
							'usr_dps_id' => array('alias' => 'dps_id'),
							'usr_encours' => array('alias' => 'encours'),
							'usr_encours_allow' => array('alias' => 'encours_allow'),
							'usr_naf' => array('alias' => 'naf', 'escape' => true),
							'usr_taxcode' => array('alias' => 'taxcode', 'escape' => true),
							'usr_is_locked' => array('alias' => 'is_locked'),
							'usr_parent_id' => array('alias' => 'parent_id', 'zero_is_null' => true),
							'usr_opm_id' => array('alias' => 'opm_id'),
							'usr_bnk_id' => array('alias' => 'bnk_id'),
							'usr_rco_id' => array('alias' => 'rco_id'),
							);
						$search_cols = array(
							'sc_cls_id' => CLS_USER,
							'sc_obj_id_0' => 'id',
							'sc_obj_id_1' => 0,
							'sc_obj_id_2' => 0,
							'sc_ref' => 'ref',
							'sc_text_0' => 'society',
							'sc_text_1' => array('adr_lastname', 'adr_firstname'),
							'sc_text_2' => 'email',
							'sc_text_3' => 'zipcode',
							'sc_text_4' => 'city',
							);

						if( $db_version >= 295 ){
							$cols['usr_can_login'] = array('alias' => 'can_login');
						}
						if( $db_version >= 376 ){
							$cols['usr_website'] = array('alias' => 'website', 'escape' => true);
						}
						if( $db_version >= 393 ){
							$cols['usr_is_sync'] = array('alias' => 'is_sync');
						}
						if( $db_version >= 420 ){
							$search_cols['sc_ref_1'] = 'phone';
							$search_cols['sc_ref_2'] = 'mobile';
						}
						if( $db_version >= 420 ){
							$cols['usr_date_created'] = array('alias' => 'usr_date_created', 'escape' => true);
						}
						if( $db_version >= 4916 ){
							$search_cols['sc_text_5'] =array('address1', 'address2', 'address3');
						}
						if( $db_version >= 5010 ){
							$search_cols['sc_ref'] = 'ref';
							$search_cols['sc_ref_1'] = 'society';
							$search_cols['sc_ref_2'] = array('adr_lastname', 'adr_firstname');
							$search_cols['sc_text_0'] = 'email';
							$search_cols['sc_text_1'] = 'zipcode';
							$search_cols['sc_text_2'] = 'city';
							$search_cols['sc_text_3'] = array('address1', 'address2', 'address3');
							$search_cols['sc_text_4'] = 'mobile';
							$search_cols['sc_text_5'] = 'phone';
						}

						break;
					case 'address':
						$content = $content['obj'];
						$table_name = 'gu_adresses';
						$cols = array(
							'adr_usr_id' => array('alias' => 'usr_id'),
							'adr_id' => array('alias' => 'id'),
							'adr_type_id' => array('alias' => 'type_id'),
							'adr_desc' => array('alias' => 'description', 'escape' => true),
							'adr_title_id' => array('alias' => 'title_id'),
							'adr_firstname' => array('alias' => 'firstname', 'escape' => true),
							'adr_lastname' => array('alias' => 'lastname', 'escape' => true),
							'adr_society' => array('alias' => 'society', 'escape' => true),
							'adr_siret' => array('alias' => 'siret', 'escape' => true),
							'adr_address1' => array('alias' => 'address1', 'escape' => true),
							'adr_address2' => array('alias' => 'address2', 'escape' => true),
							'adr_postal_code' => array('alias' => 'postal_code', 'escape' => true),
							'adr_city' => array('alias' => 'city', 'escape' => true),
							'adr_country' => array('alias' => 'country', 'escape' => true),
							'adr_phone' => array('alias' => 'phone', 'escape' => true),
							'adr_fax' => array('alias' => 'fax', 'escape' => true),
							'adr_mobile' => array('alias' => 'mobile', 'escape' => true),
							'adr_phone_work' => array('alias' => 'phone_work', 'escape' => true),
							'adr_email' => array('alias' => 'email', 'escape' => true),
							'adr_date_created' => array('alias' => 'date_created', 'escape' => true),
							'adr_cnt_code' => array('alias' => 'country_code', 'escape' => true)
							);

						if( $db_version >= 233 ){
							$cols['adr_address3'] = array('alias' => 'address3', 'escape' => true);
						}
						if( $db_version >= 243 ){
							$cols['adr_latitude'] = array('alias' => 'latitude');
							$cols['adr_longitude'] = array('alias' => 'longitude');
							$cols['adr_date_location'] = array('alias' => 'date_location', 'escape' => true);
						}
						if( $db_version >= 249 ){
							$cols['adr_manual_location'] = array('alias' => 'manual_location', 'escape' => true);
						}
						if( $db_version >= 372 ){
							$cols['adr_proj_x'] = array('alias' => 'proj_x');
							$cols['adr_proj_y'] = array('alias' => 'proj_y');
						}

						$cols_del = array(
							'id' => 'adr_usr_id'
							);
						break;
					case 'payment':
						$content = $content['obj'];
						$table_name = 'gu_users_payment_types';
						$cols = array(
							'upt_usr_id' => array('alias' => 'usr'),
							'upt_pay_id' => array('alias' => 'id'),
							'upt_days' => array('alias' => 'days'),
							'upt_days_type' => array('alias' => 'days_type'),
							'upt_day_stop' => array('alias' => 'day_stop'),
							'upt_amount_type' => array('alias' => 'amount_type'),
							'upt_amount_value' => array('alias' => 'amount_value'),
							);
						$cols_del = array(
							'id' => 'upt_usr_id'
							);
						break;
				}
				break;
			case CLS_PERIODS:
				switch($k){
					case 'obj_del':
						$table_name = 'per_objects';
						$deleted = $content;
						$is_new_data = false;
						$cols_del = array(
							'id' => 'pob_id'
							);
						break;
					case 'obj':
						$table_name = 'per_objects';
						$cols = array(
							'pob_cls_id' => array('alias' => 'cls_id'),
							'pob_obj_id_0' => array('alias' => 'obj_id_0'),
							'pob_obj_id_1' => array('alias' => 'obj_id_1'),
							'pob_obj_id_2' => array('alias' => 'obj_id_2'),
							'pob_id' => array('alias' => 'id'),
							'pob_type_id' => array('alias' => 'type_id'),
							'pob_date_modified' => array('alias' => 'date_modified', 'escape' => true),
							);
						break;
					case 'periods':
						$content = $content['obj'];
						$table_name = 'per_periods';
						$cols = array(
							'per_pob_id' => array('alias' => 'pob_id'),
							'per_day_id' => array('alias' => 'day_id'),
							'per_hour_start' => array('alias' => 'start', 'escape' => true),
							'per_hour_stop' => array('alias' => 'end', 'escape' => true),
							);
						$cols_del = array(
							'id' => 'per_pob_id'
							);
						break;
					case 'holidays':
						$content = $content['obj'];
						$table_name = 'per_holidays';
						$cols = array(
							'hld_pob_id' => array('alias' => 'pob_id'),
							'hld_date' => array('alias' => 'date', 'escape' => true),
							'hld_expedition' => array('alias' => 'exp'),
							);
						$cols_del = array(
							'id' => 'hld_pob_id'
							);
						break;
					case 'events':
						$content = $content['obj'];
						$table_name = 'per_events';
						$cols = array(
							'pev_pob_id' => array('alias' => 'pob_id'),
							'pev_name' => array('alias' => 'name', 'escape' => true),
							'pev_date_start' => array('alias' => 'start_en', 'escape' => true),
							'pev_date_stop' => array('alias' => 'end_en', 'escape' => true),
							'pev_id' => array('alias' => 'id'),
							);
						$cols_del = array(
							'id' => 'pev_pob_id'
							);
						break;
				}
				break;
			case CLS_IMAGES_OBJECT:
				switch($k){
					case 'obj_del':
						$table_name = 'img_images_objects';
						$deleted = $content;
						$is_new_data = false;
						$cols_del = array(
							'id' => 'imo_id'
							);
						break;
					case 'obj':
						$table_name = 'img_images_objects';
						$cols = array(
							'imo_id' => array('alias' => 'imo_id'),
							'imo_cls_id' => array('alias' => 'cls_id'),
							'imo_img_id' => array('alias' => 'img_id'),
							'imo_type_id' => array('alias' => 'type_id'),
							'imo_obj_id_0' => array('alias' => 'obj_id_0'),
							'imo_obj_id_1' => array('alias' => 'obj_id_1'),
							'imo_obj_id_2' => array('alias' => 'obj_id_2'),
							'imo_pos' => array('alias' => 'pos'),
							'imo_date_created' => array('alias' => 'date_created', 'escape' => true),
							'imo_date_modified' => array('alias' => 'date_modified', 'escape' => true),
							'imo_date_deleted' => array('alias' => 'date_deleted', 'escape' => true),
							);
						break;
				}
				break;
			case CLS_CALLS:
				$is_id_alpha = true;
				$table_name = 'gu_call_logs';
				switch($k){
					case 'obj_del':
						$deleted = $content;
						$is_new_data = false;
						$cols_del = array(
							'id' => 'gcl_id'
							);
						break;
					case 'obj':
						$cols = array(
							'gcl_id' => array('alias' => 'id', 'escape' => true),
							'gcl_author_id' => array('alias' => 'gcl_author_id'),
							'gcl_usr_dst' => array('alias' => 'gcl_usr_dst'),
							'gcl_date' => array('alias' => 'gcl_date_created', 'escape' => true),
							'gcl_duration' => array('alias' => 'gcl_duration'),
							'gcl_type' => array('alias' => 'gcl_type'),
							'gcl_phone' => array('alias' => 'gcl_phone', 'escape' => true),
							'gcl_callback_date' => array('alias' => 'gcl_callback_date', 'default' => 'null', 'escape' => true),
							'gcl_answered' => array('alias' => 'gcl_answered', 'default' => 0),
							'gcl_dst_free' => array('alias' => 'gcl_dst_free', 'default' => 0),
							'gcl_comment' => array('alias' => 'gcl_comment', 'escape' => true),
							);

						if( $db_version >= 404 ){
							$cols['gcl_author_name'] = array('alias' => 'gcl_author_name', 'default' => 'null', 'escape' => true);
						}
						if( $db_version >= 423 ){
							$cols['gcl_android_id'] = array('alias' => 'gcl_android_id', 'default' => 'null', 'escape' => true);
						}
						break;
				}
				break;
			case CLS_NOTIFICATIONS:
				$is_id_alpha = true;
				$table_name = 'nt_notifications';
				switch($k){
					case 'obj_del':
						$deleted = $content;
						$is_new_data = false;
						$cols_del = array(
							'id' => 'nt_id'
							);
						break;
					case 'obj':
						$cols = array(
							'nt_id' => array('alias' => 'id', 'escape' => true),
							'nt_type_id' => array('alias' => 'nt_type_id'),
							'nt_author_id' => array('alias' => 'nt_author_id'),
							'nt_author_name' => array('alias' => 'nt_author_name', 'escape' => true, 'default' => '\'\''),
							'nt_title' => array('alias' => 'nt_title', 'escape' => true),
							'nt_desc' => array('alias' => 'nt_desc', 'escape' => true),
							'nt_date_created' => array('alias' => 'nt_date_created', 'escape' => true),
							);
						break;
					case 'objects':
						$content = $content['obj'];
						$table_name = 'nt_notification_objects';
						$cols = array(
							'nto_nt_id' => array('alias' => 'id', 'escape' => true),
							'nto_cls_id' => array('alias' => 'cls_id'),
							'nto_obj_id_0' => array('alias' => 'obj_id_0'),
							'nto_obj_id_1' => array('alias' => 'obj_id_1'),
							'nto_obj_id_2' => array('alias' => 'obj_id_2'),
							);
						$cols_del = array(
							'id' => 'nto_nt_id'
							);
						break;
				}
				break;
			case CLS_CHAT_CONVERSATIONS:
				$is_id_alpha = true;
				$table_name = 'ch_conversations';
				switch($k){
					case 'obj_del':
						$deleted = $content;
						$is_new_data = false;
						$cols_del = array(
							'id' => 'ch_id'
							);
						break;
					case 'obj':
						$cols = array(
							'ch_id' => array('alias' => 'id', 'escape' => true),
							'ch_cls_id' => array('alias' => 'ch_cls_id'),
							'ch_obj_id_0' => array('alias' => 'ch_obj_id_0', 'escape' => true),
							'ch_obj_id_1' => array('alias' => 'ch_obj_id_1', 'escape' => true),
							'ch_obj_id_2' => array('alias' => 'ch_obj_id_2', 'escape' => true),
							'ch_date_created' => array('alias' => 'ch_date_created', 'escape' => true),
							);
						break;
					case 'messages':
						$content = $content['obj'];
						$table_name = 'ch_messages';
						$cols = array(
							'msg_id' => array('alias' => 'unique_id', 'escape' => true),
							'msg_conv_id' => array('alias' => 'id', 'escape' => true),
							'msg_author_id' => array('alias' => 'author_id'),
							'msg_author_email' => array('alias' => 'author_email', 'escape' => true),
							'msg_author_name' => array('alias' => 'author_name', 'escape' => true),
							'msg_content' => array('alias' => 'content', 'escape' => true),
							'msg_date_created' => array('alias' => 'date_created', 'escape' => true),
							);
						$cols_del = array(
							'id' => 'msg_conv_id'
							);
						break;
				}
				break;
			case CLS_FOLLOWED_LIST:
				switch($k){
					case 'obj_del':
						$table_name = 'prw_followed_lists';
						$deleted = $content;
						$is_new_data = false;
						$cols_del = array(
							'id' => 'pfl_id'
							);
						break;
					case 'obj':
						$table_name = 'prw_followed_lists';
						$cols = array(
							'pfl_id' => array('alias' => 'id'),
							'pfl_name' => array('alias' => 'name', 'escape' => true),
							'pfl_type' => array('alias' => 'type'),
							'pfl_is_published' => array('alias' => 'is_published'),
							'pfl_date_created' => array('alias' => 'date_created', 'escape' => true),
							'pfl_date_modified' => array('alias' => 'date_modified', 'escape' => true),
							);
						if( $db_version >= 451 ){
							$cols['pfl_fls_id'] = array('alias' => 'fls_id', 'default' => 'null');
						}
						break;
					case 'products':
						$content = $content['obj'];
						$table_name = 'prw_followed_products';
						$cols = array(
							'pwf_id' => array('alias' => 'id'),
							'pwf_cpt_id' => array('alias' => 'cpt_id'),
							'pwf_prd_id' => array('alias' => 'prd_id'),
							'pwf_pfl_id' => array('alias' => 'pfl_id'),
							'pwf_cpt_ref' => array('alias' => 'cpt_ref', 'escape' => true),
							'pwf_disable' => array('alias' => 'disable'),
							'pwf_is_cpt' => array('alias' => 'is_cpt'),
							'pwf_rank' => array('alias' => 'rank'),
							'pwf_pmc' => array('alias' => 'pmc'),
							'pwf_date_created' => array('alias' => 'date_created', 'escape' => true),
							'pwf_date_lastcheck' => array('alias' => 'date_lastcheck', 'escape' => true),
							'pwf_date_modified' => array('alias' => 'date_modified', 'escape' => true),
							);
						$cols_del = array(
							'id' => 'pwf_pfl_id'
							);
						break;
					case 'users':
						$content = $content['obj'];
						$table_name = 'prw_followed_users';
						$cols = array(
							'pfu_pfl_id' => array('alias' => 'pfl_id'),
							'pfu_prf_id' => array('alias' => 'prf_id'),
							'pfu_usr_id' => array('alias' => 'usr_id'),
							'pfu_date_created' => array('alias' => 'date_created', 'escape' => true),
							'pfu_date_modified' => array('alias' => 'date_modified', 'escape' => true),
							);
						if( $db_version >= 4818 ){
							$cols['pfu_usr_ref'] = array('alias' => 'usr_ref', 'default' => 'null', 'escape' => true);
						}
						$cols_del = array(
							'id' => 'pfu_pfl_id'
							);
						break;
				}
				break;
			case CLS_LINEAR_STATES:
				switch($k){
					case 'obj_del':
						$table_name = 'prw_states';
						$deleted = $content;
						$is_new_data = false;
						$cols_del = array(
							'id' => 'ps_id'
							);
						break;
					case 'obj':
						$table_name = 'prw_states';
						$cols = array(
							'ps_id' => array('alias' => 'id'),
							'ps_name' => array('alias' => 'name', 'escape' => true),
							'ps_date_created' => array('alias' => 'date_created', 'escape' => true),
							'ps_date_modified' => array('alias' => 'date_modified', 'escape' => true),
							);
						break;
				}
				break;
			case CLS_FOLLOWED_LIST_SECTIONS:
				switch($k){
					case 'obj_del':
						$table_name = 'prw_followed_list_sections';
						$deleted = $content;
						$is_new_data = false;
						$cols_del = array(
							'id' => 'fls_id'
							);
						break;
					case 'obj':
						$table_name = 'prw_followed_list_sections';
						$cols = array(
							'fls_id' => array('alias' => 'id'),
							'fls_title' => array('alias' => 'title', 'escape' => true),
							'fls_date_created' => array('alias' => 'date_created', 'escape' => true)
							);
						break;
				}
				break;
			case CLS_LINEAR_RAISED:
				switch($k){
					case 'obj_del':
						$table_name = 'prw_linear_raised';
						$deleted = $content;
						$is_new_data = false;
						$cols_del = array(
							'id' => 'plr_id'
							);
						break;
					case 'obj':
						$table_name = 'prw_linear_raised';
						$cols = array(
							'plr_id' => array('alias' => 'id'),
							'plr_author_id' => array('alias' => 'author_id'),
							'plr_usr_id' => array('alias' => 'usr_id'),
							'plr_pfl_id' => array('alias' => 'pfl_id'),
							'plr_group_id' => array('alias' => 'group_id'),
							'plr_date_created' => array('alias' => 'date_created', 'escape' => true),
							'plr_date_modified' => array('alias' => 'date_modified', 'escape' => true),
							);
						if( $db_version >= 449 ){
							$cols['plr_total_dn'] = array('alias' => 'total_dn', 'default' => 'null');
							$cols['plr_total_dn_section'] = array('alias' => 'total_dn_section', 'default' => 'null');
						}
						break;
					case 'offers':
						$table_name = 'prw_offers';
						parseData($filezip, CLS_LINEAR_OFFERS, $content);
						$next = true;
						$cols_del = array(
							'id' => 'ofr_plr_id'
							);
						$related_del = array('delete from prw_offers_states where pst_ofr_id in (select ofr_id from prw_offers where ofr_plr_id in ([IDS]))');
						break;
				}
				break;
			case CLS_LINEAR_OFFERS:
				switch($k){
					case 'obj':
						$k = 'offers';
						$table_name = 'prw_offers';
						$cols = array(
							'ofr_id' => array('alias' => 'id'),
							'ofr_cpt_id' => array('alias' => 'cpt_id'),
							'ofr_prd_id' => array('alias' => 'prd_id'),
							'ofr_landedprice' => array('alias' => 'landedprice'),
							'ofr_shippingprice' => array('alias' => 'shippingprice'),
							'ofr_promo_price' => array('alias' => 'promo_price'),
							'ofr_url' => array('alias' => 'url', 'escape' => true),
							'ofr_date_created' => array('alias' => 'date_created', 'escape' => true),
							'ofr_usr_id' => array('alias' => 'usr_id'),
							'ofr_count' => array('alias' => 'count'),
							'ofr_level' => array('alias' => 'level'),
							'ofr_facings' => array('alias' => 'facings'),
							'ofr_positions' => array('alias' => 'positions'),
							'ofr_pwf_rank' => array('alias' => 'pwf_rank'),
							'ofr_pwf_pmc' => array('alias' => 'pwf_pmc'),
							'ofr_plr_id' => array('alias' => 'plr_id'),
							);
						if( $db_version >= 459 ){
							$cols['ofr_is_cpt'] = array('alias' => 'is_cpt');
						}
						break;
					case 'states':
						$content = $content['obj'];
						$table_name = 'prw_offers_states';
						$cols = array(
							'pst_ofr_id' => array('alias' => 'ofr_id'),
							'pst_ps_id' => array('alias' => 'ps_id'),
							);
						$cols_del = array(
							'id' => 'pst_ofr_id'
							);
						break;
				}
				break;
			case CLS_OBJECTIF_TYPES:
				switch($k){
					case 'obj_del':
						$table_name = 'obj_objectifs';
						$deleted = $content;
						$is_new_data = false;
						$cols_del = array(
							'id' => 'obj_id'
							);
						break;
					case 'obj':
						$table_name = 'obj_objectifs';
						$cols = array(
							'obj_id' => array('alias' => 'id'),
							'obj_name' => array('alias' => 'name', 'escape' => true),
							'obj_code' => array('alias' => 'code', 'escape' => true),
							);
						break;
				}
				break;
			case CLS_OBJECTIFS:
				switch($k){
					case 'obj_del':
						$table_name = 'obj_periods';
						$deleted = $content;
						$is_new_data = false;
						$cols_del = array(
							'id' => 'obp_id'
							);
						break;
					case 'obj':
						$table_name = 'obj_periods';
						$cols = array(
							'obp_id' => array('alias' => 'id'),
							'obp_usr_id' => array('alias' => 'usr_id'),
							'obp_obj_id' => array('alias' => 'obj_id'),
							'obp_date_type' => array('alias' => 'date_type', 'escape' => true),
							'obp_date_start' => array('alias' => 'date_start', 'escape' => true),
							'obp_date_end' => array('alias' => 'date_end', 'escape' => true),
							'obp_value' => array('alias' => 'value'),
							'obp_name' => array('alias' => 'name', 'escape' => true)
							);
						if( $db_version >= 4982 ){
							$cols['obp_cls_id'] = array('alias' => 'cls_id', 'default' => 0);
							$cols['obp_obj_id_0'] = array('alias' => 'obj_id_0', 'default' => 0);
							$cols['obp_obj_id_1'] = array('alias' => 'obj_id_1', 'default' => 0);
							$cols['obp_obj_id_2'] = array('alias' => 'obj_id_2', 'default' => 0);
							$cols['obp_seller_id'] = array('alias' => 'seller_id', 'default' => 0);
						}
						break;
				}
				break;
			case CLS_STATS_GOALS:
				$is_id_alpha = true;
				switch($k){
					case 'obj_del':
						$table_name = 'stats_goals';
						$deleted = $content;
						$is_new_data = false;
						$cols_del = array(
							'id' => 'sg_id'
							);
						break;
					case 'obj':
						$table_name = 'stats_goals';
						$cols = array(
							'sg_id' => array('alias' => 'id', 'escape' => true),
							'sg_firstname' => array('alias' => 'firstname', 'escape' => true, 'default' => '\'\''),
							'sg_lastname' => array('alias' => 'lastname', 'escape' => true, 'default' => '\'\''),
							'sg_email' => array('alias' => 'email', 'escape' => true, 'default' => '\'\''),
							'sg_ref' => array('alias' => 'ref', 'escape' => true, 'default' => '\'\''),
							'sg_usr_id' => array('alias' => 'usr_id', 'default' => 0),
							'sg_seller_id' => array('alias' => 'seller_id', 'default' => 0),
							'sg_date' => array('alias' => 'date'),
							'sg_STATS_NB_ORDER' => array('alias' => '_STATS_NB_ORDER', 'default' => 0),
							'sg_STATS_CA_ORDER' => array('alias' => '_STATS_CA_ORDER', 'default' => 0),
							'sg_STATS_CA_INVOICE' => array('alias' => '_STATS_CA_INVOICE', 'default' => 0),
							'sg_STATS_MARGE_ORDER' => array('alias' => '_STATS_MARGE_ORDER', 'default' => 0),
							'sg_STATS_MARGE_INVOICE' => array('alias' => '_STATS_MARGE_INVOICE', 'default' => 0),
							'sg_STATS_NB_DEVIS' => array('alias' => '_STATS_NB_DEVIS', 'default' => 0),
							'sg_STATS_NB_CALLS' => array('alias' => '_STATS_NB_CALLS', 'default' => 0),
							'sg_STATS_TIME_CALLS' => array('alias' => '_STATS_TIME_CALLS', 'default' => 0),
							'sg_STATS_NB_REPORT' => array('alias' => '_STATS_NB_REPORT', 'default' => 0),
							'sg_STATS_TIME_REPORT' => array('alias' => '_STATS_TIME_REPORT', 'default' => 0),
							'sg_STATS_NB_PRD_ORDER' => array('alias' => '_STATS_NB_PRD_ORDER', 'default' => 0),
							'sg_STATS_BIG_SALE' => array('alias' => '_STATS_BIG_SALE', 'default' => 0),
							'sg_STATS_AVG_BASKET' => array('alias' => '_STATS_AVG_BASKET', 'default' => 0),
							'sg_STATS_NB_CUSTOMERS' => array('alias' => '_STATS_NB_CUSTOMERS', 'default' => 0),
							'sg_STATS_UPDATED_CUSTOMERS' => array('alias' => '_STATS_UPDATED_CUSTOMERS', 'default' => 0),
							'sg_STATS_FIRST_CALL' => array('alias' => '_STATS_FIRST_CALL', 'default' => 0),
							'sg_STATS_NB_CONTACT' => array('alias' => '_STATS_NB_CONTACT', 'default' => 0),
							'sg_STATS_UPDATED_CONTACT' => array('alias' => '_STATS_UPDATED_CONTACT', 'default' => 0),
							'sg_STATS_NB_PROSPECT' => array('alias' => '_STATS_NB_PROSPECT', 'default' => 0)
							);
						if( $db_version >= 4890 ){
							$cols['sg_STATS_NB_LINEAR_RAISED'] = array('alias' => '_STATS_NB_LINEAR_RAISED', 'default' => 0);
						}
						if( $db_version >= 4945 ){
							$cols['sg_STATS_NB_REWARDS'] = array('alias' => '_STATS_NB_REWARDS', 'default' => 0);
						}
						if( $db_version >= 4975 ){
							$cols['sg_src_cls_id'] = array('alias' => 'src_cls_id', 'default' => 0);
							$cols['sg_cust_id'] = array('alias' => 'cust_id', 'default' => 0);
							$cols['sg_src_obj_id_0'] = array('alias' => 'src_obj_id_0', 'default' => 0);
						}
						break;
				}
				break;
			case CLS_ZONES:
				switch($k){
					case 'obj_del':
						$table_name = 'sys_zones';
						$deleted = $content;
						$is_new_data = false;
						$cols_del = array(
							'id' => 'dzn_id'
							);
						break;
					case 'obj':
						$table_name = 'sys_zones';
						$cols = array(
							'dzn_id' => array('alias' => 'id'),
							'dzn_code' => array('alias' => 'code', 'escape' => true),
							'dzn_name' => array('alias' => 'name', 'escape' => true),
							'dzn_country' => array('alias' => 'country_code', 'escape' => true),
							'dzn_type_id' => array('alias' => 'type_id'),
							'dzn_parent_id' => array('alias' => 'parent_id'),
							);
						break;
				}
				break;
			case CLS_ACTIONS_HISTORY:
				switch($k){
					case 'obj_del':
						$table_name = 'act_actions_history';
						$deleted = $content;
						$is_new_data = false;
						$cols_del = array('id' => 'ahi_id');
						break;
					case 'obj':
						$table_name = 'act_actions_history';
						$cols = array(
							'ahi_id' => array('alias' => 'id'),
							'ahi_act_id' => array('alias' => 'act_id'),
							'ahi_date_created' => array('alias' => 'date_created', 'escape' => true),
							'ahi_author_id' => array('alias' => 'author_id'),
							'ahi_cls_id' => array('alias' => 'cls_id'),
							'ahi_obj_id_0' => array('alias' => 'obj_id_0', 'escape' => true),
							'ahi_obj_id_1' => array('alias' => 'obj_id_1', 'escape' => true),
							'ahi_obj_id_2' => array('alias' => 'obj_id_2', 'escape' => true),
							);
						break;
				}
				break;
			case CLS_PRD_REWARDS:
				switch($k){
					case 'obj_del':
						$table_name = 'rwd_prd_rewards';
						$deleted = $content;
						$is_new_data = false;
						$cols_del = array('id' => 'rwp_id');
						break;
					case 'obj':
						$table_name = 'rwd_prd_rewards';
						$cols = array(
							'rwp_id' => array('alias' => 'id'),
							'rwp_prd_id' => array('alias' => 'prd_id'),
							'rwp_prf_id' => array('alias' => 'prf_id'),
							'rwp_points' => array('alias' => 'points'),
							'rwp_date_start' => array('alias' => 'date_start', 'escape' => true),
							'rwp_date_end' => array('alias' => 'date_end', 'escape' => true),
							'rwp_date_created' => array('alias' => 'date_created', 'escape' => true),
							);
						break;
				}
				break;
			case CLS_STATS_REWARDS:
				switch($k){
					case 'obj_del':
						$table_name = 'stats_rewards';
						$deleted = $content;
						$is_new_data = false;
						$cols_del = array('id' => 'stats_id');
						break;
					case 'obj':
						$table_name = 'stats_rewards';
						$cols = array(
							'stats_id' => array('alias' => 'stats_id'),
							'stats_wst_id' => array('alias' => 'wst_id'),
							'stats_date_en' => array('alias' => 'date_en', 'escape' => true),
							'stats_usr_id' => array('alias' => 'usr'),
							'stats_rwa_id' => array('alias' => 'rwa'),
							'stats_action' => array('alias' => 'name', 'escape' => true),
							'stats_cls_id' => array('alias' => 'cls_id'),
							'stats_obj_id_0' => array('alias' => 'obj_id_0'),
							'stats_obj_id_1' => array('alias' => 'obj_id_1'),
							'stats_obj_id_2' => array('alias' => 'obj_id_2'),
							'stats_points' => array('alias' => 'pts'),
							'stats_ratio' => array('alias' => 'ratio', 'escape' => true),
							'stats_convert' => array('alias' => 'convert', 'escape' => true),
							'stats_date_limit_en' => array('alias' => 'date_limit_en', 'escape' => true),
							'stats_godchild' => array('alias' => 'godchild'),
							'stats_type_amount' => array('alias' => 'type_amount', 'escape' => true),
							'stats_date_created' => array('alias' => 'date_created', 'escape' => true),
							);
						break;
				}
				break;
			case CLS_SERIALS:
				switch($k){
					case 'obj':
						$k = 'serials';
						$table_name = 'obj_serials';
						$cols = array(
							'osl_id' => array('alias' => 'id'),
							'osl_cls_id' => array('alias' => 'cls_id'),
							'osl_obj_id_0' => array('alias' => 'obj_id_0'),
							'osl_obj_id_1' => array('alias' => 'obj_id_1'),
							'osl_obj_id_2' => array('alias' => 'obj_id_2'),
							'osl_date_dlc' => array('alias' => 'date_dlc', 'escape' => true),
							'osl_date_production' => array('alias' => 'date_production', 'escape' => true),
							'osl_dps_id' => array('alias' => 'dps_id'),
							'osl_serial' => array('alias' => 'serial', 'escape' => true),
							'osl_ref_gescom' => array('alias' => 'ref_gescom', 'escape' => true),
							'osl_qte' => array('alias' => 'qte'),
							);
						break;
				}
				break;

		}

		// gestion des éléments communs à toutes les classes
		switch($k){
			case 'field_values':
				$table_name = 'fld_object_values';
				$cols = array(
					'pv_lng_code' => array('alias' => 'lng_code', 'escape' => true),
					'pv_obj_id_0' => array('alias' => 'obj_id_0' ),
					'pv_obj_id_1' => array('alias' => 'obj_id_1' ),
					'pv_obj_id_2' => array('alias' => 'obj_id_2' ),
					'pv_fld_id' => array('alias' => 'id' ),
					'pv_value' => array('alias' => 'obj_value', 'escape' => true ),
					);

				// cas du passage des clé en alpha
				if( $db_version >= 375 ){
					$cols['pv_obj_id_0'] = array('alias' => 'obj_id_0', 'escape' => true );
					$cols['pv_obj_id_1'] = array('alias' => 'obj_id_1', 'escape' => true );
					$cols['pv_obj_id_2'] = array('alias' => 'obj_id_2', 'escape' => true );
				}

				$cols_del = array(
					'id' => 'pv_obj_id_0', 'id_1' => 'pv_obj_id_1', 'id_2' => 'pv_obj_id_2'
					);
				$where_del = "pv_fld_id in ( select fld_id from fld_fields where fld_cls_id = ".$cls." )";
				break;
			case 'field_models':
				$table_name = 'fld_object_models';
				$cols = array(
					'pm_obj_id_0' => array('alias' => 'obj_id_0', 'escape' => true ),
					'pm_obj_id_1' => array('alias' => 'obj_id_1', 'escape' => true ),
					'pm_obj_id_2' => array('alias' => 'obj_id_2', 'escape' => true ),
					'pm_mdl_id' => array('alias' => 'mdl_id' ),
					);

				$cols_del = array(
					'id' => 'pm_obj_id_0', 'id_1' => 'pm_obj_id_1', 'id_2' => 'pm_obj_id_2'
					);
				$where_del = "pm_mdl_id in ( select mdl_id from fld_models where mdl_cls_id = ".$cls." )";
				break;
			case 'note_values':
				$table_name = 'fld_object_notes';
				$cols = array(
					'ono_id' => array('alias' => 'id'),
					'ono_cls_id' => array('alias' => 'cls_id' ),
					'ono_obj_id_0' => array('alias' => 'obj_id_0' ),
					'ono_obj_id_1' => array('alias' => 'obj_id_1' ),
					'ono_obj_id_2' => array('alias' => 'obj_id_2' ),
					'ono_name' => array('alias' => 'name', 'escape' => true ),
					'ono_content' => array('alias' => 'content', 'escape' => true ),
					'ono_date_created' => array('alias' => 'date_created_en', 'escape' => true ),
					'ono_date_modified' => array('alias' => 'date_modified_en', 'escape' => true ),
					'ono_usr_id' => array('alias' => 'usr_id'),
					);
				$cols_del = array(
					'id' => 'ono_obj_id_0', 'id_1' => 'ono_obj_id_1', 'id_2' => 'ono_obj_id_2'
					);
				$where_del = "ono_cls_id = ".$cls;
				break;
			case 'doc_values':
				$table_name = 'doc_objects';
				$cols = array(
					'dob_obj_id_0' => array('alias' => 'obj_id_0' ),
					'dob_obj_id_1' => array('alias' => 'obj_id_1' ),
					'dob_obj_id_2' => array('alias' => 'obj_id_2' ),
					'dob_cls_id' => array('alias' => 'cls_id' ),
					'dob_doc_id' => array('alias' => 'doc_id' ),
					);
				$cols_del = array(
					'id' => 'dob_obj_id_0', 'id_1' => 'dob_obj_id_1', 'id_2' => 'dob_obj_id_2'
					);
				$where_del = "dob_cls_id = ".$cls;
				break;
			case 'rel_values':
				$table_name = 'rel_relations_hierarchy';
				$cols = array(
					'rrh_rrt_id' => array('alias' => 'rrh_rrt_id' ),
					'rrt_src_cls' => array('alias' => 'rrt_src_cls' ),
					'rrh_src_0' => array('alias' => 'rrh_src_0' ),
					'rrh_src_1' => array('alias' => 'rrh_src_1' ),
					'rrh_src_2' => array('alias' => 'rrh_src_2' ),
					'rrt_dst_cls' => array('alias' => 'rrt_dst_cls' ),
					'rrh_dst_0' => array('alias' => 'rrh_dst_0' ),
					'rrh_dst_1' => array('alias' => 'rrh_dst_1' ),
					'rrh_dst_2' => array('alias' => 'rrh_dst_2' ),
					);

				if( $db_version >= 4977 ){
					$cols['rrh_level'] = array('alias' => 'rrh_level' );
				}

				$cols_del = array(
					'id' => 'rrh_src_0', 'id_1' => 'rrh_src_1', 'id_2' => 'rrh_src_2'
					);
				$where_del = "rrt_src_cls = ".$cls;
				break;
		}


		if( $table_name == '' ){
			error_log("fichier non implémenté: ".$k);
		}

		if( sizeof($cols) <= 0 && sizeof($cols_del) <= 0 ){
			error_log("La classe n'est pas configuré pour être fonctionnelle en mode bulk");
			exit;
		}

		if( $is_new_data ){ // ajout de donnée avec des replace into
			if( !$next && sizeof( $content ) > 0 ){

				if( sizeof($search_cols) > 0 ){
					$sql_head_search = 'insert into search_cache ('.implode(',',array_keys($search_cols)).') values ';
					$sql_final_search .= $sql_head_search;
				}

				$sql_head = 'replace into '.$table_name.' ('.implode(',',array_keys($cols)).') values ';

				$sql_final .= $sql_head;

				foreach( $content as $c ){
					if( $cpt>0 && $cpt%$pas == 0 ){
						$sql_final .= $query_separator.$sql_head;
						if( sizeof($search_cols) > 0 ){
							$sql_final_search .= $query_separator.$sql_head_search;
						}
						$cpt = 0;
					}elseif($cpt>0){
						$sql_final .= ',';
						if( sizeof($search_cols) > 0 ){
							$sql_final_search .= ',';
						}
					}
					$t = array();
					foreach( $cols as $action ){
						if( !array_key_exists($action['alias'],$c) && !isset($action['default']) ){
							print $k.'::::'.$action['alias'].'////';
							var_dump($c);
							exit;
						}

						$val = isset($c[$action['alias']]) ? $c[$action['alias']] : null;

						if( $val === null ){
							if( isset($action['default']) ){
								$t[] = $action['default'];
							}else{
								$t[] = 'null';
							}
							continue;
						}

						if( isset($action['func']) ){
							switch($action['func']){
								case 'dateheureparse' : $val = dateheureparse(str_replace(' à ', ' ', $val)); break;
							}
						}

						if( isset($action['escape']) && $action['escape'] ){
							$val = '\''.str_replace("'","''",$val).'\'';
						}
						if( isset($action['zero_is_null']) && $action['zero_is_null'] && $val == 0 ){
							$val = 'null';
						}

						$t[] = $val;
					}
					$sql_final .= '('.implode(',', $t).')';

					if( sizeof($search_cols) ){
						$t_search = array();
						foreach( $search_cols as $col_key => $col_name ){
							if( is_numeric($col_name) ){
								$t_search[] = $col_name;
							}elseif( !is_array($col_name) && trim($col_name) == '' ){
								$t_search[] = '\'\'';
							}else{
								$val = '';

								if( is_array($col_name) ){
									foreach( $col_name as $cname ){
										$val .= $c[$cname].' ';
									}
								}else{
									$val = $c[$col_name];

									if(($col_name=='mobile' || $col_name == 'phone') && $val != ''){
										try{
											$phoneUtil = PhoneNumberUtil::getInstance();
											$phoneNumber = $phoneUtil->parse($val, isset($c["cnt_code"]) ? strtoupper($c["cnt_code"]):'FR');

											if ($phoneUtil->isValidNumber($phoneNumber)) {
												$val = $phoneUtil->format($phoneNumber, PhoneNumberFormat::E164);
											}
										}catch(Exception $e){}
									}
								}

								if( $val === null ){
									$t_search[] = '\'\'';
									continue;
								}else{
									$val = str_remove_accents($val);
									$val = strtolower($val);
								}
								if( in_array($col_key, array('sc_obj_id_0','sc_obj_id_1','sc_obj_id_2')) && is_numeric($val) ){
									$t_search[] = $val;
								}else{
									$t_search[] = '\''.str_replace("'", "''", $val).'\'';
								}

							}
						}
						$sql_final_search .= '('.implode(',', $t_search).')';
					}

					$cpt ++;
				}
				$sql_final .= $query_separator;

				if( $sql_final_search != '' ){
					$sql_final_search .= $query_separator;
				}
			}

			// ajout du fichier des nouveautés
			if( $sql_final != '' ){
				$filezip[$k.'.sql'] = isset($filezip[$k.'.sql']) ? $filezip[$k.'.sql'].$sql_final : $sql_final;
			}
			// ajout du fichier des nouveautés pour le moteur de recherche
			if( $sql_final_search != '' ){
				$filezip['search.sql'] = isset($filezip['search.sql']) ? $filezip['search.sql'].$sql_final_search : $sql_final_search;
			}

			// gestion de la suppression pour ce type de donnée
			$sql_final = '';
			$sql_final_search = '';
			if( isset($all_obj_ids) && sizeof( $cols_del ) > 0 ){

				// dans le cas ou l'objet principale ne dispose pas de plusieurs id on fait 1 seul requete avec un "in" pour gagner en perf.
				if( !isset($all_obj_ids[1][1]) ){
					$ids = array();
					$ids_alpha = array();
					foreach( $all_obj_ids as $id ){
						$ids[] = $id[0];
						$ids_alpha[] = '\''.$id[0].'\'';
					}

					// permet la suppression des tables liées
					if( isset($related_del) ){
						foreach( $related_del as $rdel ){
							$sql_final .= str_replace('[IDS]', implode(',', $is_id_alpha ? $ids_alpha : $ids), $rdel).$query_separator;
						}
					}

					$sql_final .= 'delete from '.$table_name.' where '.$cols_del['id'].' in ('.implode(',', $is_id_alpha ? $ids_alpha : $ids).') '.($where_del ? ' and '.$where_del : '').$query_separator;
					$sql_final_search .= 'delete from search_cache where sc_cls_id='.$cls.' and cast(sc_obj_id_0 as int) in ('.implode(',',$ids_alpha).')'.$query_separator;

					// total spé pour répondre à une besoin non standard sur les produits
					// dans le cas des colisages, sur les champs sur les conditionnements le prd_id est en deuxieme obj
					if( $cls == CLS_PRODUCT && $k == 'field_values' ){
						$sql_final .= 'delete from '.$table_name.' where pv_obj_id_1 in ('.implode(',', $is_id_alpha ? $ids_alpha : $ids).") and pv_fld_id in ( select fld_id from fld_fields where fld_cls_id = ".CLS_PRD_COLISAGE." )".$query_separator;
					}
				}else{
					foreach( $all_obj_ids as $id ){

						if( $is_id_alpha ){
							$w = ' '.$cols_del['id'].'=\''.$id[0].'\'';
						}else{
							$w = ' '.$cols_del['id'].'='.$id[0];
						}
						$w_s = ' cast(sc_obj_id_0 as int)='.$id[0];

						if( isset($id[1]) && $id[1] ){
							if( $is_id_alpha ){
								$w .= ' and '.$cols_del['id_1'].'=\''.$id[1].'\'';
							}else{
								$w .= ' and '.$cols_del['id_1'].'='.$id[1];
							}
							$w_s .= ' and cast(sc_obj_id_1 as int)='.$id[1];
						}
						if( isset($id[2]) && $id[2] ){
							if( $is_id_alpha ){
								$w .= ' and '.$cols_del['id_2'].'=\''.$id[2].'\'';
							}else{
								$w .= ' and '.$cols_del['id_2'].'='.$id[2];
							}
							$w_s .= 'and cast(sc_obj_id_2 as int)='.$id[2];
						}

						if( $where_del != '' ){
							$w .= ' and '.$where_del;
						}

						$sql_final .= 'delete from '.$table_name.' where '.$w.$query_separator;
						$sql_final_search .= 'delete from search_cache where sc_cls_id='.$cls.' and '.$w_s.$query_separator;
					}
				}

				// ajout du fichier pour les suppressions
				if( $sql_final != '' ){
					$filezip[$k.'_del.sql'] = isset($filezip[$k.'_del.sql']) ? $filezip[$k.'_del.sql'].$sql_final : $sql_final;
				}

				// ajout du fichier pour les suppressions du moteur de recherche
				if( sizeof($search_cols) && $sql_final_search != '' ){
					$filezip['search_del.sql'] = isset($filezip['search_del.sql']) ? $filezip['search_del.sql'].$sql_final_search : $sql_final_search;
				}

			}


		}else{ // suppression des données pour l'obj

			// dans le cas ou l'objet principale ne dispose pas de plusieurs id on fait 1 seul requete avec un "in" pour gagner en perf.
			if( !isset($cols_del['id_1']) ){

				$ids = array();
				$ids_alpha = array();
				foreach( $all_obj_ids as $id ){
					$ids[] = $id[0];
					$ids_alpha[] = '\''.$id[0].'\'';
				}

				$sql_final .= 'delete from '.$table_name.' where '.$cols_del['id'].' in ('.implode(',', $is_id_alpha ? $ids_alpha : $ids).') '.($where_del ? ' and '.$where_del : '').$query_separator;

			}else{
				foreach( $deleted as $d ){
					if( $is_id_alpha ){
						$w = $cols_del['id'].'=\''.$d[0].'\'';
					}else{
						$w = $cols_del['id'].'='.$d[0];
					}

					if( isset($d[1]) && $d[1] ){
						if( $is_id_alpha ){
							$w .= ' and '.$cols_del['id_1'].'=\''.$d[1].'\'';
						}else{
							$w .= ' and '.$cols_del['id_1'].'='.$d[1];
						}
					}
					if( isset($d[2]) && $d[2] ){
						if( $is_id_alpha ){
							$w .= ' and '.$cols_del['id_2'].'=\''.$d[2].'\'';
						}else{
							$w .= ' and '.$cols_del['id_2'].'='.$d[2];
						}
					}

					$sql_final .= 'delete from '.$table_name.' where '.$w.$query_separator;
				}
			}

			if( $sql_final != '' ){
				$filezip[$k.'.sql'] = isset($filezip[$k.'.sql']) ? $filezip[$k.'.sql'].$sql_final : $sql_final;
			}

			//suppresion des éléments du moteur de recherche même si pas de suppression on écrase tous
			if( isset($all_obj_ids) && $is_searchable ){

				// dans le cas ou l'objet principale ne dispose pas de plusieurs id on fait 1 seul requete avec un "in" pour gagner en perf.
				if( !isset($all_obj_ids[1][1]) ){
					$ids = array();
					foreach( $all_obj_ids as $id ){
						$ids[] = '\''.$id[0].'\'';
					}

					$sql_final_search .= 'delete from search_cache where sc_cls_id='.$cls.' and cast(sc_obj_id_0 as int) in ('.implode(',',$ids).')'.$query_separator;
				}else{
					foreach( $all_obj_ids as $id ){
						$w_s = 'cast(sc_obj_id_0 as int)='.$id[0];
						if( isset($id[1]) && $id[1] ){
							$w_s .= ' and cast(sc_obj_id_1 as int)='.$id[1];
						}
						if( isset($id[2]) && $id[2] ){
							$w_s .= ' and cast(sc_obj_id_2 as int)='.$id[2];
						}

						$sql_final_search .= 'delete from search_cache where sc_cls_id='.$cls.' and '.$w_s.$query_separator;
					}
				}

				// ajout du fichier pour les suppressions du moteur de recherche
				if( $sql_final_search != '' ){
					$filezip['search_del.sql'] = isset($filezip['search_del.sql']) ? $filezip['search_del.sql'].$sql_final_search : $sql_final_search;
				}
			}

		}

	}
}

/// \endcond
