<?php

/**	\file sineres-import.php
 *	\ingroup crontabs Sineres
 *
 *	Ce script permet d'importer les éléments à partir de sineres
 *	Attention toute la partie produits et stocks utilise le système d'import de riashop et n'ai pas traité ici.

 * 	Arguments : 
 *		1 - tnt_id 
 * 		2 - initialisation, si true il va créer les reprénsentants
 *
 */
if (!isset($ar_params)) {
	die("L'exécution de ce script nécessite l'appel de execute-script.php." . PHP_EOL);
}

require_once( 'sineres.define.inc.php' );
require_once( 'prd.stocks.inc.php' );
require_once( 'tasks.inc.php' );

$init = false;
if( isset($ar_params['init']) && $ar_params['init'] == 'true' ){
	$init = true;
}

$errors = array();

// liste des tâches standards (hors ean et piece) dans l'ordre dans lesquelles elles doivent s'exécuter
// cette liste devrait pouvoir être configurée par locataire
$standard_task_list = array( SINERES_TASK_PRODUCTS, SINERES_TASK_STOCKS, SINERES_TASK_CLIENT, SINERES_TASK_CONDITIONS, SINERES_TASK_ORDERS);

foreach( $configs as $config ){
	// traitement uniquement pour la sodip/paban
	if( !isset($config['sync_global_gescom_type']) || $config['sync_global_gescom_type']!=GESCOM_TYPE_SINERES ){
		continue;
	}
	// charge le fichier de config du site principal
	if( !is_file( $config['site_dir'].'/config.inc.php' ) ){
		sineres_log('Fichier de configuration introuvable pour le locataire : '.$config['tnt_id']."\n");
		continue;
	}

	require_once( $config['site_dir'].'/config.inc.php' );

	$config['sineres_ftp_dir_import'] = $config['site_dir'].'/../ftp-sineres';

	// teste l'existence du dossier d'import
	if( !isset( $config['sineres_ftp_dir_import'] ) || !is_dir( $config['sineres_ftp_dir_import'] ) ){
		sineres_log('Sineres : Le dossier d\'import est introuvable ou non configuré');
		continue;
	}

	$codes_files = array(
		'secteur_activite' => 'secteur_activite.csv',
		'famille' => 'famille.csv',
		'sfamille' => 'sfamille.csv',
		'representant' => 'representant.csv',
		'tva' => 'code_taxe.csv',
		'categorie_remise_standard' => 'categorie_remise_standard.csv',
		'categorie_remise_negoce' => 'categorie_remise_negoce.csv',
		'code_conditionnement' => 'code_conditionnement.csv',
	);

	$file_errors = array();

	foreach($codes_files as $var => $file){
		$return = sineres_get_matching($file);

		if ($return === false ) {
			$file_errors[] = $file;
			continue;
		}

		$$var = $return;
	}

	if (!empty($file_errors)) {
		sineres_log('les fichiers ('.implode(', ', $file_errors). ') n\'existe pas pour le tenant : '.$config['tnt_id']);
		continue;
	}

	// initialisation de l'installation
	if( $init ){

		foreach( $representant as $code => $libelle ){
			// check si le représentant existe, si non on en ajoute un
			$rseller = gu_users_get(0, '', '', array(PRF_ADMIN, PRF_SELLER), '', 0, $code );
			if( !$rseller || !mysql_num_rows($rseller) ){

				$usr = gu_users_add( $code.'@yuto.com', null, PRF_SELLER, $code, true );
				if( $usr ){
					gu_users_set_seller_id($usr, $usr);
					$adr_id = gu_adresses_add( $usr, 3, null, $libelle, '' );
					gu_users_address_invoices_set($usr, $adr_id);
				}

			}
		}
		return;
	}

	//Initialisation collection d'object crosse task (product => stock)
	$product_collection = array();

	// traitement des fichiers extraits
	foreach( $standard_task_list as $task_id ){

		$file_link = '';
		$file_name = '';
		switch( $task_id ){
			// tâche permettant d'importer les clients
			case SINERES_TASK_CLIENT : {

				$dir = $config['sineres_ftp_dir_import'].'/users/';

				$files = sineres_get_oldest_file( $dir );

				if( !$files ){
					continue;
				}

				foreach( $files as $file_name ){
					$file_link = $dir.$file_name;

					if( !is_file( $file_link ) ){
						continue;
					}

					$sellers = array(); 

					// lecture du fichier ligne par ligne
					$cpt = -1;
					$handle = fopen( $file_link, "r" );
					if( $handle !== false ){
						while( ( $file_line = fgets( $handle ) ) !== false ){
							if( trim( $file_line ) == '' ){
								continue;
							}

							$cpt++;

							if( $cpt == 0 ){// retire la premiere ligne
								continue;
							}

							$sin_client = sineres_file_decode( $task_id, $file_line );

							if( $sin_client && sizeof( $sin_client ) ){
								foreach( $sin_client as $sclient ){

									// Les clients Paban débutant par P01F ou P01T ne doivent pas apparaître sur les tablettes (cf #25541)
									if( strpos($sclient['cod_client'], 'P01F')===0 || strpos($sclient['cod_client'], 'P01T')===0 ){
										$sclient['date_annulation'] = date('d/m/Y');
									}

									// teste si le client existe déjà sur riashop
									$rusr = gu_users_get( 0, '', '', 0, '', 0, $sclient['cod_client'] );
									if( $rusr && ria_mysql_num_rows( $rusr ) ){
										$cpt = 0;
										while( $r = ria_mysql_fetch_assoc($rusr) ){
											if( $r['can_login'] == 1 ){
												$cpt++;
											}
										}
										if( $cpt > 1 ){
											sineres_log($file_name.' - Traitement stoppé : Il existe '.ria_mysql_num_rows( $rusr ).' clients avec la référence '.$sclient['cod_client']);
											continue;
										}
										ria_mysql_data_seek($rusr, 0);
										$usr = ria_mysql_fetch_assoc( $rusr );
										
										if( $sclient['date_annulation'] != '' ){
											// suppression du client 
											gu_users_del($usr['id']);
											continue;
										}
									}else{
										if( $sclient['date_annulation'] != '' ) continue; 

										$prf = PRF_CUST_PRO;
										$prc = 0;

										// si email déjà pris on met le pattern par défaut
										if( trim( $sclient['email'] ) == '' || !gu_users_check_email( $sclient['email'] ) ){ // si email vide ou déjà pris on met le pattern par défaut
											$sclient['email'] = str_replace('*', $sclient['cod_client'], $config['sync_sage_empty_email_parttern']);
										}

										// création de l'utilisateur
										$nid = gu_users_add( $sclient['email'], null, $prf, $sclient['cod_client'], true, $prc );
										if( !$nid ){
											sineres_log($file_name.' -  Erreur lors de la création du client '.$sclient['cod_client'] .' : email peut être utilisé');
											continue;
										}

										// récupère l'utilisateur pour voir les modifitications ensuite
										$rusr = gu_users_get( $nid );
										if( !$rusr || !ria_mysql_num_rows( $rusr ) ){
											sineres_log($file_name.' - Erreur lors de la récupération du client après la création '.$sclient['cod_client']);
											continue;
										}

										$usr = ria_mysql_fetch_assoc( $rusr );

									}

									$country = sys_countries_get_name( $sclient['pays'] );

									// Mise à jour de l'email
									if( trim( $sclient['email'] ) != '' && $usr['email'] != $sclient['email'] ){

										if( !gu_users_check_email( $sclient['email'], $usr['id'] ) ){ // si email déjà pris alors on met le pattern par défaut
											$sclient['email'] = str_replace('*', $sclient['cod_client'], $config['sync_sage_empty_email_parttern']);
										}

										if( !gu_users_update_email( $usr['id'], $sclient['email'] ) ){
											sineres_log($file_name.' - Erreur lors de la modification de \'adresse email '.$sclient['email'].' du client '.$sclient['cod_client']);
											continue;
										}

									}

									// Mise à jour de l'adresse de facturation
									$society_name = $sclient['politesse'];
									if( $society_name != '' ){
										$society_name .= ' ';
									}
									$society_name .= $sclient['nom'];
									$type = 2; // que des sociétés a priori

									if( $usr['adr_invoices'] > 0 ){

										if( !gu_adresses_update( $usr['id'], $usr['adr_invoices'], $type, '', '', '', $society_name, $sclient['siret'], $sclient['rue_1'], $sclient['rue_2'], $sclient['cod_postal'], $sclient['ville'], $country, $sclient['telephone'], $sclient['telecopie'], null, null, true, null, null, $sclient['pays'] ) ){
											sineres_log($file_name.' - Erreur lors de la mise à jour de l\'adresse de facturation du client '.$sclient['cod_client']);
											continue;
										}

									}else{
										// création de l'adresse de facturation
										$adr = gu_adresses_add( $usr['id'], $type, '', '', '', $society_name, $sclient['siret'], $sclient['rue_1'], $sclient['rue_2'], $sclient['cod_postal'], $sclient['ville'], $country, $sclient['telephone'], $sclient['telecopie'], '', '', '', '', $sclient['pays'] );

										if( !$adr ){
											sineres_log($file_name.' - Erreur lors de la création de l\'adresse de facturation du client '.$sclient['cod_client']);
											continue;
										}

										// affectation de l'adresse de facturation au client
										if( !gu_users_address_invoices_set( $usr['id'], $adr ) ){
											sineres_log($file_name.' - Erreur lors de l\'affectation de l\'adresse de facturation du client '.$sclient['cod_client']);
											continue;
										}

									}

									// mise à jour de l'encours du client
									/*
									$sclient['ca_encours'] = str_replace( ',', '', $sclient['ca_encours'] );
									if( !gu_users_set_encours( $usr['id'], $sclient['ca_encours'] ) ){
										sineres_log($file_name.' - Erreur lors de la mise à jour de l\'encours client '.$sclient['cod_client']);
										continue;
									}
									*/

									// mise à jour du code ape
									if( !gu_users_set_naf( $usr['id'], $sclient['ape'] ) ){
										sineres_log($file_name.' - Erreur lors de la mise à jour du naf client '.$sclient['cod_client']);
										continue;
									}

									// mise à jour du taxecode
									if( !gu_users_set_taxcode( $usr['id'], $sclient['affiliation_tva'] ) ){
										sineres_log($file_name.' - Erreur lors de la mise à jour du taxcode client '.$sclient['cod_client']);
										continue;
									}

									// gestion du code blocage du client
									if( $sclient['cod_blocage'] == '3' ){ // client fermé
										if( !gu_users_set_is_locked( $usr['id'], true ) ){
											sineres_log($file_name.' - Erreur lors du passage du client en bloqué '.$sclient['cod_client']);
											continue;
										}
									}else{ // sinon repasse le client en non bloqué
										if( $usr['is_locked'] ){
											if( !gu_users_set_is_locked( $usr['id'], false ) ){
												sineres_log($file_name.' - Erreur lors du passage du client en débloqué '.$sclient['cod_client']);
												continue;
											}
										}
									}


									$rDps = prd_deposits_get( null, null, $sclient['cod_succursale'], null, null );

									if( $rDps && ria_mysql_num_rows($rDps) ){
										$dps = ria_mysql_fetch_assoc($rDps);

										gu_users_update_dps( $usr['id'], $dps['id'] );
									}


									// gestion de la nature du client
									if( $sclient['nature_client'] == 'P' ){ // cas d'un client particulier changement de profile
										if( !gu_users_set_profile( $usr['id'], PRF_CUSTOMER ) ){
											sineres_log($file_name.' - Erreur lors du changement de profil du client en particulier '.$sclient['cod_client']);
											continue;
										}else{
											if( $usr['prf_id'] == PRF_CUSTOMER ){
												if( !gu_users_set_profile( $usr['id'], PRF_CUST_PRO ) ){
													sineres_log($file_name.' - Erreur lors du changement de profil du client en pro '.$sclient['cod_client']);
													continue;
												}
											}
										}
									}

									// gestion de la catégorie tarifaires des clients : categorie_remise_negoce
									$prc_id = false;
									$rpricecat = prd_prices_categories_get( 0, true, $sclient['categorie_remise_negoce'] );
									if( $rpricecat && ria_mysql_num_rows( $rpricecat ) ){
										$pricecat = ria_mysql_fetch_assoc( $rpricecat );
										$prc_id = $pricecat['id'];
									}else{
										// création de la catégorie tarifaire - idéalement à ne pas faire sur l'import client
										$prc_id = prd_prices_categories_add( $sclient['categorie_remise_negoce'], false, true );
										if( !$prc_id ){
											sineres_log($file_name.' - Erreur lors de la création de la cat tarifaire : '.$sclient['cod_client']);
											continue;
										}
									}
									if( $prc_id ){
										// affectation du client sur la catégories tarifaires
										if( !gu_users_set_prc( $usr['id'], $prc_id ) ){
											sineres_log($file_name.' - Erreur lors de l\'affectation de la cat tarifaire : '.$sclient['cod_client']);
											continue;
										}
									}

									// affectation du représentant
									// attention tous les comerciaux non trouvé son ignoré et le client devient sans affectation
									$seller_id = 0;
									if( !isset($sellers[$sclient['representant']]) ){
										if( $sclient['representant'] != ''){
											$rseller = gu_users_get(0, '', '', array(PRF_ADMIN, PRF_SELLER), '', 0, $sclient['representant'] );
											if( $rseller && ria_mysql_num_rows($rseller) ){
												$seller = ria_mysql_fetch_assoc($rseller); 
												$seller_id = $seller['seller_id'];
												$sellers[$sclient['representant']] = $seller_id;
											}else{
												//sineres_log($file_name.' - Commercial non trouvé : '.$sclient['representant']);
												$sellers[$sclient['representant']] = 0;
											}
										}
									}else{
										$seller_id = $sellers[$sclient['representant']];
									}

									if( !gu_users_set_seller_id( $usr['id'], $seller_id ) ){
										sineres_log($file_name.' - Erreur lors de l\'affectation du commercial '.$seller_id.' sur le client '.$sclient['cod_client']);
										continue;
									}

									// ajout du moyen de paiement ( en compte par défaut )
									gu_users_payment_types_add( $usr['id'], _PAY_COMPTE, 0, 2, 0 );

									// marque le client comme sycnhronisé 
									gu_users_set_is_sync($usr['id'], true);

									// dépot de stockage ... cod_succursale'
									// ?? paiement ?? 'cod_blocage'
									// utile pour la facturation, pas d'interet côté yuto ? 'cod_client_facture'
									// catégorie de tarifs != de la catégorie tarifaire déja utilisé : pas utile ? 'categorie_remise_atelier'
									// non nécessaire ? 'frais_acheminement'
									// en attente 'famille_statcli'
									// print $sclient['cod_client']."\n";
								}
							}
						}
						fclose( $handle );

						// déplacement du fichier dans un dossier d'archive pour conserver un historique
						if( is_file( $file_link ) ){
							//suppression du fichier
							rename( $file_link, $dir.'archives/'.$file_name );
						}
					}
				}
				break;
			}
			// tâche permettant d'importer les produits
			case SINERES_TASK_PRODUCTS : {
				$dir = $config['sineres_ftp_dir_import'].'/products/';

				$files = sineres_get_oldest_file( $dir );

				if( !$files ){
					continue;
				}

				$brands_collection = array();
				$categories_collection = array();
				$sell_units_collection = array();

				$rCat = prd_categories_get_all();
				if( $rCat && ria_mysql_num_rows( $rCat ) ){
					while( $cat = ria_mysql_fetch_assoc( $rCat ) ){
						$key = (is_null( $cat['parent_id'] ) ? 0 : $cat['parent_id'] ).'-'.strtolower( $cat['name'] );
						$categories_collection[$key] = $cat;
					}
				}

				$rBrd = prd_brands_get();
				if( $rBrd && ria_mysql_num_rows( $rBrd ) ){
					while( $brd = ria_mysql_fetch_assoc( $rBrd ) ){
						$brands_collection[$brd['name']] = $brd['id'];
					}
				}

				$rSun = prd_sell_units_get();
				if( $rSun && ria_mysql_num_rows( $rSun ) ){
					while( $sun = ria_mysql_fetch_assoc( $rSun ) ){
						$sell_units_collection[$sun['name']] = $sun['id'];
					}
				}
				foreach( $files as $file_name ){
					$file_link = $dir.$file_name;

					if( !is_file( $file_link ) ){
						continue;
					}

					// lecture du fichier ligne par ligne

					$handle = fopen( $file_link, "r" );

					$colisage = array();
					if( !$handle ){
						continue;
					}
					$headers = fgetcsv( $handle, 0, ';' ); // on skip la première ligne des en tête du fichier
					$headers = array(
						'cod_four',
						'reference',
						'designation',
						'px_ach_net',
						'px_vte_maison',
						'cod_four_ach',
						'categ_rem_standard',
						'categ_rem_specif',
						'secteur_activite',
						'famille_stat',
						'sfamille_stat',
						'famille_gdevte',
						'contenance',
						'hauteur',
						'longueur',
						'largeur',
						'poids',
						'volume',
						'unite_hauteur',
						'unite_longueur',
						'unite_largeur',
						'unite_poids',
						'unite_contenance',
						'unite_volume',
						'type_cod_barre',
						'cod_barre',
						'reference_interne',
						'tva',
						'contenu_achat',
						'unite_achat',
						'contenu_vente',
						'unite_vente',
						'Epuisement',
						'cod_us',
						'code_ecotaxe',
						'code_tipp',
						'code_tgap',
						'frais_acheminement',
						'conditionnement',
						'follow_stock',
						);
					$count = 1;
					while( $line = fgetcsv( $handle, 0, ';' ) ){
						$line = array_map('trim', $line);
						unset($line[count($line)-1]);
						$count++;
						if( count($headers) != count($line) ){
							sineres_log('Erreur ligne '.$count.' Nombre d\'en tête : '.count($headers).', nombre de colonne : '.count($line));
							continue;
						}
						$row = array_combine( $headers, $line );
						$new_md5 = md5(json_encode(array(
							$row['reference_interne'],
							$row['cod_four'],
							$row['reference'],
							$row['cod_four_ach'],
							$row['poids'],
							$row['hauteur'],
							$row['longueur'],
							$row['largeur'],
							$row['px_ach_net'],
							$row['px_vte_maison'],
							$row['designation'],
							$row['secteur_activite'],
							$row['famille_stat'],
							$row['sfamille_stat'],
							$row['cod_barre'],
							$row['tva'],
							$row['unite_vente'],
							$row['Epuisement'],
							$row['follow_stock'],
							$row['conditionnement'],
						)));
						$colisage_line = array();
						$colisage_line['ref'] = $row['cod_four'].'-'.$row['reference'];
						// Récupération de la marque depuis la collection
						if( !array_key_exists( $row['cod_four_ach'], $brands_collection ) ){
							$brd_id = prd_brands_add( $row['cod_four_ach'], '', '', true, '', true );
							if ($brd_id) {
								$brands_collection[$row['cod_four_ach']] = $brd_id;
							}else{
								sineres_log($file_name.' - Erreur ligne '.$count.' -  Erreur lors de l\'ajout de la marque pour le produit '.$row['reference_interne'] .'.');
							}
						}else{
							$brd_id = $brands_collection[$row['cod_four_ach']];
						}

						// formatage des nombres
						$row['poids'] = str_replace(',', '.', $row['poids']);
						$row['hauteur'] = str_replace(',', '.', $row['hauteur']);
						$row['longueur'] = str_replace(',', '.', $row['longueur']);
						$row['largeur'] = str_replace(',', '.', $row['largeur']);
						$row['px_ach_net'] = str_replace(',', '.', $row['px_ach_net']);
						$row['px_vte_maison'] = str_replace(',', '.', $row['px_vte_maison']);

						$prd_was_created = false;

						if (!array_key_exists($row['reference_interne'], $product_collection)) {
							$sql = '
								select
									prd_id as id
								from prd_products
								where prd_tnt_id=' . $config['tnt_id'] . '
									and prd_date_deleted is null
									and prd_ref_gescom="'.addslashes($row['reference_interne']).'"
							';
							// essai de récupération
							$r_prd = ria_mysql_query($sql);
							// création du produit
							if (!$r_prd || !ria_mysql_num_rows($r_prd)) {
								// creation du produit
								$prd_id = prd_products_add(
									($row['cod_four'].'-'.$row['reference']),
									$row['designation'],
									'',
									$brd_id,
									true,
									$row['poids'],
									$row['longueur'],
									$row['largeur'],
									$row['hauteur'],
									'',
									true
								);
								if (!$prd_id) {
									sineres_log($file_name.' - Erreur ligne '.$count.' -  Erreur lors de la création du produit '.$row['reference_interne'] .'.');
									continue;
								}
								if (!prd_products_set_ref_gescom($prd_id, $row['reference_interne'], true)) {
									sineres_log($file_name.' - Erreur ligne '.$count.' -  Erreur lors de l\'attribution de la référence gescom du produit '.$row['reference_interne'] .'.');
									continue;
								}
								$product_collection[$row['reference_interne']] = array(
									'id' => $prd_id,
								);
								$prd_was_created = true;
							}else{
								// récupération de la base
								$p = ria_mysql_fetch_assoc($r_prd);
								$product_collection[$row['reference_interne']] = $p;
							}
						}
						$prd_id = $product_collection[$row['reference_interne']]['id'];

						// vérification du md5
						if (!isset($product_collection[$row['reference_interne']]['lastMd5'])) {
							$r_last_md5 = tsk_md5_get(CLS_PRODUCT, $prd_id);
							if ($r_last_md5 && ria_mysql_num_rows($r_last_md5)) {
								$tmp = ria_mysql_fetch_assoc($r_last_md5);
								$product_collection[$row['reference_interne']]['lastMd5'] = $tmp['md5'];
							}
						}
						if ( isset($product_collection[$row['reference_interne']]['lastMd5']) && $product_collection[$row['reference_interne']]['lastMd5'] == $new_md5) {
							continue;
						}else{
							tsk_md5_add(CLS_PRODUCT, $prd_id, $new_md5);
							$product_collection[$row['reference_interne']]['lastMd5'] = $new_md5;
						}
						// prix acheté
						if (!isset($product_collection[$row['reference_interne']]['px_ach_net']) ||$product_collection[$row['reference_interne']]['px_ach_net'] != $row['px_vte_maison']) {
							if (!prd_products_set_purchase_avg($prd_id, $row['px_ach_net'])) {
								sineres_log($file_name.' - Erreur ligne '.$count.' : Erreur lors de la création du produit '.$row['reference_interne'] .'.');
							}else{
								$product_collection[$row['reference_interne']]['px_ach_net'] = $row['px_ach_net'];
							}
						}
						// tarifs
						if (!isset($product_collection[$row['reference_interne']]['price']) ||$product_collection[$row['reference_interne']]['price'] != $row['px_vte_maison']) {
							$rPrice = prc_prices_get( 0, 1, false, false, false, $prd_id, false, false, false, null, true, 1, 1, false, 'none', array(), array(), null, false, 0, null, 0, 0, false, 0 );

							if ($rPrice && ria_mysql_num_rows($rPrice)) {
								// update
								$prc = ria_mysql_fetch_assoc( $rPrice );
								if (!prc_prices_update( $prc['id'], 1, $row['px_vte_maison'], null, null, 1, $prd_id )) {
									sineres_log($file_name.' - Erreur ligne '.$count.' : Erreur lors de la mise à jour du tarif produit '.$row['reference_interne'] .'.');
								}else{
									$product_collection[$row['reference_interne']]['price'] = $row['px_vte_maison'];
								}
							}else{
								// ajout
								$prc_id = prc_prices_add(1, $row['px_vte_maison'], '1000-01-01 00:00:00', '9999-12-31 23:59:59', 1, $prd_id, 0, true, true, '', array(), false, false);
								if (!$prc_id) {
									sineres_log($file_name.' - Erreur ligne '.$count.' : Erreur lors de l\'ajout du tarif produit '.$row['reference_interne'] .'.');
								}else{
									$product_collection[$row['reference_interne']]['price'] = $row['px_vte_maison'];
								}
							}
						}

						// traitement pour les catégories list($s, $f, $sf)
						$cats = sineres_get_categories_keys(
							array($row['secteur_activite'], $row['famille_stat'], $row['sfamille_stat']),
							array($secteur_activite, $famille, $sfamille )
						);
						$concat_cat = implode(';', $cats);

						if (!isset($product_collection[$row['reference_interne']]['classify']) ||$product_collection[$row['reference_interne']]['classify'] != $concat_cat) {
							$parent = $config['cat_root'];
							foreach ($cats as $cat) {
								$key = $parent.'-'.strtolower( $cat );
								// si la catégorie n'existe pas dans la collection on l'ajoute
								if( !array_key_exists( $key, $categories_collection )) {
									$cat_id = prd_categories_add(
										$cat, '', '', $parent, true, true, null
									);
									if ($cat_id) {
										$categories_collection[$key] = array(
											'id' => $cat_id,
										);
										$parent = $cat_id;
									}
								}else{
									$parent = $categories_collection[$key]['id'];
								}
							}
							if( !prd_classify_exists( $parent, $prd_id ) ){
								if( !prd_products_add_to_cat( $prd_id, $parent, true ) ){
									sineres_log($file_name.' - Erreur ligne '.$count.' : Erreur lors du classement du produit '.$row['reference_interne'] .'.');
								}else{
									$product_collection[$row['reference_interne']]['classify'] = $concat_cat;
								}
							}else{
								$product_collection[$row['reference_interne']]['classify'] = $concat_cat;
							}
						}
						//update du produit
						prd_products_update(
							$prd_id,
							($row['cod_four'].'-'.$row['reference']),
							$row['designation'],
							'',
							$brd_id,
							true,
							$row['poids'],
							$row['longueur'],
							$row['largeur'],
							$row['hauteur']
						);

						if (!isset($product_collection[$row['reference_interne']]['cod_barre']) ||$product_collection[$row['reference_interne']]['cod_barre'] != $row['cod_barre']) {
							if (!prd_products_update_barcode($prd_id, $row['cod_barre'])){
								sineres_log($file_name.' - Erreur ligne '.$count.' : Erreur lors de la mise à jours du code à barre '.$row['reference_interne'] .'.');
							}else{
								$product_collection[$row['reference_interne']]['cod_barre'] = $row['cod_barre'];
							}
						}

						// Traitement pour la tva
						if( array_key_exists($row['tva'], $tva) ){
							$tax = $tva[$row['tva']];
						}else{
							$tax = 0;
						}
						if ($tax == 'EXONERE') {
							if (!prc_tvas_del( 0, $prd_id)) {
								sineres_log($file_name.' - Erreur ligne '.$count.' : Erreur lors de l\'ajout de la tva. '.$row['reference_interne'] .'.');
							}
						}else{
							$rate = trim(str_replace(array('TVA', ',', '%'), array('', '.', ''), $tax))/100+1;
							if (!prc_tvas_add($rate, $prd_id, 0, true)) {
								sineres_log($file_name.' - Erreur ligne '.$count.' : Erreur lors de l\'ajout de la tva. '.$row['reference_interne'] .'.');
							}
						}

						// unité de vente
						$unite_vente = $code_conditionnement[$row['unite_vente']];
						if (!isset($product_collection[$row['reference_interne']]['unite_vente']) ||$product_collection[$row['reference_interne']]['unite_vente'] != $unite_vente) {
							$sun_id = false;
							if( !array_key_exists( $unite_vente, $sell_units_collection ) ){
								$sun_id = prd_sell_units_add( $unite_vente );
								if( $sun_id ){
									$sell_units_collection[$unite_vente] = $sun_id;
								}
							}else{
								$sun_id = $sell_units_collection[$unite_vente];
							}

							if (!prd_products_set_sell_unit($prd_id, $sun_id)) {
								sineres_log($file_name.' - Erreur ligne '.$count.' : Erreur lors de l\'ajout de l\'unité de vente : '.$unite_vente.' - ' . $row['reference_interne'] .'.');
							}else{
								$product_collection[$row['reference_interne']]['unite_vente'] = $unite_vente;
							}
						}

						// prd_sleep
						if (!isset($product_collection[$row['reference_interne']]['Epuisement']) ||$product_collection[$row['reference_interne']]['Epuisement'] != $row['Epuisement']) {
							if (!prd_products_set_sleep($prd_id, $row['Epuisement'] == '1' ? true: false)) {
								sineres_log($file_name.' - Erreur ligne '.$count.' : Erreur lors de la définition produit en sommeil . '.$row['reference_interne'] .'.');
							}else{
								$product_collection[$row['reference_interne']]['Epuisement'] = $row['Epuisement'];
							}
						}
						// suivie en stock
						if (!isset($product_collection[$row['reference_interne']]['follow_stock']) ||$product_collection[$row['reference_interne']]['follow_stock'] != $row['follow_stock']) {
							if (!prd_products_set_follow_stock($prd_id, trim($row['follow_stock']) == 'O' ? true: false)) {
								sineres_log($file_name.' - Erreur ligne '.$count.' : Erreur lors de la définition suivie en stock. '.$row['reference_interne'] .'.');
							}else{
								$product_collection[$row['reference_interne']]['follow_stock'] = $row['follow_stock'];
							}
						}
						// Traitement pour contenu achat et contenu vente
						$colisage_line['qte'] = $row['conditionnement'];
						$colisage_line['label'] = $row['conditionnement'];
						$colisage[] = $colisage_line;
					}
					fclose( $handle );

					foreach( $colisage as $col ){

						$prd_id = prd_products_get_id($col['ref']);
						if( !$prd_id ){
							continue;
						}
						if( $col['qte'] == 1 || $col['qte'] == 0 ){
							if( !prd_colisage_classify_del_all( $prd_id ) ){
								sineres_log('Erreur de suppression du conditionnement sur le produit : '.$prd_id);
							}
							continue;
						}

						$rCol = prd_colisage_classify_get( 0, $prd_id );

						if( $rCol || ria_mysql_num_rows($rCol) ){
							$cl = ria_mysql_fetch_assoc($rCol);
							prd_colisage_types_update( $cl['id'], false, $col['qte']);
						}else{
							if( $col_id = prd_colisage_types_add( $col['label'], $col['qte'] ) ){
								prd_colisage_classify_upd( $col_id, $prd_id );
							}else{
								sineres_log('Erreur lors de la création d\'un conditionnement pour le produit :' .$prd_id);
								continue;
							}
						}
					}

					// déplacement du fichier dans un dossier d'archive pour conserver un historique
					if( is_file( $file_link ) ){
						//suppression du fichier
						rename( $file_link, $dir.'archives/'.$file_name );
					}
				}
				break;
			}
			// tâche permettant d'importer les stocks
			case SINERES_TASK_STOCKS : {

				$dir = $config['sineres_ftp_dir_import'].'/stocks/';

				$files = sineres_get_oldest_file( $dir );

				if( !$files ){
					continue;
				}

				// récupère toutes les dépots de la base et fait un tableau
				$deposit_collection = array();
				$rdps = prd_deposits_get();
				if( $rdps ){
					while( $d = ria_mysql_fetch_assoc($rdps) ){
						if (trim($d['ref'])) {
							$deposit_collection[$d['ref']] = $d['id'];
						}
					}
				}
				$stock_collection = array();

				foreach( $files as $file_name ){
					$file_link = $dir.$file_name;

					if( !is_file( $file_link ) ){
						continue;
					}

					// Il arrive que certains fichiers soient vides, ne pas générer de warning pour autant
					if( filesize( $file_link )==0 ){
						continue;
					}

					// lecture du fichier ligne par ligne

					$handle = fopen( $file_link, "r" );

					if( !$handle ){
						continue;
					}

					$headers = fgetcsv( $handle, 0, ';' );
					if( $headers=='' ){
						continue;
					}

					if (!is_array($headers)) {
						sineres_log($file_name.' - Erreur l\'en-tête du fichier CSV n\'est pas bien formatée.');
						continue;
					}
					$headers = array_map('trim', $headers);

					$row_count = 0;
					while( $l = fgets($handle) ){
						$line = explode(';', $l);
						$line = array_map('trim', $line);
						$row_count++;

						if( count($headers) != count($line)) {
							sineres_log($file_name.' - Ligne '.$row_count.' - Le nombre de colonnes de la ligne ('.count($line).') ne correspond pas au nombre de colonnes de l\'entête ('.count($headers).').');
							continue;
						}

						$row = array_combine( $headers, $line );

						$new_md5 = md5(json_encode(array(
							$row['code_succursale'],
							$row['reference_interne'],
							$row['stock_physique']
						)));

						$key = $row['code_succursale'].'-'.$row['reference_interne'];
						if ( isset($stock_collection[$key]['lastMd5']) && $stock_collection[$key]['lastMd5'] == $new_md5) {
							continue;
						}

						// check si le dépot existe et le crée dans le cas contraire
						if (!array_key_exists($row['code_succursale'], $deposit_collection) ){
							$dps_id = prd_deposits_add( $row['code_succursale'], sizeof($deposit_collection) == 0, '', '', '', '', 'FRANCE', '', '', '', false, 1, $row['code_succursale'] );
							if ($dps_id) {
								$deposit_collection[$row['code_succursale']] = $dps_id;
							}else{
								sineres_log($file_name.' - Erreur création de la succursale '.$row['reference_interne']);
								continue;
							}
						}

						if (!array_key_exists($row['reference_interne'], $product_collection)) {
							$sql = '
								select
									prd_id as id
								from prd_products
								where prd_tnt_id=' . $config['tnt_id'] . '
									and prd_date_deleted is null
									and prd_ref_gescom="'.addslashes($row['reference_interne']).'"
							';
							// essai de récupération
							$r_prd = ria_mysql_query($sql);
							if (!$r_prd || !ria_mysql_num_rows($r_prd)) {
								//sineres_log($file_name.' - Erreur lors de la récupération du produit '.$row['reference_interne'] .'.');
								continue;
							}else{
								$product_collection[$row['reference_interne']] = ria_mysql_fetch_assoc($r_prd);
							}
						}

						$prd_id = $product_collection[$row['reference_interne']]['id'];
						$dps_id = $deposit_collection[$row['code_succursale']];

						// vérification du md5
						if (!isset($stock_collection[$key]['lastMd5'])) {
							$r_last_md5 = tsk_md5_get(CLS_STOCK, array($dps_id, $prd_id));
							if ($r_last_md5 && ria_mysql_num_rows($r_last_md5)) {
								$tmp = ria_mysql_fetch_assoc($r_last_md5);
								$stock_collection[$key]['lastMd5'] = $tmp['md5'];
							}
						}
						if ( isset($stock_collection[$key]['lastMd5']) && $stock_collection[$key]['lastMd5'] == $new_md5) {
							continue;
						}else{
							tsk_md5_add(CLS_STOCK, array($dps_id, $prd_id), $new_md5);
							$stock_collection[$key]['lastMd5'] = $new_md5;
						}
						$row['stock_physique'] = str_replace(',', '.', $row['stock_physique']);
						if (!prd_dps_stocks_update($prd_id, $dps_id, $row['stock_physique'], 0, 0, 0, 0, 0)) {
							sineres_log($file_name.' - Erreur lors de l\'ajout des stocks '.$row['reference_interne'] .'.');
						}
					}

					fclose( $handle );


					// déplacement du fichier dans un dossier d'archive pour conserver un historique
					if( is_file( $file_link ) ){
						//suppression du fichier
						rename( $file_link, $dir.'archives/'.$file_name );
					}
				}

				break;
			}
			// tâche permettant les tarifs
			case SINERES_TASK_CONDITIONS : {

				$dir = $config['sineres_ftp_dir_import'].'/prices/';

				$files = sineres_get_oldest_file( $dir );

				if( !$files ){
					continue;
				}

				// récupère l'ensemble des produits de la base pour un traitement plus rapide 
				$all_prds = array();
				$rproduct = prd_products_get_all();
				while( $p = ria_mysql_fetch_assoc($rproduct) ){
					$all_prds[$p['ref']] = $p['id'];
				}


				foreach( $files as $file_name ){
					$file_link = $dir.$file_name;

					if( !is_file( $file_link ) ){
						continue;
					}

					// lecture du fichier ligne par ligne
					$handle = fopen( $file_link, "r" );

					$headers_new = array(
						'cod_type',
						'cod_client',
						'cod_four',
						'reference',
						'type_div_rem',
						'categ_rem_neg',
						'categ_rem_standard',
						'categ_rem_spec',
						'secteur_activite',
						'famille_stat',
						'sfamille_stat',
						'date_debut',
						'date_fin',
						'nature',
						'p1_palier',
						'p1_remise1',
						'p1_remise2',
						'p1_prix',
						'p2_palier',
						'p2_remise1',
						'p2_remise2',
						'p2_prix',
						'p3_palier',
						'p3_remise1',
						'p3_remise2',
						'p3_prix',
						'p4_palier',
						'p4_remise1',
						'p4_remise2',
						'p4_prix',
						'date_creation',
						'date_modification',
						'date_annulation'
					);

					if( !$handle ){
						continue;
					}
					$headers = fgetcsv( $handle, 0, ';' );
					if (!is_array($headers)){
						sineres_log('Erreur l\'en-tête est mal formaté.');
						rename( $file_link, $dir.'archives/'.$file_name );
						continue;
					}

					$prd_cache = array();
					$prd_cache_invalid = array();
					$brd_cache = array();
					$prc_cache = array();
					$usr_cache = array();
					$usr_cache_invalid = array();
					$cat1_cache = array();
					$cat2_cache = array();
					$cat3_cache = array();

					$cpt = 0;
					while( $line = fgetcsv( $handle, 0, ';' ) ){
						$cpt ++;
						
						foreach( $line as $k => $l ){
							$line[$k] = trim( $l );
						}
						if (count($headers) != count($line)){
							sineres_log('Erreur l\'en-tête et la ligne non pas le même nombre d\'élément.');
							continue;
						}
						$row = array_combine( $headers, $line );

						$row['cod_soc'] = '1'; // temporaire car l'export fourni par cette information pour le moment

						// liste les palliers 
						$palliers = array();
						$palliers[] = array(
							'pallier' => $row['p1_palier'],
							'remise1' => $row['p1_remise1'],
							'remise2' => $row['p1_remise2'],
							'prix' => $row['p1_prix'],
							);
						if( $row['p2_palier'] > 0 ){
							$palliers[] = array(
								'pallier' => $row['p2_palier'],
								'remise1' => $row['p2_remise1'],
								'remise2' => $row['p2_remise2'],
								'prix' => $row['p2_prix'],
								);
						}
						if( $row['p3_palier'] > 0 ){
							$palliers[] = array(
								'pallier' => $row['p3_palier'],
								'remise1' => $row['p3_remise1'],
								'remise2' => $row['p3_remise2'],
								'prix' => $row['p3_prix'],
								);
						}
						if( $row['p4_palier'] > 0 ){
							$palliers[] = array(
								'pallier' => $row['p4_palier'],
								'remise1' => $row['p4_remise1'],
								'remise2' => $row['p4_remise2'],
								'prix' => $row['p4_prix'],
								);
						}

						$last_min_pallier = 1;
						foreach( $palliers as $pallier ){

							$price_key = $row['cod_type'].'-'.$row['cod_soc'].'-'.$row['cod_client'].'-'.$row['cod_four'].'-'.$row['reference'].'-'.$row['categ_rem_neg'].'-'.$row['categ_rem_standard'].'-'.$row['categ_rem_spec'].'-'.$row['secteur_activite'].'-'.$row['famille_stat'].'-'.$row['sfamille_stat'].'-'.$row['type_div_rem'].'-'.$pallier['pallier'];

							if( strlen($price_key) > 255 ){
								sineres_log( $file_name.' - Clé tarifs trop longue : '.$price_key);
								continue;
							}


							$prd_id = $prc_id = 0;
							$prd_ref = $row['cod_four'].'-'.$row['reference'];

							// récupère l'id du produit
							if( trim( $prd_ref ) != '-' && $row['reference'] != '' ){
								if( isset($all_prds[$prd_ref]) ){
									$prd_id = $all_prds[$prd_ref];
								}else{
									continue;
								}
							}

							// controle du code client 
							if( $row['cod_client'] != '' ){

								if( isset($usr_cache_invalid[$row['cod_client']]) ){
									continue;
								}else if( isset($usr_cache[$row['cod_client']]) ){
									$prc_id = $usr_cache[$row['cod_client']];
								}else{
									$rusr = gu_users_get(0, '', '', 0, '', 0, $row['cod_client']); 
									if( !$rusr || !ria_mysql_num_rows($rusr) ){
										$usr_cache_invalid[$row['cod_client']]=1;
										sineres_log( $file_name.' : '.$price_key.' : Tarifs code client non trouvé : '.$row['cod_client']);
										continue;
									}
									$usr_cache[$row['cod_client']] = $prc_id;
								}
							}

							// tente de récupérer un tarif avec la clé 
							$rprc = prc_prices_get(0,0,false, false, false, false, false, false, false, null, true, 1, 0, array('value' => $price_key, 'symbol' => '='));

							if( $row['date_annulation'] != '' ){ // si une date d'annulation à été fourni alors le tarifs doit être supprimé

								if( $rprc && ria_mysql_num_rows($rprc) ){
									$prc = ria_mysql_fetch_assoc($rprc);
									if( !prc_prices_del( $prc['id'] ) ){
										sineres_log( $file_name.' : '.$price_key.' : Tarifs erreur lors de la suppression : '.$price_key);
										continue;
									}
								}

							}else{

								// controlles du type de tarifs
								$prc_type = false;
								$prc_value = false;
								switch( trim( $row['nature'] ) ){
									case 'P' :
										$prc_type = DISCOUNT_PERCENT;
										$prc_value = 1-($pallier['remise1'] / 100);
										if( $pallier['remise2'] > 0 ){
											$prc_value = $prc_value * (1-($pallier['remise2'] / 100));
										}
										$prc_value = (1-$prc_value) * 100;
										break;
									case 'N' :
										$prc_type = NEW_PRICE;
										$prc_value = $pallier['prix'];
										break;
								}
								if( $prc_type == false ){
									sineres_log( $file_name.' : '.$price_key.' : Tarifs avec un type inconnue : '.$row['nature']);
									continue;
								}

								// vérification de la valeur
								$prc_value = str_replace(' ','', $prc_value);
								$prc_value = str_replace(',','.', $prc_value);
								if( !is_numeric( $prc_value ) ){
									sineres_log( $file_name.' : '.$price_key.' : Tarifs non valide : '.$prc_value);
									continue;
								}

								// controles des dates
								$date_start = $row['date_debut'] != '' ? $row['date_debut'] : null;
								if( $date_start != null && !isdate( $date_start ) ){
									sineres_log( $file_name.' : '.$price_key.' : Tarifs date début non valide : '.$date_start);
									continue;
								}
								$date_end = $row['date_fin'];
								if( !isdate( $date_end ) ){
									sineres_log( $file_name.' : '.$price_key.' : Tarifs date fin non valide : '.$date_end);
									continue;
								}
								if( $date_end == "2999-12-31" || $date_end == '31/12/2050' ){
									$date_end = null;
								}

								$qte = $last_min_pallier; // non géré par sineres
								$qte_max = $pallier['pallier'] != '99999' && is_numeric($pallier['pallier']) ? $pallier['pallier'] : false ;
								if( $qte_max ){
									$last_min_pallier = $qte_max;
								}

								// récupère la catégorie en fonction des données sineres
								$cat = $config['cat_root'];

								$sa = $fa = $sf = '';
								list($sa, $fa, $sf) = sineres_get_categories_keys(
									array($row['secteur_activite'], $row['famille_stat'], $row['sfamille_stat']),
									array($secteur_activite, $famille, $sfamille )
								);
								
								if( trim( $sa ) != '' && trim( $sa ) != $row['secteur_activite'] ){
									// tente de trouver une catégorie avec la référence sa
									if( isset($cat1_cache[$sa]) ){
										$cat = $cat1_cache[$sa];
									}else{
										$rcat_sa = prd_categories_get( 0, false, $config['cat_root'], $sa, false, false, null, false, array(), false, false, false, false, false, false, false, true );
										if( !$rcat_sa || !ria_mysql_num_rows( $rcat_sa ) ){
											$cat = prd_categories_add( $sa, '', '', $cat, true, true );
											if( !$cat ){
												sineres_log( $file_name.' : '.$price_key.' : Tarifs catégories SA non trouvé : '.$sa);
												continue;
											}
										}else{
											$cat_sa = ria_mysql_fetch_assoc( $rcat_sa );
											$cat = $cat_sa['id'];
										}
										$cat1_cache[$sa] = $cat;
									}

									if( trim( $fa ) != '' && trim( $fa ) != $row['secteur_activite'].$row['famille_stat'] ){
										// essaire de trouvé la catégorie de second niveau
										if( isset($cat2_cache[$fa]) ){
											$cat = $cat2_cache[$fa];
										}else{
											$rcat_fa = prd_categories_get( 0, false, $cat, $fa, false, false, null, false, array(), false, false, false, false, false, false, false, true );
											if( !$rcat_fa || !ria_mysql_num_rows( $rcat_fa ) ){
												// on ajoute la catégorie 
												$cat = prd_categories_add( $fa, '', '', $cat, true, true );
												if( !$cat ){
													sineres_log( $file_name.' : '.$price_key.' : Tarifs catégories FA non trouvé : '.$sa.' - '.$fa);
													continue;
												}
											}else{
												$cat_fa = ria_mysql_fetch_assoc( $rcat_fa );
												$cat = $cat_fa['id'];
											}
											$cat2_cache[$fa] = $cat;
										}

										if( trim( $sf ) != '' && trim( $sf ) != $row['secteur_activite'].$row['famille_stat'].$row['sfamille_stat'] ){
											// essaire de trouvé la catégorie de troisième niveau
											if( isset($cat3_cache[$sf]) ){
												$cat = $cat3_cache[$sf];
											}else{
												$rcat_sf = prd_categories_get( 0, false, $cat, $sf, false, false, null, false, array(), false, false, false, false, false, false, false, true );
												if( !$rcat_sf || !ria_mysql_num_rows( $rcat_sf ) ){
													$cat = prd_categories_add( $sf, '', '', $cat, true, true );
													if( !$cat ){
														sineres_log( $file_name.' : '.$price_key.' : Tarifs catégories SF non trouvé : '.$sa.' - '.$fa.' - '.$sf);
														continue;
													}
												}else{
													$cat_sf = ria_mysql_fetch_assoc( $rcat_sf );
													$cat = $cat_sf['id'];
												}
												$cat3_cache[$sf] = $cat;
											}
										}
									}
								}

								// marque
								$brd_id = 0;
								if( trim( $row['cod_four'] ) != '' ){
									if( isset($brd_cache[$row['cod_four']]) ){
										$brd_id = $brd_cache[$row['cod_four']];
									}else{
										$rbrd = prd_brands_get( 0, false, $row['cod_four'], '', false  ); // dernier param à mettre à true uand les marques seront sync
										if( !$rbrd || !ria_mysql_num_rows( $rbrd ) ){
											//sineres_log( $file_name.' : '.$price_key.' : Tarifs fournisseur non trouvé : '.$row['cod_four']);
											continue;
										}
										$brd = ria_mysql_fetch_assoc( $rbrd );
										$brd_id = $brd['id'];
										$brd_cache[$row['cod_four']] = $brd_id;
									}
								}

								// cat tarifaire
								$prc_id = 0;
								if( $row['categ_rem_neg'] != '' ){
									if( isset($prc_cache[$row['categ_rem_neg']]) ){
										$prc_id = $prc_cache[$row['categ_rem_neg']];
									}else{
										$rpricecat = prd_prices_categories_get( 0, true, $row['categ_rem_neg'] );
										if( !$rpricecat || !ria_mysql_num_rows( $rpricecat ) ){
											// ajout de la categorie tarifaire en bdd
											$prc_id = prd_prices_categories_add( $row['categ_rem_neg'], false, true );
											if( !$prc_id ){
												sineres_log( $file_name.' : '.$price_key.' : Tarifs catégories tarifaire non trouvé : '.$row['categ_rem_neg']);
												continue;
											}
										}else{
											$pricecat = ria_mysql_fetch_assoc( $rpricecat );
											$prc_id = $pricecat['id'];
										}
										$prc_cache[$row['categ_rem_neg']] = $prc_id;
									}
								}


								// switch sur le type de condition

								// CL = Catégorie de remise Client = catégories tarifaires ria
								// AR = Catégorie de remise standard Article = champ avance : 4241 - _FLD_SINERES_CAT_REM_STD
								// RS = Catégorie de remise spécifique Article = champ avance : 4242 - _FLD_SINERES_CAT_REM_SPE
								// FA = Famille Article = catéogries produits
								// SF = Sous-famille Article = catéogries produits

								// Prix	Prix de base
								// CP	Condition particulière
								// CG	Condition générale

								// Fournisseur = marque ria

								$sineres_cond = array();
								switch( $row['cod_type'] ){
									case 'C0': //Prix - Prix de base vente

										sineres_log( $file_name.' : '.$price_key.' : Tarifs Prix - Prix de base vente :  non géré');
										continue;

										break;
									case 'C1': //Prix - Prix Promotion

										// dans le cas d'un prix promo, un produit doit forcement être sur un produit 
										if( $prd_id == 0 ){
											sineres_log( $file_name.' : '.$price_key.' : Tarifs Prix - Prix de promotion sans produit non géré');
											continue;
										}

										$sineres_cond = array(
											_FLD_SINERES_PROMO => array(
												'symbol' => '=',
												'value'  => 'oui'
											)
										);

										// ajout du champ avancé exclu promo
										if( $row['date_annulation'] != '' ){
											fld_object_values_set( $prd_id, _FLD_SINERES_PROMO, 'Oui');
										}else{
											fld_object_values_set( $prd_id, _FLD_SINERES_PROMO, '');
										}

										break;
									case 'C2': //Prix - Prix Tarif annexe

										// dans le cas d'un prix promo, un produit doit forcement être sur un produit 
										if( $prd_id == 0 ){
											sineres_log( $file_name.' : '.$price_key.' : Tarifs Prix - Prix de annexe sans produit non géré');
											continue;
										}

										$sineres_cond = array(
											_FLD_SINERES_EXCLUS => array(
												'symbol' => '=',
												'value'  => 'oui'
											)
										);

										// ajout du champ avancé exclu promo
										if( $row['date_annulation'] != '' ){
											fld_object_values_set( $prd_id, _FLD_SINERES_EXCLUS, 'Oui');
										}else{
											fld_object_values_set( $prd_id, _FLD_SINERES_EXCLUS, '');
										}
										break;
									case 'C6': //CP - Client/Référence article

										if( $row['cod_client'] == '' || $prd_id == 0 ){
											sineres_log( $file_name.' : '.$price_key.' : Tarifs CP - Client/Référence article : ne respecte pas la règle');
											continue;
										}

										$sineres_cond = array(
											_FLD_USR_REF => array(
												'symbol' => '=',
												'value'  => $row['cod_client']
											)
										);

										break;
									case 'CB': //CP - Client/Fournisseur/SAFASF

										if( $row['cod_client'] == '' || $cat == 0 || $brd_id == 0 ){
											sineres_log( $file_name.' : '.$price_key.' : Tarifs CP - Client/Fournisseur/SAFASF : ne respecte pas la règle');
											continue;
										}

										$sineres_cond = array(
											_FLD_USR_REF         => array(
												'symbol' => '=',
												'value'  => $row['cod_client']
											),
											_FLD_PRD_BRD_ID      => array(
												'symbol' => '=',
												'value'  => $brd_id
											)
										);

										break;
									case 'CA': //CP - Client/Fournisseur

										if( $row['cod_client'] == '' || $brd_id == 0 ){
											sineres_log( $file_name.' : '.$price_key.' : Tarifs CP - Client/Fournisseur : ne respecte pas la règle');
											continue;
										}

										$sineres_cond = array(
											_FLD_USR_REF    => array(
												'symbol' => '=',
												'value'  => $row['cod_client']
											),
											_FLD_PRD_BRD_ID => array(
												'symbol' => '=',
												'value'  => $brd_id
											)
										);

										break;
									case 'C3': //CP - Client/SAFASF

										if( $row['cod_client'] == '' || $cat == 0 ){
											sineres_log( $file_name.' : '.$price_key.' : Tarifs CP - Client/SAFASF : ne respecte pas la règle');
											continue;
										}

										$sineres_cond = array(
											_FLD_USR_REF         => array(
												'symbol' => '=',
												'value'  => $row['cod_client']
											)
										);

										break;
									case 'C7': //CP - Client/RS

										if( $row['cod_client'] == '' || $row['categ_rem_spec'] == '' ){
											sineres_log( $file_name.' : '.$price_key.' : Tarifs CG - CP - Client/RS : ne respecte pas la règle');
											continue;
										}

										$sineres_cond = array(
											_FLD_USR_REF             => array(
												'symbol' => '=',
												'value'  => $row['cod_client']
											),
											_FLD_SINERES_CAT_REM_SPE => array(
												'symbol' => '=',
												'value'  => $row['categ_rem_spec']
											)
										);

										break;
									case 'CJ': //CG - CL/Référence article

										if( $prc_id == 0 || $prd_id == 0 ){
											sineres_log( $file_name.' : '.$price_key.' : Tarifs CG - CL/Référence article : ne respecte pas la règle');
											continue;
										}

										$sineres_cond = array(
											_FLD_USR_PRC => array( 'symbol' => '=', 'value' => $prc_id )
										);

										break;
									case 'CD': //CG - CL/Fournisseur/SAFASF

										if( $prc_id == 0 || $cat == 0 || $brd_id == 0 ){
											sineres_log( $file_name.' : '.$price_key.' : Tarifs CG - CL/Fournisseur/SAFASF : ne respecte pas la règle');
											continue;
										}

										$sineres_cond = array(
											_FLD_USR_PRC         => array(
												'symbol' => '=',
												'value'  => $prc_id
											),
											_FLD_PRD_BRD_ID      => array(
												'symbol' => '=',
												'value'  => $brd_id
											)
										);

										break;
									case 'CK': //CG - CL/Fournisseur/AR

										if( $prc_id == 0 || $row['categ_rem_standard'] == '' || $brd_id == 0 ){
											sineres_log( $file_name.' : '.$price_key.' : Tarifs CG - CL/Fournisseur/AR : ne respecte pas la règle');
											continue;
										}

										$sineres_cond = array(
											_FLD_USR_PRC             => array(
												'symbol' => '=',
												'value'  => $prc_id
											),
											_FLD_PRD_BRD_ID          => array(
												'symbol' => '=',
												'value'  => $brd_id
											),
											_FLD_SINERES_CAT_REM_STD => array(
												'symbol' => '=',
												'value'  => $row['categ_rem_standard']
											)
										);

										break;
									case 'CC': //CG - CL/Fournisseur

										if( $prc_id == 0 || $brd_id == 0 ){
											sineres_log( $file_name.' : '.$price_key.' : Tarifs CG - CL/Fournisseur/ : ne respecte pas la règle');
											continue;
										}

										$sineres_cond = array(
											_FLD_USR_PRC    => array(
												'symbol' => '=',
												'value'  => $prc_id
											),
											_FLD_PRD_BRD_ID => array(
												'symbol' => '=',
												'value'  => $brd_id
											)
										);

										break;
									case 'CE': //CG - CL/SAFASF

										if( $prc_id == 0 || $cat == 0 ){
											sineres_log( $file_name.' : '.$price_key.' : Tarifs CG - CL/SAFASF : ne respecte pas la règle');
											continue;
										}

										$sineres_cond = array(
											_FLD_USR_PRC         => array(
												'symbol' => '=',
												'value'  => $prc_id
											)
										);

										break;
									case 'C4': //CG - CL/AR

										if( $prc_id == 0 || $row['categ_rem_standard'] == '' ){
											sineres_log( $file_name.' : '.$price_key.' : Tarifs CG - CL/SAFASF : ne respecte pas la règle');
											continue;
										}

										$sineres_cond = array(
											_FLD_USR_PRC             => array(
												'symbol' => '=',
												'value'  => $prc_id
											),
											_FLD_SINERES_CAT_REM_STD => array(
												'symbol' => '=',
												'value'  => $row['categ_rem_standard']
											)
										);

										break;
									case 'CF': //CP - Générale, Client/SAFASF

										sineres_log( $file_name.' : '.$price_key.' : Tarifs CP - Générale, Client/SAFASF : non implémenté');
										continue;

										break;
								}


								if( !$rprc || !ria_mysql_num_rows($rprc) ){
									// ajout
									if( !prc_prices_add( $prc_type, $prc_value, $date_start, $date_end, $qte, $prd_id, $cat, false, true, $price_key, $sineres_cond, $qte_max)){
										sineres_log( $file_name.' : '.$price_key.' : Tarifs erreur lors de l\'ajout : '.$price_key);
										continue;
									}
								}else{
									// mise à jour
									$prc = ria_mysql_fetch_assoc($rprc);
									$price_id = $prc['id'];

									if( !prc_prices_update( $price_id, $prc_type, $prc_value, $date_start, $date_end, $qte, $prd_id, $cat, false, $price_key, $qte_max ) ){
										sineres_log( $file_name.' : '.$price_key.' : Tarifs erreur lors de la mise à jour : '.$price_key);
										continue;
									}

									// récupère les conditions pour ce tarifs
									$rcnd = prc_price_conditions_get($price_id);
									if( $rcnd && ria_mysql_num_rows($rcnd) ){
										$sineres_cond_finded = array();
										while( $cnd = ria_mysql_fetch_assoc($rcnd) ){

											$find_cnd = false;
											foreach( $sineres_cond as $fld => $val ){
												if( $cnd['fld'] == $fld ){ // si même champs alors on peut tenter une mise de la cond
													$find_cnd = true;
													$sineres_cond_finded[] = $fld;

													// condition à mettre à jour
													if( !prc_price_conditions_upd( $price_id, $fld, $val['value'], $val['symbol'] ) ){
														sineres_log( $file_name.' : '.$price_key.' : Tarifs echec de mise à jour d\'une condition sur un tarif '.$price_id. ' : '.$fld.' : '.print_r($val, true));
														prc_prices_del( $price_id ); // ne conserve pas le tarifs car si les conditions sont faussé les calculs le seront
														continue;
													}
												}
											}

											if( !$find_cnd ){
												// condition à supprimer
												if( !prc_price_conditions_del( $price_id, $cnd['fld'] ) ){
													sineres_log( $file_name.' : '.$price_key.' : Tarifs echec de mise à jour d\'une condition sur un tarif '.$price_id. ' : '.$cnd['fld']);
														prc_prices_del( $price_id ); // ne conserve pas le tarifs car si les conditions sont faussé les calculs le seront
													continue;
												}
											}
										}

										// toutes les nouvelles conditions sont a ajouter
										foreach( $sineres_cond as $fld => $val ){
											if( !in_array($fld, $sineres_cond_finded) ){
												if( !prc_price_conditions_add( $price_id, $fld, $val['value'], $val['symbol'] ) ){
													sineres_log( $file_name.' : '.$price_key.' : Tarifs echec de création d\'une condition sur un tarif '.$price_id. ' : '.$fld.' : '.print_r($val, true));
													prc_prices_del( $price_id ); // ne conserve pas le tarifs car si les conditions sont faussé les calculs le seront
													continue;
												}
											}
										}

									}else{
										// aucune conditions normale ?
									}
								}
							}

						}
					}

					fclose( $handle );

					// déplacement du fichier dans un dossier d'archive pour conserver un historique
					if( is_file( $file_link ) ){
						//suppression du fichier
						rename( $file_link, $dir.'archives/'.$file_name );
					}
				}

				break;
			}
			// tâche permettant d'importer les commandes
			case SINERES_TASK_ORDERS :{

				$dir = $config['sineres_ftp_dir_import'].'/orders/';

				$files = sineres_get_oldest_file( $dir );

				if( !$files ){
					continue;
				}

				foreach( $files as $file_name ){
					$file_link = $dir.$file_name;

					if( !is_file( $file_link ) ){
						continue;
					}

					// lecture du fichier ligne par ligne
					$handle = fopen( $file_link, "r" );

					$headers_new = array(
						'cod_suclivr',
						'nature_document',
						'no_document',
						'no_sequence',
						'cod_client',
						'avoir',
						'representant',
						'frais_acheminement',
						'escompte',
						'facture_no',
						'date_facturation',
						'no_commande_ori',
						'ref_commande_lib',
						'ref_commande_date',
						'ref_retour_lib',
						'ref_retour_date',
						'cli_retour_lib',
						'cli_retour_date',
						'cod_four',
						'reference',
						'reference_interne',
						'designation',
						'secteur_activite',
						'famille_stat',
						'sfamille_stat',
						'qte_commandee',
						'qte_cumul_livr',
						'pump',
						'px_achat',
						'montant_tva',
						'total_ht_vente',
						'total_ht_achat',
						'cde_date_preparat',
						'cde_delai_demande',
						'date_creation',
						'date_modification'
					);

					if( !$handle ){
						continue;
					}

					// tableau qui stock temporairement la liste des pièces traités
					// une pièce présente commence par être vidé de ces lignes des produits puis recréé sauf si elle est présente dans ce tableau
					// dans le fichier chaque piece est intégralement envoyé par sineres
					$doc_order_pieces = array();
					$doc_bl_pieces = array();
					$doc_inv_pieces = array();

					$headers = fgetcsv( $handle, 0, ';','@' );// @ pas propre mais le fichier en entré est bof

					if( !is_array($headers) ){
						sineres_log( $file_name.' : le fichier est vide ou mal formaté');
						rename( $file_link, $dir.'archives/'.$file_name );
						continue;
					}
					$headers = array_map('trim', $headers);

					while( $line = fgetcsv( $handle, 0, ';','@') ){// @ pas propre mais le fichier en entré est bof
						$line = array_map('trim', $line);

						if( sizeof($line) != sizeof($headers) ){
							sineres_log( $file_name.' : '.$row['no_document'].' : ligne non valide en taille '.sizeof($line).': '.implode(":", $line));
							continue;
						}

						$row = array_combine( $headers, $line );

						// test le code client
						if( $row['cod_client'] == '' ){
							continue;
						}

						$rusr = gu_users_get(0, '', '', 0, '', 0, $row['cod_client']);
						if( !$rusr || !ria_mysql_num_rows($rusr) ){
							sineres_log( $file_name.' : '.$row['no_document'].' : Client non trouvé : '.$row['cod_client']);
							continue;
						}

						$usr = ria_mysql_fetch_assoc($rusr);


						// récupèration du représentant
						// pour les commandes le commercial est mis sur l'entete alors que pour les factures c'est à la ligne
						$seller_id = 0;
						if( $row['representant'] != ''){
							$rseller = gu_users_get(0, '', '', array(PRF_ADMIN, PRF_SELLER), '', 0, $row['representant'] );
							if( $rseller && ria_mysql_num_rows($rseller) ){
								$seller = ria_mysql_fetch_assoc($rseller);
								$seller_id = $seller['seller_id'];
							}else{
								//sineres_log($file_name.' : '.$row['no_document'].' : Commercial non trouvé : '.$row['representant']);
							}
						}

						// récupère le prd_id en fonction de la ref gescom
						$prd_id = prd_products_get_by_ref_gescom( $row['reference_interne'] );
						if( !$prd_id ){
							// recherche via la ref normal .. ??
							sineres_log( $file_name.' : C/F '.$row['no_document'].' : Produit sineres introuvable '.$row['reference_interne']);
							continue;
						}

						$line_id = $row['no_sequence'];
						$ref = $row['cod_four'].'-'.$row['reference'];
						$name = $row['designation'];
						$qte = $row['qte_commandee'];
						if( $qte == 0 ) continue;

						$row['total_ht_vente'] = str_replace(array(',',' '), array('.',''), $row['total_ht_vente']);
						$price_ht = $row['total_ht_vente'] / $qte;

						$tva_rate = 1;
						if( $price_ht > 0 ){
							$row['montant_tva'] = str_replace(array(',',' '), array('.',''), $row['montant_tva']);
							$tva_amount = $row['montant_tva'] / $qte;
							$tva_rate = 1+($tva_amount / $price_ht);
						}

						$datelivr = $row['cde_delai_demande'];
						$purchase_avg = $row['pump'];

						// en fonction du type de document
						switch($row['nature_document']){
							case 'O': // offre, certain client on ne passe pas par l'étape de commande mais seulement sur des offres
							case 'C': // comptant, bl partiel

								if( !isdate( $row['ref_commande_date'] ) ){
									//sineres_log( $file_name.' : '.$row['no_document'].' : Date de bl invalide : '.$row['ref_commande_date']);
									continue;
								}

								{ // traitement de la commande liée au bl 
									// tente de récupérer la commande de ce bon de livraison 
									$rord = false;
									
									// dans le cas de la sodip un numéro de souche est ajouté, a voir pour les autres sociétés .. 
									$row['ref_commande_lib'] = trim(str_replace(array('CI0','C.I 0'),array('',''),$row['ref_commande_lib']));
									if( is_numeric($row['ref_commande_lib']) && ord_orders_exists($row['ref_commande_lib']) ){

										// ma commande provient de ria et doit être mise à jour par rapport à mon bl
										$rord = ord_orders_get_with_adresses(0,$row['ref_commande_lib']);

									}else{
										//tente de récupérer la commande via le numéro de pièce
										$rord = ord_orders_get_with_adresses(0, 0, 0, $row['no_document'], '2017-04-01' ); // date supérieur à la date de la migration sineres
									}

									if( !$rord || !ria_mysql_num_rows($rord) ){

										// la commande est à construire car non présente
										$ord_id = ord_orders_add_sage( $usr['id'], $row['ref_commande_date'], _STATE_PAY_CONFIRM, '', $row['no_document'], $row['ref_commande_lib'], true,false );
										if( !$ord_id ){
											sineres_log( $file_name.' : C '.$row['no_document'].' : Erreur lors de la création de la commande : ord_orders_add_sage');
											continue;
										}
									}else{
										$ord = ria_mysql_fetch_assoc($rord);
										$ord_id = $ord['id'];


										// mise à jour ddu numéro de piece 
										if( $row['no_document']!= $ord['piece'] ){
											ord_orders_piece_set( $ord['id'], $row['no_document'], false, true, true );
										}
									}

									// masque la commande temporairement
									ord_orders_unmask($ord_id, true, true);

									// on vide la commande de ces lignes  uniquement dans le cas ou celle ci n'a pas été traité en amont sur ce fichier
									if( !in_array($row['no_document'], $doc_order_pieces) ){
										
										// mise à jour du client sur le bl 
										if( !ord_orders_update_user( $ord_id, $usr['id'] ) ){
											sineres_log( $file_name.' : C '.$ord_id.';'.$usr['id'].' : Erreur lors de la mise à jour du client sur commande : ord_bl_update_user');
											continue;
										}

										// suppression des produits
										if( !ord_products_del( $ord_id ) ){
											sineres_log( $file_name.' : C '.$row['no_document'].' : Erreur lors de la suppression des lignes de la commandes');
											continue;
										}

										// mise à jour de la référence de commande 
										if( !ord_orders_ref_update( $ord_id, $row['ref_commande_lib'], false ) ){
											sineres_log( $file_name.' : C '.$row['no_document'].' : Erreur lors de la mise à jour de la référence sur la commande');
											continue;
										}
									}

									// ajoute la commande à la liste des commandes traités
									$doc_order_pieces[] = $row['no_document'];
									
									// création de la ligne 
									if( !ord_products_add_sage( $ord_id, $prd_id, $line_id, $ref, $name, $qte, $price_ht, $tva_rate, $datelivr, null, 0, false, false, false, $purchase_avg, $row['date_creation']) ){
										sineres_log( $file_name.' : C '.$row['no_document'].' : Erreur de création de la ligne produit '.$ref);
										continue;
									}

									// démaske la commande 
									ord_orders_unmask($ord_id, false, true);

									// affectation du représentant
									if( !ord_orders_set_seller_id( $ord_id, $seller_id ) ){
										sineres_log($file_name.' : C '.$row['no_document'].' : Erreur d\'affectation du commercial : '.$row['representant']);
									}
								}

								{ // traitement du bon de livraison 

									// une ligne sans date de prépa n'a pas été expédié donc pas de bl créé.
									if( trim($row['cde_date_preparat'])=='' ){
										continue;
									}
									
									// les piece de bl sont concaténé avec la date de prépa pour avoir de bon unique en fonction de l'expé
									$bl_piece = $row['no_document'].'-'.$row['cde_date_preparat'];

									// tente de récupérer le bon de livraison 
									$rbl = false;
									if( ord_bl_exists_piece($bl_piece) ){
										$rbl = ord_bl_get(0, 0, false, true, false, array(), $bl_piece, array(), '2017-04-01' ); // date supérieur à la migration sineres
									}

									if( !$rbl || !ria_mysql_num_rows($rbl) ){
										// le bl est à construire car non présent
										$bl_id = ord_bl_add_sage( $usr['id'], $bl_piece, $row['ref_commande_lib'], $row['date_creation'], _STATE_BL_EXP , 0 );
										if( !$bl_id ){
											sineres_log( $file_name.' : C '.$bl_piece.' : Erreur lors de la création du bl : ord_bl_add_sage');
											continue;
										}
									}else{
										$bl = ria_mysql_fetch_assoc($rbl);
										$bl_id = $bl['id'];
									}
									
									// on vide le bl de ses lignes  uniquement dans le cas ou celle ci n'a pas été traité en amont sur ce fichier
									if( !in_array($bl_piece, $doc_bl_pieces) ){

										// mise à jour du client sur le bl 
										if( !ord_bl_update_user( $bl_id, $usr['id'] ) ){
											sineres_log( $file_name.' : C '.$bl_piece.' : Erreur lors de la mise à jour du client sur bl : ord_bl_update_user');
											continue;
										}

										// suppression des produits
										if( !ord_bl_products_del( $bl_id ) ){
											sineres_log( $file_name.' : C '.$bl_piece.' : Erreur lors de la suppression des lignes du bl');
											continue;
										}
									}

									// ajoute le à la liste des bl traités
									$doc_bl_pieces[] = $bl_piece;
									
									if( !ord_bl_products_add_sage( $bl_id, $prd_id, $line_id, $ref, $name, $qte, $price_ht, $tva_rate, $ord_id, '', null, 0 ) ){
										sineres_log( $file_name.' : C '.$bl_piece.' : Erreur de création de la ligne produit bl '.$ref);
										continue;
									}

									

									if( $ord_id ){
										ord_orders_state_update( $ord_id, _STATE_BL_EXP, '', false );
										ord_orders_set_date_modified( $ord_id );
									}
								}

								break;

							case 'F':  // traitement de la facture pas de liaison avec la commande car les numéro de lignes ne sont pas correcte
								{
									if( !isdate( $row['date_facturation'] ) ){
										continue;
									}

									$row['facture_no'] = trim($row['facture_no']);

									$piece = $row['facture_no'] == '0' ? $row['no_document'] : $row['facture_no'];
									//tente de récupérer la facture via le numéro de pièce
									$rinv = ord_invoices_get(0, 0, 0, false, false, false, $piece );

									if( !$rinv || !ria_mysql_num_rows($rinv) ){
										//tente de récupérer la facture via le numéro de document
										$rinv = ord_invoices_get(0, 0, 0, false, false, false, $row['no_document'] );
										//si le numéro de facture est 0 on utilise le numéro de document comme piece
										if( !$rinv || !ria_mysql_num_rows($rinv) ){
											// la facture est à construire car non présente
											$inv_id = ord_invoices_add_sage( $usr['id'], $piece, $row['ref_commande_lib'], $row['date_facturation'] );
											if( !$inv_id ){
												sineres_log( $file_name.' : F '.$piece.' : Erreur lors de la création de la facture : ord_invoices_add_sage');
												continue;
											}
										}else{
											$inv = ria_mysql_fetch_assoc($rinv);
											$inv_id = $inv['id'];
										}
									}else{
										$inv = ria_mysql_fetch_assoc($rinv);
										$inv_id = $inv['id'];
									}

									// on vide la commande de ces lignes  uniquement dans le cas ou celle ci n'a pas été traité en amont sur ce fichier
									if( !in_array($piece, $doc_inv_pieces) ){
										//update piece
										if (isset($inv['piece']) && $piece != $inv['piece']) {
											if (!ord_invoices_set_piece($inv_id, $piece)) {
												sineres_log( $file_name.' : F '.$inv_id.';'.$piece.' : Erreur lors de la mise à jour de la pièce sur factures : ord_invoices_set_piece');
											}
										}
										// mise à jour du client sur le bl
										if( !ord_invoices_update_user( $inv_id, $usr['id'] ) ){
											sineres_log( $file_name.' : F '.$inv_id.';'.$usr['id'].' : Erreur lors de la mise à jour du client sur factures : ord_invoices_update_user');
											continue;
										}

										// suppression des produits
										if( !ord_inv_products_del( $inv_id ) ){
											sineres_log( $file_name.' : F '.$piece.' : Erreur lors de la suppression des lignes de la factures');
											continue;
										}

										// mise à jour de la référence de commande
										if( !ord_invoices_update_ref( $inv_id, $row['ref_commande_lib'], false ) ){
											sineres_log( $file_name.' : F '.$piece.' : Erreur lors de la mise à jour de la référence sur la factures');
											continue;
										}
									}

									// ajoute la commande à la liste des commandes traités
									$doc_inv_pieces[] = $piece;

									// création de la ligne
									if( !ord_inv_products_add_sage( $inv_id, $prd_id, $line_id, $ref, $name, $price_ht, $qte, $tva_rate, null ) ){
										sineres_log( $file_name.' : F '.$piece.' : Erreur de création de la ligne produit '.$ref);
										continue;
									}

									// affectation du représentant
									if( !ord_inv_products_set_seller( $inv_id, $prd_id, $line_id, $seller_id ) ){
										sineres_log($file_name.' : F '.$piece.' : Erreur d\'affectation du commercial : '.$row['representant']);
									}

									// démasque la facture
									ord_invoices_unmask($inv_id);
								}

								break;
							default : // autres
								break;
						}
					}

					fclose( $handle );

					// déplacement du fichier dans un dossier d'archive pour conserver un historique
					if( is_file( $file_link ) ){
						//suppression du fichier
						rename( $file_link, $dir.'archives/'.$file_name );
					}
				}

				break;
			}

		}

	}
}

// gestion des erreurs
if( sizeof( $errors ) ){
	mail( '<EMAIL>', 'Logs Sineres', implode( "\n", $errors ) );
}

function sineres_log($string){
	global $config, $errors;
	$msg = $GLOBALS['config']['tnt_id'].' : '.$string;
	if ($config['env_sandbox']) {
		echo $msg.PHP_EOL;
	}else{
		$errors[] = $msg;
	}
}
