<?php
	
	/**	\file clean-fld_object_values.php
	 *
	 *	Ce script permet de faire le ménage dans la table "fld_object_values" des éléments supprimés.
	 *
	 */
	
	set_include_path(dirname(__FILE__) . '/../include/');

	require_once('db.inc.php');
	require_once('define.inc.php');
	
	$options = getopt('', array('action:', 'sleep::', 'log_file'));
	
	// Argument obligatoire : type d'action :
	// 		- "del_all" (supprime en une fois le jeu de résultats)
	//		- "del_row" (supprime ligne par ligne le jeu de résultats)
	//		- "count" (compte le nombre de résultat à supprimer)
	if( !isset($options['action']) ){
		print 'L\'argument obligatoire "action" n\'a pas été spécifié.'."\n";
		exit;
	}
	$options['action'] = strtolower(trim($options['action']));
	if( !in_array($options['action'], array('del_all', 'del_row', 'count')) ){
		print 'L\'argument "action" doit être une des valeurs suivantes : "del_all", "del_row" ou "count".'."\n";
		exit;
	}
	$action_type = $options['action'];
	
	// Argument optionnel : temps de pause en millisecondes entre chaque action
	$sleep_time = 50;
	if( isset($options['sleep']) ){
		if( !is_numeric($options['sleep']) || $options['sleep'] < 0 ){
			print 'L\'argument "sleep" doit être un entier supérieur ou égal à 0.'."\n";
			exit;
		}
		$sleep_time = $options['sleep'];
	}
	
	// Argument optionnel, sans valeur à spécifier : indique si un fichier de log reçoit les messages
	$log_file = false;
	if( isset($options['log_file']) ){
		$log_file = '/var/log/php/clean_fld_object_values_'.date('YmdHis').'.log';
	}
	
	// action SQL suivant la valeur de "$action_type"
	$sql_common_part = 'DELETE';
	if( $action_type == 'del_row' ){
		$sql_common_part = 'SELECT
			pv_tnt_id as "tnt", pv_lng_code as "lng", pv_obj_id_0 as "obj_0",
			pv_obj_id_1 as "obj_1", pv_obj_id_2 as "obj_2", pv_fld_id as "fld"
		';
	}elseif( $action_type == 'count' ){
		$sql_common_part = 'SELECT COUNT(*)';
	}
	
	$queries = array();

	// locataires supprimés
	{
		$queries['no_tenant'] = '
			'.$sql_common_part.'
			FROM fld_object_values
			WHERE NOT EXISTS (
				SELECT 1 FROM tnt_tenants
				WHERE tnt_id = pv_tnt_id AND tnt_date_deleted IS NULL
			)
		';
	}

	// champs ou classes supprimés
	{
		$queries['no_fld_no_cls'] = '
			'.$sql_common_part.'
			FROM fld_object_values
			WHERE NOT EXISTS (
				SELECT 1 FROM fld_fields
				JOIN fld_classes on fld_cls_id = cls_id AND fld_tnt_id = IF(cls_tnt_id = 0, fld_tnt_id, cls_tnt_id)
				WHERE fld_tnt_id IN (0, pv_tnt_id) AND fld_date_deleted IS NULL AND cls_date_deleted IS NULL
				AND fld_id = pv_fld_id
			)
		';
	}

	// lignes de facture
	{
		$queries['inv_rows'] = '
			'.$sql_common_part.'
			FROM fld_object_values
			WHERE EXISTS (
				SELECT 1 FROM fld_fields WHERE fld_cls_id = '.CLS_INV_PRODUCT.'
				AND fld_tnt_id IN (0, pv_tnt_id) AND fld_id = pv_fld_id
			) AND (
				NOT EXISTS (
					SELECT 1 FROM ord_invoices WHERE inv_tnt_id = pv_tnt_id AND inv_id = pv_obj_id_0
				) OR NOT EXISTS (
					SELECT 1 FROM ord_inv_products WHERE prd_tnt_id = pv_tnt_id AND prd_inv_id = pv_obj_id_0
					AND prd_id = pv_obj_id_1 AND prd_line_id = pv_obj_id_2
				)
			)
		';
	}

	// lignes de BL
	{
		$queries['bl_rows'] = '
			'.$sql_common_part.'
			FROM fld_object_values
			WHERE EXISTS (
				SELECT 1 FROM fld_fields WHERE fld_cls_id = '.CLS_BL_PRODUCT.'
				AND fld_tnt_id IN (0, pv_tnt_id) AND fld_id = pv_fld_id
			) AND (
				NOT EXISTS (
					SELECT 1 FROM ord_bl WHERE bl_tnt_id = pv_tnt_id AND bl_id = pv_obj_id_0
				) OR NOT EXISTS (
					SELECT 1 FROM ord_bl_products WHERE prd_tnt_id = pv_tnt_id AND prd_bl_id = pv_obj_id_0
					AND prd_id = pv_obj_id_1 AND prd_line_id = pv_obj_id_2
				)
			)
		';
	}

	// lignes de PL
	{
		$queries['pl_rows'] = '
			'.$sql_common_part.'
			FROM fld_object_values
			WHERE EXISTS (
				SELECT 1 FROM fld_fields WHERE fld_cls_id = '.CLS_PL_PRODUCT.'
				AND fld_tnt_id IN (0, pv_tnt_id) AND fld_id = pv_fld_id
			) AND (
				NOT EXISTS (
					SELECT 1 FROM ord_pl WHERE pl_tnt_id = pv_tnt_id AND pl_id = pv_obj_id_0
				) OR NOT EXISTS (
					SELECT 1 FROM ord_pl_products WHERE prd_tnt_id = pv_tnt_id AND prd_pl_id = pv_obj_id_0
					AND prd_id = pv_obj_id_1 AND prd_line_id = pv_obj_id_2
				)
			)
		';
	}

	// lignes de commandes
	{
		$queries['ord_rows'] = '
			'.$sql_common_part.'
			FROM fld_object_values
			WHERE EXISTS (
				SELECT 1 FROM fld_fields WHERE fld_cls_id = '.CLS_ORD_PRODUCT.'
				AND fld_tnt_id IN (0, pv_tnt_id) AND fld_id = pv_fld_id
			) AND (
				NOT EXISTS (
					SELECT 1 FROM ord_orders WHERE ord_tnt_id = pv_tnt_id AND ord_id = pv_obj_id_0
					AND (ord_state_id != '._STATE_CANCEL_MERCHAND.' OR ord_masked = 0)
					AND (ord_state_id != '._STATE_BASKET.' OR ord_usr_id IS NOT NULL OR DATE_ADD(ord_date, INTERVAL 6 MONTH) >= NOW())
				) OR NOT EXISTS (
					SELECT 1 FROM ord_products WHERE prd_tnt_id = pv_tnt_id AND prd_ord_id = pv_obj_id_0
					AND prd_id = pv_obj_id_1 AND prd_line_id = pv_obj_id_2
				)
			)
		';
	}

	// factures
	{
		$queries['inv_head'] = '
			'.$sql_common_part.'
			FROM fld_object_values
			WHERE EXISTS (
				SELECT 1 FROM fld_fields WHERE fld_cls_id = '.CLS_INVOICE.'
				AND fld_tnt_id IN (0, pv_tnt_id) AND fld_id = pv_fld_id
			) AND NOT EXISTS (
				SELECT 1 FROM ord_invoices WHERE inv_tnt_id = pv_tnt_id AND inv_id = pv_obj_id_0
			)
		';
	}

	// produits
	{
		$queries['products'] = '
			'.$sql_common_part.'
			FROM fld_object_values
			WHERE EXISTS (
				SELECT 1 FROM fld_fields WHERE fld_cls_id = '.CLS_PRODUCT.'
				AND fld_tnt_id IN (0, pv_tnt_id) AND fld_id = pv_fld_id
			) AND NOT EXISTS (
				SELECT 1 FROM prd_products WHERE prd_tnt_id = pv_tnt_id AND prd_id = pv_obj_id_0
				AND prd_date_deleted IS NULL
			)
		';
	}

	// messages
	{
		$queries['messages'] = '
			'.$sql_common_part.'
			FROM fld_object_values
			WHERE EXISTS (
				SELECT 1 FROM fld_fields WHERE fld_cls_id = '.CLS_MESSAGE.'
				AND fld_tnt_id IN (0, pv_tnt_id) AND fld_id = pv_fld_id
			) AND NOT EXISTS (
				SELECT 1 FROM gu_messages WHERE cnt_tnt_id = pv_tnt_id AND cnt_id = pv_obj_id_0
				AND cnt_date_delete IS NULL
			)
		';
	}

	// commandes
	{
		$queries['ord_head'] = '
			'.$sql_common_part.'
			FROM fld_object_values
			WHERE EXISTS (
				SELECT 1 FROM fld_fields WHERE fld_cls_id = '.CLS_ORDER.'
				AND fld_tnt_id IN (0, pv_tnt_id) AND fld_id = pv_fld_id
			) AND NOT EXISTS (
				SELECT 1 FROM ord_orders WHERE ord_tnt_id = pv_tnt_id AND ord_id = pv_obj_id_0
				AND (ord_state_id != '._STATE_CANCEL_MERCHAND.' OR ord_masked = 0)
				AND (ord_state_id != '._STATE_BASKET.' OR ord_usr_id IS NOT NULL OR DATE_ADD(ord_date, INTERVAL 6 MONTH) >= NOW())
			)
		';
	}

	// clients
	{
		$queries['users'] = '
			'.$sql_common_part.'
			FROM fld_object_values
			WHERE EXISTS (
				SELECT 1 FROM fld_fields WHERE fld_cls_id = '.CLS_USER.'
				AND fld_tnt_id IN (0, pv_tnt_id) AND fld_id = pv_fld_id
			) AND NOT EXISTS (
				SELECT 1 FROM gu_users WHERE usr_tnt_id = pv_tnt_id AND usr_id = pv_obj_id_0
				AND usr_date_deleted IS NULL
			)
		';
	}
	
	log_msg('Début de traitement.');
	
	foreach( $queries as $query_name => $sql ){
		
		$bench_start = microtime(true);
		
		$r = ria_mysql_query($sql);
		
		$bench_end = microtime(true);
		
		usleep($sleep_time * 1000);
		
		if( ria_mysql_errno() ){
			log_msg("Erreur SQL : ".mysql_error()." - Requête '".$query_name."'");
		}else{
			switch( $action_type ){
				case 'del_all': {
					log_msg("Requête '".$query_name."' exécutée en ".round($bench_end - $bench_start, 3)." sec (".ria_mysql_affected_rows()." résultats supprimés).");
					break;
				}
				case 'del_row': {
					$count_results = $r ? ria_mysql_num_rows($r) : 0;
					if( $count_results ){
					
						log_msg($count_results." résultats à supprimer pour '".$query_name."' (exécution en ".round($bench_end - $bench_start, 3)." sec).");
						
						// boucle sur le jeu de résultats et les supprime un à la fois, avec une pause entre chaque
						$i = 1;
						while( $obj = ria_mysql_fetch_assoc($r) ){
							
							$bench_start_row = microtime(true);
							
							$sql_del = '
								delete from fld_object_values
								where pv_tnt_id = '.$obj['tnt'].' and pv_lng_code = "'.addslashes($obj['lng']).'" and pv_obj_id_0 = '.$obj['obj_0'].'
								and pv_obj_id_1 = '.$obj['obj_1'].' and pv_obj_id_2 = '.$obj['obj_2'].' and pv_fld_id = '.$obj['fld'].'
							';
							
							ria_mysql_query($sql_del);
							
							$bench_end_row = microtime(true);
							
							log_msg("Résultat ".$i." / ".$count_results." supprimé en ".round($bench_end_row - $bench_start_row, 3)." sec.");
							usleep($sleep_time * 1000);
							$i++;
							
						}
						
					}else{
						log_msg("Aucun résultat à supprimer pour '".$query_name."' (exécution en ".round($bench_end - $bench_start, 3)." sec).");
					}
					break;
				}
				case 'count': {
					log_msg("Nombre de résultats pour '".$query_name."' : ".( $r && ria_mysql_num_rows($r) ? ria_mysql_result($r, 0, 0) : 'N.C.' )." (exécuté en ".round($bench_end - $bench_start, 3)." sec).");
					break;
				}
			}
		}
		
	}
	
	log_msg('Fin de traitement.');
	
	/**	Affiche dans la console, ou enregistre dans un log, le message donné en argument.
	 *	\param $msg Message à afficher ou à stocker. Si le message ne termine pas par "\n", cette chaîne est ajoutée.
	 */
	function log_msg( $msg ){
		
		global $log_file;
		
		if( substr($msg, strlen($msg) - 2, 2) != "\n" ){
			$msg .= "\n";
		}
		
		if( $log_file !== false ){
			error_log($msg, 3, $log_file);
		}else{
			print $msg;
		}
		
	}
	
