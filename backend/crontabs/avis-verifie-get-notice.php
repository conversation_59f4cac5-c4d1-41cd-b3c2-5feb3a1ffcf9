<?php
	/** \file avis-verifie-get-notice.php
	 * 	Ce script permet de récupérer les avis consommateurs sur les produits et le site depuis "Avis Vérifié",
	 * 	pour les importer dans le site. Il ne marche plus et à besoin d'une maintenance.
	 */

	// Script bloqué s'il n'est pas exécuter via execute-script.php (en mode 1 = pour tous les sites)
	if (!isset($ar_params)) {
		error_log("L'exécution du script ".__FILE__." nécessite l'appel de execute-script.php.".PHP_EOL);
		exit;
	}

	// Fichiers nécessaire au bon fonctionnement
	require_once('define.inc.php');
	require_once('messages.inc.php');
	require_once('AvisVerifie/avisVerifie.inc.php');

	foreach( $configs as $config ){
		// Bloque si "Avis Vérifié" n'est pas activé pour ce site
		if( !isset($config['avis_verifie_activer']) || !$config['avis_verifie_activer'] ){
			continue;
		}

		// Création de l'instance avis certifié
		$avis_verifie = new AvisVerifie();

		// Recupère la liste des produit ayant un avis et leur date
		$list_products = $avis_verifie->get_list_product_have_notice();

		$date_now = new DateTime();
		foreach( $list_products as $value ){
			// Récupère les avis pour chaque produit
			$notices = $avis_verifie->get_notice_product($value['0']);

			if( !isset($value['1']) || !isdate($value['1']) ){
				$value['1'] = $date_now->format('Y-m-d');
			}

			if( is_array($notices) && count($notices) ){
				foreach( $notices as $notice ){
					$product = prd_products_get_id($notice->id_product);

					// Vérifié si un avis consommateur n'est pas déjà lié à l'identifiant "Avis vérifié" avant d'en créer un nouveau
					$exist = gu_messages_exists_avis_verifie($notice->id_review);

					if( $exist == 0 ){
						// Ajout d'un nouvel avis
						add_message( $notice->firstname, $notice->lastname, '', '', '', 'Avis consommateur sur un article', $notice->review, 'RVW_PRODUCT', '', false, NULL, $product, '', '', '', 0, 0, false, false, null, false, array(), array(), $notice->rate, null, null, false, null, $notice->order_ref, 'site-contact',$notice->id_review, 0, 1, $value['1'] );
					}
				}
			}
		}

		// Gestion des avis consommateur sur le site
		$list_notice_website = $avis_verifie->get_notice_website();
		foreach( $list_notice_website as $notice ){
			// Vérifié si un avis consommateur n'est pas déjà lié à l'identifiant "Avis vérifié" avant d'en créer un nouveau
			$exist = gu_messages_exists_avis_verifie($notice->id_review);

			if( $exist == 0 ){
				// Ajout d'un nouvel avis
				add_message( $notice->firstname, $notice->lastname, '', '', '', 'Avis consommateur sur un site', $notice->review, 'RVW_SITE', '', false, NULL, 0, '', '', '', 0, 0, false, false, null, false, array(), array(), $notice->rate, null, null, false, null, $notice->order_ref , 'site-contact',$notice->id_review, 0, 1, $value['1'] );
			}
		}
	}
