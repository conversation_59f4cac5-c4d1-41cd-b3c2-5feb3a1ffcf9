<?php
/** \file update-zones-espagne.php
 *	\ingroup system crontabs
 * 	Ce script est destiné à mettre à jour les zones (Régions, province, Communes, Codes postaux) pour l'Espagne.
 * 	Description du code postal Allemand
 * 	5 chiffres 
 * 	Les 2 premiers chiffres correspondent au province par ordre alphabétique
 * 	Les 3 derniers correspondent à la zones postales
 */
set_include_path(dirname(__FILE__) . '/../include/');

require_once('db.inc.php');
require_once('strings.inc.php');
require_once('sys.zones.inc.php');
require_once('geo-api.inc.php');

$file_text = api_geoname_get_zones('ES');
/* Identifiants de types de zones :
	*		- Communauté : 19
	*		- Province : 20
	*		- Code postal : 45
	*		- Ville : 21
	*/
if($file_text != ''){
	$cpt = 1;
	foreach ($file_text as $line) {
		$line = explode('	', $line);
		$ville       = $line[2];
		$zip_code    = $line[1];
		$province      = $line[5];
		$province_code = $line[6];
		$communaute        = $line[3];
		$communaute_code   = $line[4];

		//Recherche la région en base et l'a créée si elle n'existe pas
		$zone_get = api_geogouv_add_zone(_ZONE_COMMUNAUTE_ESPAGNE, $communaute_code, $communaute,0, 'ES');
		if (!$zone_get) {
			$error = true;
			error_log(__FILE__.':'.__LINE__.'Erreur lors de la création / récupération de la région : "'.htmlspecialchars($communaute_code.' - '.$communaute).'"');
			continue;
		}else{
			$region_get = api_geogouv_add_zone(_ZONE_PROV_ESPAGNE, $province_code, $province, $zone_get);
			if (!$region_get) {
				$error = true;
				error_log(__FILE__.':'.__LINE__.'Erreur lors de la création / récupération du département : "'.htmlspecialchars($province_code.' - '.$province).'"');
				continue;
			}else{
				$ville = strtoupper2($ville);
				print 'Commune : '. $ville .PHP_EOL;
				$zipcode_zone = api_geogouv_add_zone(_ZONE_ZIPCODE_ESPAGNE, $zip_code, $zip_code, $region_get);
				if (!$zipcode_zone) {
					$error = true;
					error_log(__FILE__.':'.__LINE__.'Erreur lors de la création / récupération du code postal : "' . htmlspecialchars($zip_code) . '"');
					continue;
				}else{
					// Recherche la commune et l'a créée si elle n'existe pas
					$commune_zone = api_geogouv_add_zone(_ZONE_VILLE_ESPAGNE, $ville, $ville, $zipcode_zone);
					if (!$commune_zone) {
						$error = true;
						error_log(__FILE__.':'.__LINE__.'Erreur lors de la création / récupération de la commune : "'.htmlspecialchars($zip_code.' - '.$ville).'"');
						continue;
					}
					print 'Code postal : ' . $zip_code . PHP_EOL;
					$ar_codepostal_ids[] = $zipcode_zone;
					$ar_commune_ids[] = $commune_zone;
				}
			
			}
			$ar_departement_ids[] = $region_get;
			$ar_region_ids[] = $zone_get;
		}
	}

	//Inclusion des zones spéciales (Armée)
	array_push($ar_commune_ids, 361207, 361208);
	array_push($ar_codepostal_ids, 361205, 361206);
	
	//Deprecated des zones plus utilisées
	if (!isset($error)) {
		sys_zones_set_deprecated_out_ids(_ZONE_COMMUNAUTE_ESPAGNE, $ar_region_ids);
		sys_zones_set_deprecated_out_ids(_ZONE_PROV_ESPAGNE, $ar_departement_ids);
		sys_zones_set_deprecated_out_ids(_ZONE_VILLE_ESPAGNE, $ar_commune_ids);
		sys_zones_set_deprecated_out_ids(_ZONE_ZIPCODE_ESPAGNE, $ar_codepostal_ids);
		// Reconstruction de la hiérarchie
		sys_zones_hierarchy_rebuild(0,'ES');
	}

}