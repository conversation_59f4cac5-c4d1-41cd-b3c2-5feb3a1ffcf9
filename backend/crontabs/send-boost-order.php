<?php
	/** \file send-boost-order.php
	 *
	 * 	Ce script est chargé de relancer les clients n'ayant pas repassé commande depuis plus de X jours
	 * 	La variable de configuration suivante doit être définie : boost_order
	 *  Exemple :
	 *  [
	 *  	{
	 *  		"days":60,
	 *  		"amount":4.1666,
	 *  		"type":0,
	 *  		"valid":10
	 *  	},{
	 *  		"days":120,
	 *  		"amount":8.3333,
	 *  		"type":0,
	 *  		"valid":10
	 *  	},{...}
	 *  ]
	 *
	 */
	if (!isset($ar_params)) {
		die("L'exécution de ce script nécessite l'appel de execute-script.php." . PHP_EOL);
	}

	require_once('promotions.inc.php');

	require_once('Marketing/CampaignsManager.inc.php');
	require_once('Marketing/Channels.inc.php');
	require_once('Marketing/TriggersManager.inc.php');


	foreach( $configs as $config ){
		if( !array_key_exists('boost_order', $config) || trim($config['boost_order']) == '' ){
			continue;
		}

		$boost_order = json_decode( $config['boost_order'], true );

		// Contrôle du paramètre
		$params_is_ok = true;
		if( !is_array($boost_order) || !sizeof($boost_order) ){
			$params_is_ok = false;
		}else{
			foreach( $boost_order as $key=>$one_recovery ){
				if( !ria_array_key_exists(array('days', 'amount', 'type', 'valid'), $one_recovery) ){
					$params_is_ok = false;
				}else{
					if( !is_numeric($one_recovery['days']) || $one_recovery['days'] <= 0 ){
						$params_is_ok = false;
						break;
					}

					if( !is_numeric($one_recovery['amount']) || $one_recovery['amount'] <= 0 ){
						$params_is_ok = false;
						break;
					}

					if( !in_array($one_recovery['type'], array('0', '1')) ){
						$params_is_ok = false;
						break;
					}

					if( !is_numeric($one_recovery['valid']) || $one_recovery['valid'] < 0 ){
						$params_is_ok = false;
						break;
					}
				}

				$boost_order[ $key ]['user'] = array();
			}
		}

		if( !$params_is_ok ){
			continue;
		}

		$boost_order = array_msort( $boost_order, array('days'=>SORT_DESC) );

		// Récupère les comptes clients
		$r_user = gu_users_get();
		if( !$r_user || !ria_mysql_num_rows($r_user) ){
			continue;
		}

		while( $user = ria_mysql_fetch_assoc($r_user) ){

			if( !gu_valid_email($user['email']) ){
				continue;
			}

			if (isset($config['boost_order_newsletter']) && is_numeric($config['boost_order_newsletter']) && $config['boost_order_newsletter'] > 0) {
				if (!newsletter_is_inscripted($user['email'], $config['boost_order_newsletter'])) {
					continue;
				}
			}

			$last_send = fld_object_values_get( $user['id'], _FLD_USR_BOOST_SEND, '', false, true );
			if( trim($last_send) != '' && isdateheure($last_send) ){
				if( strtotime($last_send) > strtotime('-'.$config['boost_limit_send'].' days') ){
					continue;
				}
			}

			// Récupère la date de dernière commande
			$last_order = gu_users_get_last_order( $user['id'] );
			if( !$last_order ){
				continue;
			}
			
			$last_time = false;

			// Pouvoir envoyer les campagnes selon la date de dernière facture (qui doit obligatoirement être antérieure à la dernière commande)
			if( isset($config['boost_order_on_invoices']) && $config['boost_order_on_invoices'] ){
				$last_invoice = gu_users_get_last_invoice( $user['id'] );
				if( $last_invoice ){
					if( strtotime($last_invoice['date_en']) > strtotime($last_order['date']) ){
						$last_time = $last_invoice['date_en'];
					}
				}
			}else{
				$last_time = $last_order['date'];
			}

			if( !$last_time ){
				continue;
			}

			// Contrôle si le compte client doit recevoir une des alertes
			foreach( $boost_order as $key=>$one_recovery ){
				$limit_apply =  strtotime( '-'.$one_recovery['days'].' days' );

				if( strtotime($last_time) <= $limit_apply ){
					$boost_order[ $key ]['user'][] = $user;
					break;
				}
			}
		}

		// Pour chaque alerte où des comptes clients doivent la recevoir
		foreach( $boost_order as $key=>$one_recovery ){
			if( !is_array($one_recovery['user']) || !sizeof($one_recovery['user']) ){
				continue;
			}

			$cod_promo = pmt_codes_generated(array('promo_generated_length'=>7));
			if( trim($cod_promo) == '' ){
				error_log(__FILE__.':'.__LINE__.' erreur lors de la génération du code promotion');
				break;
			}

			// Création du code promotion
			$cod = pmt_codes_add( '['.date('d/m/Y').'] Relance client', _PMT_TYPE_CODE, $cod_promo, '['.date('d/m/Y').'] Relance client ayant passé commande il y plus de '.$one_recovery['days'].' jours' );
			if( !is_numeric($cod) || $cod <= 0 ){
				error_log(__FILE__.':'.__LINE__.' erreur lors de la création du code promotion');
				break;
			}

			// Définition de la réduction
			$date_start = date('Y-m-d 00:00:00');
			$date_stop  = ( $one_recovery['valid'] > 0 ? date( 'Y-m-d 23:59:59', strtotime('+'.$one_recovery['valid'].' days') ) : null );

			$offer = pmt_offers_add( $cod, _PMT_TYPE_CODE, $one_recovery['amount'], $one_recovery['type'], 1, 0, 0, 0, 'order', $date_start, $date_stop, 0, false, false, true );
			if( !is_numeric($offer) || $offer <= 0 ){
				pmt_codes_del( $cod );
				error_log(__FILE__.':'.__LINE__.' erreur lors de la définition de la réduction');
				break;
			}

			$error = false;
			foreach( $one_recovery['user'] as $one_user ){
				if( !pmt_users_add( $cod, $one_user['id'], true) ){
					error_log(__FILE__.':'.__LINE__.' erreur lors de l\'ajout de la restriction client '.$cod.'-'.$user['id']);
					pmt_offers_del( $cod, $offer );
					pmt_codes_del( $cod );
					$error = true;
					break;
				}

				if( !pmt_websites_add($cod, $config['wst_id']) ){
					error_log(__FILE__.':'.__LINE__.' erreur lors de l\'activation du code promotion sur le site : '.$cod.'-'.$config['wst_id']);
					pmt_offers_del( $cod, $offer );
					pmt_codes_del( $cod );
					$error = true;
					break;
				}
			}
			
			if(!$error){
				if( $config['tnt_id'] == 14 ){
					$now = new DateTime('now');
					$days = 2;
					// create trigger for that code

					if( isset($config['boost_order_margin']) && is_numeric($config['boost_order_margin']) && $config['boost_order_margin'] > 0 ){
						$days = $config['boost_order_margin'] / 24 + $days;
					}

					$trigger_id = TriggersManager::addTriggers('CONTROL', CLS_PMT_CODE, $cod, _FLD_OFF_DATE_STOP, 'PMT_EXPIRE_DAYS', $days);

					$date = new DateTime($date_stop);
					$date->modify('-2 days');

					switch( $date->format('N') ){
						case '7':
							$date->modify('-2 days');
							break;
						case '6':
							$date->modify('-1 day');
							break;
					}
					// create campaign for that code
					$title = 'Campaign pour le code promotion '.$cod_promo;
					$desc = 'Un sms sera envoyé à tous les utilisateurs 2 jours avant la fin du code promotion '.$cod_promo;
					$campaign_id = CampaignsManager::addCampaigns( $title, $desc, 'NOW', $now->format('Y-m-d H:i:s'), $date->format('Y-m-d H:i:s'), $date->format('Y-m-d'), '17:00' );
					
					// create channel sms
					$channel_id = Channels::addChannels( $config['wst_id'], 'SMS', $campaign_id, false, false, 1, $config['i18n_lng'] );
					
					// add content
					$content = 'Votre remise Client Privilège COOPCORICO expire dans moins de 48h ! Profitez vite de votre remise de '.number_format ( $one_recovery['amount']*_TVA_RATE_DEFAULT, 0 ).'E avec le code '.$cod_promo.'.';
		
					Channels::addChannelContent( $channel_id, $content );
					
					// create trigger group AND type condition
					$group_id = TriggersManager::addTriggersGroups( $campaign_id, 'and', 'all' );
					// add created trigger to condition
					TriggersManager::addTriggerCondition( $group_id, $trigger_id, $campaign_id);
				}

				foreach( $one_recovery['user'] as $one_user ) {
					$send = pmt_codes_send( $cod, 'boost-order', $one_user );
					if( $send ){
						fld_object_values_set( $one_user['id'], _FLD_USR_BOOST_SEND, date('Y-m-d H:i:s') );
					}
				}

				// Application de la marge de sécurité
				if( isset($config['boost_order_margin']) && is_numeric($config['boost_order_margin']) && $config['boost_order_margin'] > 0 ){
					$date_stop = date( 'Y-m-d 23:59:59', strtotime('+ '.$config['boost_order_margin'].' hours', strtotime($date_stop)) );
					pmt_codes_update_dates( $cod, false, $date_stop );
				}
			}
		}
	}

