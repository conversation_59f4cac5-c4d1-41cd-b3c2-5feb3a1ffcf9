<?php
	
	/** \file export.php
	 *
	 * 	Ce script permet d'exporter les données pour xlsoft
	 *
	 */
	
	set_include_path(dirname(__FILE__) . '/../include/');
	
	require_once( 'cfg.variables.inc.php' );
	require_once( 'xlsoft/xlsoft.define.inc.php' );
	
	unset($config);
	
	// Charge l'ensemble des configurations clients
	$configs = cfg_variables_get_all_tenants();
	if( !is_array($configs) || !sizeof($configs) ){
		return false;
	}
	
	foreach( $configs as $config ){
		
		// fonctionnel pour MMM uniquement
		if( $config['tnt_id'] != 26 ){
			continue;
		}
		
		// charge le fichier de config du site principale
		if( !is_file($config['site_dir'].'/config.inc.php') ) {
			error_log('[XlPos] Fichier de configuration introuvable pour le locataire : '.$config['tnt_id']);
			continue;
		}
		
		require_once($config['site_dir'].'/config.inc.php');
		
		$usr_import_file = str_replace('/htdocs', '', $config['site_dir']).'/echange_xlpos/receive/CLIENT_'.date('dmY').'_'.date('his').'.txt';
		$ord_import_file = str_replace('/htdocs', '', $config['site_dir']).'/echange_xlpos/receive/COMMANDE_'.date('dmY').'_'.date('his').'.txt';
		$usr_backup_import_file = str_replace('/htdocs', '', $config['site_dir']).'/echange_xlpos/archives/receive/CLIENT_'.date('dmY').'_'.date('his').'.txt';
		$ord_backup_import_file = str_replace('/htdocs', '', $config['site_dir']).'/echange_xlpos/archives/receive/COMMANDE_'.date('dmY').'_'.date('his').'.txt';
		
		// premier tableau : détail csv de la commande
		// deuxième tableau : ID des commandes parents (uniquement)
		// troisième tableau : détail csv des clients à créer ou à mettre à jour
		// quatrième tableau : produits avec leur nouveau prix (recalcul via code promo)
		$orders = $ord_id_ar = $users = $ar_prd_price_impact = array();
		
		// boucle sur les nouvelles commandes à importer
		if( $rord = ord_orders_get_new_to_import() ){
			while( $ord = ria_mysql_fetch_assoc( $rord ) ){
				$rord_detail = ord_orders_get_with_adresses( 0, $ord['id'] );
				if( $rord_detail && ria_mysql_num_rows($rord_detail) ){
					$ord = ria_mysql_fetch_assoc($rord_detail);
					$rusr_detail = gu_users_get( $ord['user'] );
					if( $rusr_detail && ria_mysql_num_rows($rusr_detail) ){
						$usr = ria_mysql_fetch_assoc($rusr_detail);
						
						// tente une récupération des commandes enfants
						$rchild = ord_orders_get_childs( $ord['id'], true );
						if( !$rchild ){
							error_log('[XlPos] Echec de ord_orders_get_childs( '.$ord['id'].' )');
							continue;
						}
						
						// pour les commandes sans enfants, $ord_childs contient la commande de base
						$ord_childs = array();
						$count_colis = ria_mysql_num_rows($rchild);
						if( $count_colis ){
							while( $child = ria_mysql_fetch_assoc($rchild) ){
								$ord_childs[] = $child;
							}
						}else{
							$ord_childs[] = $ord;
						}
						
						// un seul pay_id, celui de la commande parent
						$pay_libelle = xlsoft_get_pay_libelle( $ord['pay_id'] );
						if( $pay_libelle == '' ){
							error_log('[XlPos] Le moyen de paiement de la commande '.$ord['id'].' n\'a pas d\'équivalent dans XlPos.');
							continue;
						}
						
						$ord_date_text = date('d/m/Y',strtotime($ord['date_en']));
						
						$pmt_code = '';
						$discount = $discountval = 0;
						$all_catalog = $inc_promotions = false;
						$prd_inc = array();
						
						// chargement du code promotion sur la commande de base
						if( $ord['pmt_id'] ){
							if( $rpmt = pmt_codes_get( $ord['pmt_id'] ) ){
								if( ria_mysql_num_rows($rpmt) ){
									$pmt = ria_mysql_fetch_assoc($rpmt);
									
									// informations de base
									$pmt_code = $pmt['code'];
									if( $pmt['discount_type']==1 ){
										$discount = $pmt['discount'];
									}else{
										$discountval = $pmt['discount'];
									}
									$all_catalog = $pmt['all-catalog'] == 1;
									$inc_promotions = $pmt['include_pmt'];
									
									// tableau des produits inclus ou exclus
									if( $plist = pmt_codes_products_get( $pmt['id'], $all_catalog ) ){
										while( $p = ria_mysql_fetch_assoc($plist) ){
											$prd_inc[] = $p['prd_id'];
										}
									}
									
								}
							}
						}
						
						// tableau des lignes de commande incluses dans la promotion (clé : prd_id + '-' + line_id)
						$prd_in_promo = array();
						if( $pmt_code ){
							if( $rprd_full = ord_products_get( $ord['id'] ) ){
								while( $p_full = ria_mysql_fetch_assoc($rprd_full) ){
									
									$is_inc = in_array($p_full['id'], $prd_inc);
									if( $all_catalog ){
										$is_inc = !$is_inc;
									}
									if( $is_inc ){
										$is_promo = false;
										if( !$inc_promotions ){
											// Détermine s'il s'agit d'un tarif promotionnel
											$pmt = prc_promotions_get( $p_full['id'], $ord['user'], 0, $p_full['qte'], $p_full['col_id'] );
											$is_promo = is_array($pmt) && sizeof($pmt) && $pmt['price_ht'] > 0;
										}
										if( !$is_promo || $inc_promotions ){
											$prd_in_promo[ $p_full['id'].'-'.$p_full['line'] ] = $p_full;
										}
									}
									
								}
								
								// répartition, si code promotion en montant
								if( $discountval ){
									
									// détermine le sous-total en promotion
									$sub_total = 0;
									foreach( $prd_in_promo as $pkey => $pvalue ){
										$sub_total += $pvalue['total_ht'];
									}
									
									// détermine le montant répartie sur chaque produit p/r à ce sous-total et au montant total remisé
									foreach( $prd_in_promo as $pkey => $pvalue ){
										$prd_in_promo[ $pkey ]['repartition'] =( $pvalue['total_ht'] / $sub_total ) * $discountval ;
									}
									
								}
								
							}
						}
						
						$i = 0;
						foreach( $ord_childs as $ordch ){
							$i++;
							
							// nom et adresse complet(e)
							$ordch['dlv_name'] = ucfirst2($ordch['dlv_firstname']).' '.strtoupper($ordch['dlv_lastname']);
							$ordch['dlv_address'] = $ordch['dlv_address1'].' '.$ordch['dlv_address2'];
							
							if( $ordch['rly_id'] ){
								// livraison en point-relais
								if( !( $relay = dlv_relays_get_simple( $ordch['rly_id'] ) ) ){
									error_log('[XlPos] Le point-relais n\'a pas pu être chargé.');
									continue;
								}
								$relay = ria_mysql_fetch_assoc($relay);
								$ordch['dlv_name'] = $relay['name'];
								$ordch['dlv_address'] = $relay['address1'].' '.$relay['address2'];
								$ordch['dlv_postal_code'] = $relay['zipcode'];
								$ordch['dlv_city'] = $relay['city'];
								$ordch['dlv_country'] = $relay['country'];
							}elseif( $ordch['str_id'] ){
								// livraison en magasin
								if( !( $store = dlv_stores_get( $ordch['str_id'] ) ) ){
									error_log('[XlPos] Le magasin n\'a pas pu être chargé.');
									continue;
								}
								$store = ria_mysql_fetch_assoc($store);
								$ordch['dlv_name'] = $store['name'];
								$ordch['dlv_address'] = $store['address1'].' '.$store['address2'];
								$ordch['dlv_postal_code'] = $store['zipcode'];
								$ordch['dlv_city'] = $store['city'];
								$ordch['dlv_country'] = $store['country'];
							}
							
							// chargement des lignes
							if( !$ordch['parent_id'] ){
								if( !( $rprd = ord_products_get( $ordch['id'] ) ) ){
									error_log('[XlPos] Echec de ord_products_get( '.$ordch['id'].' )' );
									continue;
								}
							}else{
								if( !( $rprd = ord_products_get_from_child( $ord['id'], $ordch['id'] ) ) ){
									error_log('[XlPos] Echec de ord_products_get_from_child( '.$ord['id'].', '.$ordch['id'].' )' );
									continue;
								}
							}
							
							if( !ria_mysql_num_rows($rprd) ){
								error_log('[XlPos] Aucune ligne n\'a été chargée pour la commande '.$ordch['id'] );
								continue;
							}
							
							$prd_array = array();
							while( $prd = ria_mysql_fetch_assoc( $rprd ) ){
								$prd_array[] = $prd;
							}
							
							// création des lignes "LI"
							$tmp_ord_li = array();
							$sub_total_ttc = 0;
							$j = 0;
							foreach( $prd_array as $prd ){
								$j++;
								
								$real_prd_ref = fld_object_values_get( $prd['id'], $config['fld_prd_code_reel'] );
								if( trim($real_prd_ref) != '' ){
									$prd['ref'] = $real_prd_ref;
								}
								
								if( $pmt_code && in_array($prd['id'].'-'.$prd['line'], array_keys($prd_in_promo)) ){
									$prd['base_tarif_ht'] = $prd['price_ht'];
									if( $discount ){
										$prd['price_ht'] *= (100 - $discount) / 100;
										$prd['price_ttc'] *= (100 - $discount) / 100;
									}elseif( $discountval ){
										$disc_ht = $prd_in_promo[ $prd['id'].'-'.$prd['line'] ]['repartition'];
										$prd['price_ht'] = ( $prd['total_ht'] - $disc_ht ) / $prd['real_qte'];
										$prd['price_ttc'] = round($prd['price_ht'] * $prd['tva_rate'], 2);
									}
								}
								
								$tmp_ord_li[] = array(
									'LI',
									'Article',
									$prd['ref'],
									$prd['qte'],
									round($prd['price_ht'], 2),
									$prd['price_ttc']
								);
								
								$ar_prd_price_impact[] = array(
									array($prd['ord_id'], $prd['id'], $prd['line']),
									isset($prd['base_tarif_ht']) ? $prd['base_tarif_ht'] : $prd['price_ht'],
									$prd['price_ht']
								);
								
								$sub_total_ttc += $prd['price_ttc'] * $prd['real_qte'];
								
							}
							
							// ajoute l'écart au sous-total de la commande
							$ordch['total_ttc'] = $sub_total_ttc;
							
							// création de l'entête "TI"
							$tmp_ord_ti = array(
								'TI',
								'Commande',
								( $count_colis==1 && $ordch['parent_id'] ? $ordch['parent_id'] : $ordch['id'] ),
								'EUR',
								'Caisse',
								trim($usr['adr_lastname'].' '.$usr['adr_firstname']),
								trim($usr['ref']) != '' ? $usr['ref'] : '#'.$usr['id'],
								trim($ordch['inv_address1'].' '.$ordch['inv_address2']),
								$ordch['inv_postal_code'],
								$ordch['inv_city'],
								$ordch['inv_country'],
								trim($ordch['dlv_name']),
								trim($ordch['dlv_address']),
								$ordch['dlv_postal_code'],
								$ordch['dlv_city'],
								$ordch['dlv_country'],
								$ord_date_text,
								$pay_libelle,
								round($ordch['total_ttc'], 2),
								$usr['email'],
								'INTERNET'
							);
							
							$orders[] = $tmp_ord_ti;
							foreach( $tmp_ord_li as $row_tmp ){
								$orders[] = $row_tmp;
							}
							
							$ord_id_ar[] = $ord['id'];
							
						}
						
						$users[] = array(
							'Caisse',
							'CAISSE',
							trim($usr['ref']) != '' ? $usr['ref'] : '#'.$usr['id'],
							$usr['title_name'],
							strtoupper($usr['adr_lastname']),
							ucfirst2($usr['adr_firstname']),
							$usr['address1'].' '.$usr['address2'],
							$usr['zipcode'],
							$usr['city'],
							$usr['country'],
							$usr['phone'],
							$usr['mobile'],
							$usr['email'],
							strtoupper($usr['adr_lastname']),
							ucfirst2($usr['adr_firstname']),
							trim($usr['address1'].' '.$usr['address2']),
							$usr['zipcode'],
							$usr['city'],
							$usr['country'],
							$usr['phone'],
							$usr['mobile'],
							$usr['email']
						);
						
						// met à jour le usr_ref
						if( trim($usr['ref']) == '' ){
							if( !gu_users_set_ref( $usr['email'], '#'.$usr['id'] ) ){
								error_log('[XlPos] Echec de gu_users_set_ref( '.$usr['email'].', '.'#'.$usr['id'].' )');
							}else{
								// marque le client comme étant synchronisé
								gu_users_set_is_sync( $usr['id'], true );
							}
						}
						
					}else{
						error_log('[XlPos] Echec de gu_users_get( '.$ord['user'].' )');
					}
				}else{
					error_log('[XlPos] Echec de ord_orders_get_with_adresses( 0, '.$ord['id'].' )');
				}
			}
		}else{
			error_log('[XlPos] Echec de ord_orders_get_new_to_import().');
		}
		
		if( sizeof($users) ){
			$users_no_rc = array();
			foreach( $users as $u ){
				$user_no_rc = array();
				foreach( $u as $v ){
					$user_no_rc[] = str_replace(array("\r\n", "\r", "\n"), array('\r\n', '\r', '\n'), $v); // , '"', ';' // , '[quot]', '[pv]'
				}
				$users_no_rc[] = $user_no_rc;
			}
			xlsoft_write_file( $usr_import_file, $users_no_rc, $usr_backup_import_file );
		}
		
		if( sizeof($orders) ){
			$orders_no_rc = array();
			foreach( $orders as $o ){
				$order_no_rc = array();
				foreach( $o as $v ){
					$order_no_rc[] = str_replace(array("\r\n", "\r", "\n"), array('\r\n', '\r', '\n'), $v); // , '"', ';' // , '[quot]', '[pv]'
				}
				$orders_no_rc[] = $order_no_rc;
			}
			xlsoft_write_file( $ord_import_file, $orders, $ord_backup_import_file );
		}
		
		// met à jour le tarif HT net unitaire de chaque ligne (modifié si code promotion)
		foreach( $ar_prd_price_impact as $ppri ){
			if( round($ppri[1], 2) != round($ppri[2], 2) ){
				if( !ord_products_update_sage_price( $ppri[0][0], $ppri[0][1], $ppri[0][2], $ppri[2] ) ){
					error_log('XLSOFT EXPORT : echec de ord_products_update_sage_price( '.$ppri[0][0].', '.$ppri[0][1].', '.$ppri[0][2].', '.$ppri[2].' )');
				}
			}
		}
		
		// mise à jour du N° de pièce (temporaire) et notification "shop-owner"
		$ord_id_ar = array_unique($ord_id_ar);
		foreach( $ord_id_ar as $ord_id ){
			ord_orders_piece_set( $ord_id, 'C'.$ord_id, false, false, true, false );
		}
		
	}
	
