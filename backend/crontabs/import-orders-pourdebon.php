<?php

/** \file import-orders-pourdebon.php
 *
 * Ce script est destiné à importer les nouvelles commandes réalisées sur la place de marché pourdebon.
 * Il est lancé automatiquement deux fois par heure aux minutes 29 et 59.
 */

if( !isset($ar_params) ){
	print "L'exécution de ce script nécessite l'appel de execute-script.php.\n";

	return;
}

/**
 * Cette fonction a pour but de simplifier l'envoi de mail d'erreur.
 *
 * @param string $message Obligatoire, le message d'erreur à envoyer
 * @return bool
 *
 * @internal Cette fonction ne doit pas être utilisé en dehors de ce fichier.
 */
function send_error_mail( $message ){
	global $config;

	$message = '['.date('Y-m-d H:i:s').'] Tenant #'.$config['tnt_id'].' : '.trim($message);

	// Si la config n'est pas activé, nous ne faisons que "logger" l'erreur.
	if( empty($config['notify_pourdebon_import_orders_errors']) ){
		return error_log($message);
	}

	if( ria_array_get($config, 'env_sandbox', false) ){
		$to = '<EMAIL>';
	}else{
		$to = '<EMAIL>';
	}

	$email = new email;
	$email->setFrom('Yuto <<EMAIL>>');
	$email->addTo($to);
	$email->setSubject('Erreur - Pourdebon Tenant #'.$config['tnt_id']);
	$email->addParagraph($message);

	return $email->send();
}

require_once('comparators/mirakl/PourDeBon.inc.php');
require_once('cfg.variables.inc.php');

// Détermine si il faut ou non activer le mode "test".
$is_test = isset($ar_params['test']) && $ar_params['test'] === 'test';
$date = ria_array_get($ar_params, 'date', '1 hour ago');

foreach( $configs as $config ){
	// Vérifie que la place de marché est bien activée pour ce client, dans le cas contraire, on passe au client suivant.
	if( !ctr_comparators_actived(CTR_POURDEBON_MIRAKL) ){
		continue;
	}

	$pourdebon = new PourDeBon($is_test);

	if( !gu_users_exists($pourdebon->getUserID()) ){
		send_error_mail('Le compte client #'.$pourdebon->getUserID().' n\'existe pas (import des commandes).');
		continue;
	}

	$prm = ctr_params_get_array(CTR_POURDEBON_MIRAKL);
	if( !array_key_exists('ORD_IMPORT_ADD_REF', $prm) ){
		send_error_mail('Impossible de récupérer la configuration de PourDeBon.');
		continue;
	}

	// Récupère toute les commandes Pourdebon validées mais pas encore synchronisées avec 
	$last_hour = new DateTime($date);

	// Récupère les commandes passées sur pourdebon.
	$pourdebons_orders = $pourdebon->getOrders(array(
		'start_date' => $last_hour->format('Y-m-dH:00:00'),
	));


	// Si il n'y a aucune commande en attente de validation, on passe au client suivant.
	if( !is_array($pourdebons_orders) || !count($pourdebons_orders) ){
		continue;
	}

	foreach( $pourdebons_orders as $pourdebon_order ){
		$error = false;

		// Contrôle que la commande n'a pas déjà été importée et confirmée
		$ria_order = ord_orders_get($pourdebon->getUserID(), 0, 0, 0, null, false, false, false, false, false, false, $pourdebon_order['order_id']);
		if( $ria_order && ria_mysql_num_rows($ria_order) ){
			continue;
		}

		// Validation de la commande.
		$pourdebon->acceptOrder($pourdebon_order);

		if( !in_array($pourdebon->getLastHttpCode(), array(204, 400)) ){
			send_error_mail('Impossible de valider la commande #'.$pourdebon_order['order_id'].' avec Mirakl');
			continue;
		}

		$pourdebon_date_created = new DateTime($pourdebon_order['created_date']);

		// Création de la nouvelle commande.
		if( !($new_ord = ord_orders_add_sage($pourdebon->getUserID(), $pourdebon_date_created->format('Y-m-d H:i:s'), 1, '', '', $pourdebon_order['order_id'], false)) ){
			send_error_mail('Impossible de créer la commande #'.$pourdebon_order['order_id']);
			continue;
		}

		// Création des lignes de commandes
		foreach( $pourdebon_order['order_lines'] as $pourdebon_product ){
			$r_product = prd_products_get_simple(0, $pourdebon_product['offer_sku']);
			if( !$r_product || !ria_mysql_num_rows($r_product) ){
				send_error_mail('Impossible de récupérer les informations sur le produit #'.$pourdebon_product['offer_sku']);
				$error = true;
				break;
			}

			$prd = ria_mysql_fetch_assoc($r_product);
			$prd['tva_rate'] = _TVA_RATE_DEFAULT;

			$r_tva = prc_tvas_get(0, false, $prd['id']);
			if( $r_tva && ria_mysql_num_rows($r_tva) ){
				$prd['tva_rate'] = ria_mysql_result($r_tva, 0, 'rate');
			}

			// Ajoute la ligne de commande.
			if( !ord_products_add_free($new_ord, $pourdebon_product['offer_sku'], $prd['name'], ($pourdebon_product['price_unit'] / $prd['tva_rate']), $pourdebon_product['quantity'], null, '', $prd['tva_rate']) ){
				send_error_mail('Impossible d\'ajouter le produit #'.$pourdebon_product['product_sku'].' à la commande #'.$new_ord);
				$error = true;
				break;
			}

			// Enregistre le numéro de ligne PourDeBon.
			if( !fld_object_values_set(array($new_ord, $prd['id']), _FLD_PRD_ORD_MKT_ID, $pourdebon_product['order_line_id']) ){
				send_error_mail('Impossible d\'enregister le numéro de ligne #'.$pourdebon_product['order_line_id'].' PourDeBon pour la commande #'.$new_ord);
				$error = true;
			}
		}

		// Ajout de la comission.
		if( !$error ){
			$commission = ria_array_get($pourdebon_order, 'total_commission', false);
			$prd_commission = $pourdebon->prdCommission();

			if( $commission && $prd_commission !== null ){
				if( !ord_products_add_free($new_ord, $prd_commission['ref'], $prd_commission['name'], (($commission / $prd_commission['tva_rate']) * -1), 1, null, '', $prd_commission['tva_rate']) ){
					send_error_mail('Impossible d\'ajouter la commission sur #'.$prd_commission['ref'].' à la commande #'.$new_ord);
					$error = true;
					break;
				}
			}
		}

		// Ajoute les produits définis dans "ord_import_add_ref" si renseigné.
		if( !empty($prm['ORD_IMPORT_ADD_REF']) ){
			foreach( explode(',', $prm['ORD_IMPORT_ADD_REF']) as $ref ){
				$ref = trim($ref);

				if( !prd_products_exists_ref($ref, false) ){
					continue;
				}

				$product = ria_mysql_fetch_assoc(prd_products_get_simple(0, $ref));
				if( !ord_products_add_free($new_ord, $ref, $product['title'], 0) ){
					send_error_mail('Erreur ord_products_add_free (ORD_IMPORT_ADD_REF) #'.$ref);
				}
			}
		}

		// Enregistre le champs avancé "PourDeBon informé de l'expédition" à "Non".
		if( !$error ){
			if( !fld_object_values_set($new_ord, _FLD_ORD_CTR_SHIPPED, 'Non') ){
				send_error_mail('Impossible de renseigner le champ "PourDeBon informé de l\'expédition" à "Non" pour la commande #'.$new_ord);
				$error = true;
			}
		}

		// Mise à jour du moyen de paiement.
		if( !$error ){
			if( !ord_orders_pay_type_set($new_ord, _PAY_COMPTE) ){
				send_error_mail('Impossible de mettre à jour le moyen de paiement pour la commande #'.$new_ord);
				$error = true;
			}
		}

		// Enregistre l'origine de commande ("pourdebon").
		if( !$error ){
			if( !stats_origins_add($new_ord, CLS_ORDER, null, 'pourdebon', 'pourdebon') ){
				send_error_mail('Impossible d\'enregistrer l\'origine de la commande #'.$new_ord);
				$error = true;
			}
		}

		// Valide la commande (statut 3 et 4).
		if( !$error ){
			if( !ord_orders_state_update($new_ord, _STATE_WAIT_VALIDATION, '', false) ){
				send_error_mail('Impossible de mettre à jour le statut de la commande #'.$new_ord.' (3).');
				$error = true;
			}
		}

		if( !$error ){
			if( !ord_orders_state_update($new_ord, _STATE_PAY_CONFIRM, '', false) ){
				send_error_mail('Impossible de mettre à jour le statut de la commande #'.$new_ord.' (4).');
				$error = true;
			}
		}

		// Renseigne la date de création de la commande.
		if( !$error ){
			if( !ord_orders_set_date($new_ord, $pourdebon_date_created->format('Y-m-d H:i:s'), true) ){
				send_error_mail('Impossible de mettre à jour la date de création de la commande #'.$new_ord);
				$error = true;
			}
		}

		// Estimation date d'expédition de la commande. La date vient au format zulu donc on la passe au format UTC (Europe/Paris).
		if( !$error ){
			$zulu_expedition_date = $pourdebon->getOrderAttribute($pourdebon_order, 'estimated-expedition-date');

			if( $zulu_expedition_date !== null ){
				$zulu_expedition_date = new DateTime($zulu_expedition_date['value']);
				$zulu_expedition_date->setTimezone(new DateTimeZone('Europe/Paris'));

				if( !fld_object_values_set($new_ord, 101170, $zulu_expedition_date->format('Y-m-d H:i:s')) ){
					send_error_mail('Impossible de mettre à jour l\'estimation de la date d\'expedition pour la commande #'.$new_ord);
					$error = true;
				}
			}
		}

		// Date de livraison de la commande. La date vient au format zulu donc on la passe au format UTC (Europe/Paris).
		if( !$error ){
			$zulu_shipping_date = null;
			$order_shipping_date = $pourdebon->getOrderAttribute($pourdebon_order, 'estimated-delivery-date');

			if( $order_shipping_date !== null ){
				$zulu_shipping_date = new DateTime($order_shipping_date['value']);
				$zulu_shipping_date->setTimezone(new DateTimeZone('Europe/Paris'));

				if( !ord_orders_set_date_livr($new_ord, $zulu_shipping_date->format('Y-m-d H:i:s'))){
					send_error_mail('Impossible de mettre à jour la date de livraison pour la commande #'.$new_ord);
					$error = true;
				}
			}
		}

		// Service de livraison de la commande.
		if( !$error ){
			if( dlv_services_exists(512) && !ord_orders_set_dlv_service($new_ord, 512, false)){
				send_error_mail('Impossible de mettre à jour le service de livraison pour la commande #'.$new_ord);
				$error = true;
			}
		}

		// Si la moindre erreur, alors on masque la commande
		if( $error ){
			if( !ord_orders_unmask($new_ord, true) ){
				send_error_mail('Impossible de masquer la commande #'.$new_ord);
			}
		}
	}
}