<?php
	set_include_path(dirname(__FILE__).'/../include');
	require_once('define.inc.php');
	require_once('strings.inc.php');
	require_once('db.inc.php');
	require_once('tenants.inc.php');
	require_once('RegisterGCP.inc.php');

	// Charge la connexion à tous les maria
	$ar_connections = RegisterGCP::create()->getConnections();
	foreach ($ar_connections as $connection) {
		print_r( $connection );

		// Connexion à la base de données
		RegisterGCPConnection::connect( $connection );

		// Charge le plus petit et le plus grand tnt_id
		$res_tnt = ria_mysql_query('select min(tnt_id) as min_tnt, max(tnt_id) as max_tnt from tnt_tenants');
		$t_ids = ria_mysql_fetch_assoc( $res_tnt );

		// Pour chaque tnt_id trouvé sur le maria
		for( $tnt_id=$t_ids['min_tnt'] ; $tnt_id <= $t_ids['max_tnt'] ; $tnt_id++ ){
			// Récupère les urls en doublons
			$sql = '
				select
					url_tnt_id as tnt_id, url_wst_id as wst_id, url_lng_code, url_extern, url_intern, url_code, url_public, url_cls_id, url_obj_id_0, url_obj_id_1,
					url_obj_id_2, count(*)
				from rew_rewritemap
				where url_tnt_id = '.$tnt_id.'
				group by url_tnt_id, url_wst_id, url_lng_code, url_extern, url_intern, url_code, url_public, url_cls_id, url_obj_id_0, url_obj_id_1, url_obj_id_2
				having count(*)>1
				order by count(*) desc
			';

			print PHP_EOL.'====================================================='.PHP_EOL;
			print $sql;
			print PHP_EOL.'====================================================='.PHP_EOL;

			$res = ria_mysql_query($sql);
			if( !$res ){
				return;
			}

			while( $r = ria_mysql_fetch_assoc($res) ){
				print $r['url_extern'].PHP_EOL;

				// Charge les identifiants des urls en doublons
				$res_del = ria_mysql_query('
					select url_id
					from rew_rewritemap
					where url_tnt_id = '.$r['tnt_id'].'
						and url_wst_id = '.$r['wst_id'].'
						and url_lng_code = "'.addslashes($r['url_lng_code']).'"
						and url_extern = "'.addslashes($r['url_extern']).'"
						and url_intern = "'.addslashes($r['url_intern']).'"
						and url_code = '.$r['url_code'].'
						and url_public = '.$r['url_public'].'
						and url_cls_id = '.$r['url_cls_id'].'
						and url_obj_id_0 = '.$r['url_obj_id_0'].'
						and url_obj_id_1 = '.$r['url_obj_id_1'].'
						and url_obj_id_2 = '.$r['url_obj_id_2'].'
				');

				// Un tableau est créé et celui-ci contient tous les ids d'une url en doublon sauf le premier trouvé (on conserve au moins une url)
				$ids = [];

				$first = true;
				while( $r_del = ria_mysql_fetch_assoc($res_del) ){
					if( $first ){
						$first = false;
						continue;
					}

					$ids[] = $r_del['url_id'];
				}

				// Suppression des ids des doublons
				ria_mysql_query('
					delete from rew_rewritemap
					where url_tnt_id = '.$r['tnt_id'].'
						and url_wst_id = '.$r['wst_id'].'
						and url_id in ('.implode( ', ', $ids ).')
				');
			}
		}
	}
