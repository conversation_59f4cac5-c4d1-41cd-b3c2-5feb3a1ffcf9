<?php
	/** \file send-yesbycash-notify.php
	 *
	 * 	Ce script est chargé de relancer les clients pour les paiements YesByCash non finalisé.
	 */

	if (!isset($ar_params)) {
		die("L'exécution de ce script nécessite l'appel de execute-script.php." . PHP_EOL);
	}

	require_once('YesByCash.inc.php');
	
	$fld_yesbycash_notify = 3205;
	
	foreach( $configs as $config ){
		if( !isset($config['yesbycash_notify_active']) || !$config['yesbycash_notify_active'] ){
			continue;
		}

		// récupère les comptes clients utilisés pour les places de marché
		$mkt_users = ctr_marketplaces_get_users();
		if( !is_array($mkt_users) ){
			$mkt_users = array();
		}

		$hours = isset($config['yesbycash_notify_delay']) && is_numeric($config['yesbycash_notify_delay']) && $config['yesbycash_notify_delay'] > 0 ? $config['yesbycash_notify_delay'] : 24;
		$date_start = date( 'Y-m-d H:00:00', strtotime('-'.( $hours + 10 ).' hours') );
		$date_end = date( 'Y-m-d H:00:00', strtotime('-'.$hours.' hours') );

		// Récupère les commandes en attente de paiement avec comme moyen de paiement : YesByCash
		$rorder = ord_orders_get_with_adresses( 0, 0, array(_STATE_WAIT_VALIDATION, _STATE_WAIT_PAY), '', $date_start, $date_end, false, false, null, false, false, false, false, _PAY_YESBYCASH );
		if( $rorder && ria_mysql_num_rows($rorder) ){
			while( $order = ria_mysql_fetch_assoc($rorder) ){
				// Les commandes liés au places de marché ne sont pas notifié
				if( in_array($order['user'], $mkt_users) ){
					continue;
				}

				// controle si la commande n'a pas été déjà notifiée
				$notified = fld_object_values_get($order['id'], $fld_yesbycash_notify);
				if( $notified ){
					continue;
				}

				if( YesByCash::sendPaymentNotify($order) ){
					if( !fld_object_values_set($order['id'], $fld_yesbycash_notify, 'Oui') ){
						error_log('Erreur dans send-yesbycash-notify : '.$order['id']);
					}
				}
			}
		}
	}