<?php
use \Reports\VisitReport;
use \Reports\VisitReportEmail;

/** ingroup excel_report_email
 *	Ce module gère l'exécution de l'envoi des rapports de visite au représentant
 */
set_include_path(__DIR__ . '/../include');

require_once('cfg.variables.inc.php');
require_once('Reports/VisitReport.inc.php');
require_once('Reports/VisitReportEmail.inc.php');
// Contrôle qu'un script n'est pas déjà lancé
$file = dirname(__FILE__).'/../locks/lock-send-seller-daily-reports.txt';
if (file_exists($file)) {
    error_log('Lancement simultané de "send-seller-daily-reports".');
    return;
}

fopen($file, 'w+');

unset($config);

$tnt_id = 0;
// check pour la valeur du tenant
if (isset($argv[1])) {
    if (!is_numeric($argv[1]) || $argv[1] <= 0) {
        error_log("Veuillez renseigner un identifiant de tenant valide (numérique supérieur à zéro).");
        return;
    }

    $tnt_id = $argv[1];
}

$configs = cfg_variables_get_all_tenants($tnt_id);
if (!is_array($configs) || !sizeof($configs)) {
    if (!unlink($file)) {
        error_log('Impossible de supprimer le fichier temporaire "lock-send-seller-daily-reports".');
    }
    return false;
}

$Date = new DateTime('yesterday');

foreach ($configs as $config) {
	$r_cfg_email = cfg_emails_get('daily-rp-alert');

	if (!$r_cfg_email || !ria_mysql_num_rows($r_cfg_email)) {
		continue;
	}
	$cfg_email = ria_mysql_fetch_assoc($r_cfg_email);

	$r_sellers = gu_users_get(0, '','',PRF_SELLER);

	if (!$r_sellers || !ria_mysql_num_rows($r_sellers)) {
		continue;
	}

	while( $seller = ria_mysql_fetch_assoc($r_sellers)) {
		$email = new Email;
		$email->setFrom($cfg_email['from']);
		$email->setTo($seller['email']);
		$email->setBcc($cfg_email['bcc']);
		$email->setReplyTo($cfg_email['reply-to']);

		$tmp_file = null;
		$r_reports = rp_reports_get(0, 0, 0, $seller['id'], $Date->format('Y-m-d'), $Date->format('Y-m-d'));
		if ($r_reports && ria_mysql_num_rows($r_reports)) {
			$tmp_file = tempnam(sys_get_temp_dir(), 'Rapport_de_visite_');
			try{
				$Report = VisitReport::createFromResource($r_reports);
				$Report->generate($tmp_file);

				$VisitReportEmail = new VisitReportEmail($email, $tmp_file);
				$VisitReportEmail->notify();

				if (file_exists($tmp_file)) {
					unlink($tmp_file);
				}
			}catch(Exception $e){
				if (file_exists($tmp_file)){
					unlink($tmp_file);
				}
				$tmp_file = null;
			}
		}
	}
}

if (!unlink($file)) {
	print 'Impossible de supprimer le lock ' . $file . PHP_EOL;
}

