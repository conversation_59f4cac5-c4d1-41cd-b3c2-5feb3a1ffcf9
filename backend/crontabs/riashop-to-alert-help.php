<?php
/** \file riashop-to-alert-subscribtion.php
 * 	Ce script permet d'envoyer un mail 7 jours avant la fin de la période d'essai
 *  afin de demander aux clients s'il a besoin d'aide pour utiliser RiaShop Essentiel / Business
 * 	Ce script est lancé une fois par jour.
 */

if (!isset($ar_params)) {
	error_log("L'exécution de ce script nécessite l'appel de execute-script.php." . PHP_EOL);
	exit;
}

require_once('users.inc.php');

// Récupération des différentes configurations mails pour RiaShop vente en ligne
$cfg_emails = cfg_emails_riashop_get();

// Parcours chaque tenant
foreach( $configs as $config ){
	global $ria_db_connect;

	// Récupère l'abonnement courant
	$sub = dev_subscribtions_btob_get(true);
	if( !is_array($sub) || !count($sub) ){
		continue;
	}

	// Si l'abonnement n'est pas dans une période d'essai, on ne va pas plus loin
	if( !is_numeric($sub['testing']) || $sub['testing'] <= 0 ){
		continue;
	}

	// Le mail est envoyé à tous les administrateur
	$r_user = gu_users_get(0, '', '', PRF_ADMIN);
	if( !$r_user || !ria_mysql_num_rows($r_user) ){
		continue;
	}

	// Détermine la date à laquelle envoyé l'alert (7 jours avant la fin de la période d'essai)
	$date_send = new Datetime( dateparse($sub['date_end']) );
	$date_send->modify('-7 days');

	// Vérifie que le mail de relance est à envoyer aujourd'hui
	$today = new Datetime();
	if( $today->format('Y-m-d') != $date_send->format('Y-m-d') ){
		continue;
	}

	// On regarde si un abonnement à venir existe, cela voudra dire que le client à déjà activer son abonnement Yuto, donc on envoie pas de mail
	$sub_future = dev_subscribtions_btob_get(false, true);
	if( is_array($sub_future) && count($sub_future) ){
		continue;
	}

  // Si le client a déja créé un produit, on envoie pas de mail
  $r_product = prd_products_get_simple();
  if( $r_product && ria_mysql_num_rows($r_product) > 0 ){
    continue;
  }

	// Récupère le type d'abonnement pour personnaliser le mail
	$package = ucfirst( RegisterGCP::getPackageBtoB($config['tnt_id']) );

	// Tous les administrateurs seront en destinataire du mail
	while( $user = ria_mysql_fetch_assoc($r_user) ){
		// Retire les super-admins de l'envoi
		if( !is_numeric($user['tenant']) || $user['tenant'] <= 0 ){
			continue;
		}

		// Désinscription à la relance
		if( in_array($user['email'], ['email@adesinscrire']) ){
			continue;
		}

		// Création du mail d'alerte
		$email = new Email();
		$email->setSubject('Besoin d\'aide pour utiliser RiaShop '.$package.' ?');
		$email->setFrom($cfg_emails['vel-riashop-notif']['from']);
		$email->setTo($user['email']);

		if( trim($cfg_emails['vel-riashop-notif']['bcc']) != '' ){
			$email->addBcc($cfg_emails['vel-riashop-notif']['bcc']);
		}

		// Préparation du contenu du mail
		$html = file_get_contents('/var/www/olivier_girard/start-riashop/htdocs/dist/template-email/alert-help.html');
		$html = str_replace('[prenom]', strtolower($user['adr_firstname']) == 'nc'? '' : $user['adr_firstname'], $html);
		$html = str_replace('[date]', $sub['date_end'], $html);
		$html = str_replace('[package]', $package, $html);
		$html = str_replace('[utm_campaign]', 'vel_btob_'.strtolower($package).'_alert_help', $html);
		$html = str_replace('[url_guide]', 'https://support.riashop.fr/', $html);

		$email->addHtml($html);
		$email->send();
	}
}
