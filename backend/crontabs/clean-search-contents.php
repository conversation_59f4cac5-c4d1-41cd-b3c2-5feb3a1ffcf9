<?php

	/**	\file clean-search-contents.php
	 *
	 *	Ce script est actuellement lancé chaque jour pour faire le ménage dans les tables liés au moteur de recherche.
	 *	Il est possible de le lancer pour un locataire spécifié en indiquant son identifiant en premier argument.
	 */

	set_include_path(dirname(__FILE__) . '/../include/');
	
	require_once('db.inc.php');
	require_once('define.inc.php');
	require_once('cfg.variables.inc.php');
	require_once('tenants.inc.php');
	
	require_once('categories.inc.php');
	require_once('products.inc.php');
	require_once('users.inc.php');
	require_once('tools.faq.inc.php');
	require_once('cgv.inc.php');
	require_once('news.inc.php');
	require_once('orders.inc.php');
	require_once('delivery.inc.php');
	require_once('cms.inc.php');
	require_once('brands.inc.php');
	require_once('documents.inc.php');

	unset($config);
	
	$tnt_id = isset($argv[1]) && is_numeric($argv[1]) && $argv[1] > 0 ? $argv[1] : 0;
	$delete = isset($argv[2]) && $argv[2] ? true : false;
	$show	= !isset($argv[2]) || !$argv[2] ? false : true;

	$just_order = !isset($argv[3]) || !$argv[3] ? false : true;

	// Charge l'ensemble des configurations clients
	$configs = cfg_variables_get_all_tenants( $tnt_id );
	if( !is_array($configs) || !sizeof($configs) ){
		return false;
	}

	// Reconstruction du cache pour chaque configuration
	$total_line = $total_group = 0;

	$total_del = 0;
	foreach( $configs as $config ){
		if ($show) {
			print 'Tenant : '.$config['site_name'].PHP_EOL;
		}

		// désindexation des commandes archivées depuis + de 12 mois
		$r_order = ord_orders_archived_get( date('Y-m-d', strtotime('-12 months')) );

		$count_order_cnt = 0;
		if ($r_order) {
			$iorder = 0;

			while ($order = ria_mysql_fetch_assoc($r_order)) {
				if (!is_numeric($order['cnt_id']) || $order['cnt_id'] <= 0) {
					continue;
				}

				if (($iorder%1000) == 0) {
					if ($show) {
						print '.';
					}

					sleep(1);
				}

				$res = true;
				if ($delete) {
					$res = search_index_clean( null, $order['cnt_id'] );
					$iorder++;
				}

				if ($show) {
					print '			delete : '.$order['id'].' - '.$order['cnt_id'].' : '.$res.PHP_EOL;
				}

				if ($res) {
					if ($delete) {
						ord_orders_set_cnt_id( $order['id'], 0 );
					}

					$count_order_cnt++;
				}
			}
		}

		if ($show) {
			print '	Suppression de '.ria_mysql_num_rows($r_order).' commandes archivees.'.PHP_EOL;
		}

		if ($just_order) {
			continue;
		}

		$r_content = search_contents_get();
		if (!$r_content) {
			continue;
		}

		if ($show) {
			print '	Contenu : '.ria_mysql_num_rows( $r_content ).PHP_EOL;
		}

		$count = array(
			'Catégorie de produits' => 0,
			'Produit' => 0,
			'Compte client' => 0,
			'Catégorie de questions' => 0,
			'Question fréquente' => 0,
			'Conditions Générales de Vente' => 0,
			'Actualités' => 0,
			'Commandes' => 0,
			'Magasin' => 0,
			'Gestion de contenu' => 0,
			'Marque' => 0,
			'Type de documents' => 0,
			'Document' => 0,
			'Article CGV' => 0,
		);

		$cnt_del = 0;
		$icontent = 0;
		while ($content = ria_mysql_fetch_assoc($r_content)) {
			if (!isset($content['tag']) || !is_numeric($content['tag']) || $content['tag'] <= 0) {
				continue;
			}

			if (($icontent%1000) == 0) {
				if ($show) {
					print '.';
				}

				sleep(1);
			}

			$icontent++;

			$del = false;
			switch ($content['type']) {
				case 1 : {	// Catégorie de produits
					if (!prd_categories_exists($content['tag'], false)) {
						$del = true;
						$count['Catégorie de produits']++;
					}
					break;
				}
				case 2 : {	// Produit
					if (!prd_products_exists($content['tag'])) {
						$del = true;
						$count['Produit']++;
					}
					break;
				}
				case 3 : {	// Compte client
					if (!gu_users_exists($content['tag'])) {
						$del = true;
						$count['Compte client']++;
					}
					break;
				}
				case 4 : {	// Catégorie de questions
					if (!faq_categories_exists($content['tag'])) {
						$del = true;
						$count['Catégorie de questions']++;
					}
					break;
				}
				case 5 : {	// Question fréquente
					if (!faq_questions_exists($content['tag'])) {
						$del = true;
						$count['Question fréquente']++;
					}
					break;
				}
				case 6 : {	// Conditions Générales de Vente
					if (!cgv_versions_exists($content['tag'])) {
						$del = true;
						$count['Conditions Générales de Vente']++;
					}
					break;
				}
				case 7 : {	// Actualités
					if (!news_exists($content['tag'])) {
						$del = true;
						$count['Actualités']++;
					}
					break;
				}
				case 8 : {	// Commandes
					if (!ord_orders_exists($content['tag'], 0, 0, true)) {
						$del = true;
						$count['Commandes']++;
					}
					break;
				}
				case 9 : {	// Magasin
					if (!dlv_stores_exists($content['tag'])) {
						$del = true;
						$count['Magasin']++;
					}
					break;
				}
				case 10 : {	// Gestion de contenu
					if (!cms_categories_exists($content['tag'])) {
						$del = true;
						$count['Gestion de contenu']++;
					}
					break;
				}
				case 12 : {	// Marque
					if (!prd_brands_exists($content['tag'])) {
						$del = true;
						$count['Marque']++;
					}
					break;
				}
				case 13 : {	// Type de documents
					if (!doc_types_exists($content['tag'])) {
						$del = true;
						$count['Type de documents']++;
					}
					break;
				}
				case 14 : {	// Document
					if (!doc_documents_exists($content['tag'])) {
						$del = true;
						$count['Document']++;
					}
					break;
				}
				case 15 : {	// Article CGV
					if (!cgv_articles_exists($content['tag'])) {
						$del = true;
						$count['Article CGV']++;
					}
					break;
				}
			}

			if ($del) {
				if ($delete) {
					// Suppression du contenu
					if ($show) {
						print '			delete : '.$content['type'].' '.$content['id'].' - '.$content['tag'].' : '.search_index_clean( null, $content['id'] ).PHP_EOL;
					}else{
						search_index_clean( null, $content['id'] );
					}
				}

				$cnt_del++;
			}
		}

		if ($show) {
			print '	'.($delete ? 'Supprimé' : 'À supprimer').' : '.$cnt_del.PHP_EOL;
			print_r($count);
		}

		$total_del = $total_del + $cnt_del;
	}

	if ($show) {
		print 'Total '.( $delete ? 'supprimé' : 'à supprimer' ).' : '.$total_del.PHP_EOL;
	}

