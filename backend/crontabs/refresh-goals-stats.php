<?php
	use Riashop\PriceWatching\models\LinearRaised\LinearRaisedGetter;

	/** \file refresh-goals-stats.php
	 * 	Ce script permet de stocker dans CouchDB les statistiques pré-calculées de chaque objectif Yuto participant au palmarès.
	 * 	Il est possible de le lancer sur une période personnalisée via execute-script.php, comme ceci :
	 *
	 * 	\code
	 * 		php execute-script.php --tnt_id 582 --script refresh-goals-stats --mode 0
	 * 			--other="show_process=true&start=2020-09-01&end=2020-12-02"
	 * 	\endcode
	 *
	 * 	Pour uniquement voir la progression, il faut rajouter ceci :
	 * 		--other="show_process=true"
	 *
	 * 	Il est possible de cumuler les deux options, comme ceci :
	 * 		--other="show_process=true&start=YYYY-MM-DD&end=YYYY-MM-DD"
	 *
	 * 	Ce script est divisé en deux parties
	 * 		- la première calcule des statistiques globales
	 * 		- la seconde calcule des statistiques sur chaque objet (produit, catégorie, marque) ayant fait l'objet d'un devis,
	 * 			d'une commande ou d'une facture
	 */

	require_once('couchDb.inc.php');
	require_once('calls.inc.php');
	require_once('goals.inc.php');
	require_once('notifications.inc.php');

	if (!isset($ar_params)) {
		print "L'exécution de ce script nécessite l'appel de execute-script.php.".PHP_EOL;
		exit;
	}

	$show_process = isset($ar_params['show_process']) && $ar_params['show_process'] == 'true';

	if( !function_exists('getSellerIds') ){

		/**
		 * Cette fonction permet de retourner la liste des commerciaux pour un context, usr_id / seller_id
		 */
		$cache_usr_seller_id = array();
		function getSellerIds( $usr_id, $current_seller_id=0 ){
			global $cache_usr_seller_id;

			$key = $usr_id.'-'.$current_seller_id;

			if( isset($cache_usr_seller_id[$key]) ){
				return $cache_usr_seller_id[$key];
			}

			// gestion du commercial 
			$sellers = array();

			// ajout du commercial liée à la factures 
			if( $current_seller_id ){
				$sellers[] = $current_seller_id;
			}

			// ajout du commercial liée au client
			$usr_seller_id = gu_users_get_seller_id($usr_id);
			if( $usr_seller_id ){
				$sellers[] = $usr_seller_id;
			}

			// gestion pour le commercial dans le cas ou on utilise les relations suivant le client
			$relation_usr_ids = array();
			$parents = rel_relations_hierarchy_parents_get_ids(REL_SELLER_HIERARCHY, $usr_id);
			if( $parents && sizeof($parents) ){
				$relation_usr_ids = $parents;
			}	
			// gestion pour le commercial dans le cas ou on utilise les relations suivant le commercial de la facture
			foreach( $sellers as $slr ){
				$rsellers = gu_users_get(0, "", "", PRF_SELLER, '', 0, '', false, false, $slr);
				if( $rsellers && ria_mysql_num_rows($rsellers) ){
					while($seller = ria_mysql_fetch_assoc($rsellers) ){
						$parents = rel_relations_hierarchy_parents_get_ids(REL_SELLER_HIERARCHY, $seller['id']);
						if( $parents && sizeof($parents) ){
							$relation_usr_ids = array_merge($relation_usr_ids, $parents);
						}	
					}
				}
			}

			if( sizeof($relation_usr_ids) ){
				$rsellers = gu_users_get($relation_usr_ids, "", "", PRF_SELLER);
				if( $rsellers && ria_mysql_num_rows($rsellers) ){
					while($seller = ria_mysql_fetch_assoc($rsellers) ){
						$sellers[] = $seller['seller_id'];
					}
				}
			}

			$sellers = array_values(array_unique($sellers));

			// transforme les seller_id en usr_id
			$seller_usr_id = array();

			foreach( $sellers as $slr ){
				$rsellers = gu_users_get(0, "", "", PRF_SELLER, '', 0, '', false, false, $slr);
				if( $rsellers && ria_mysql_num_rows($rsellers) ){
					while($seller = ria_mysql_fetch_assoc($rsellers) ){
						$seller_usr_id[] = $seller['id'];
					}
				}
			}

			$cache_usr_seller_id[$key] = $seller_usr_id;

			return $seller_usr_id;
		}

	}
	if( !function_exists('getGoalsSellerID') ){
		/** Cette fonction retourne l'identifiant du compte d'un représentant.
		 * 	Elle utilise une variable globale propre à ce script :
		 * 		- $stats : correspond au tableau des stats pour les représentants
		 * 	@param int $seller Obligatoire, code représentant (gu_users > usr_seller_id)
		 * 	@return int L'identifiant de son compte (gu_users > usr_id)
		 */
		function getGoalsSellerID( $seller ){
			global $stats;

			$usr_id = false;

			foreach ($stats as $key => $data) {
				if ($data['seller_id'] == $seller) {
					$usr_id = $key;
					break;
				}
			}

			return $usr_id;
		}
	}

	if( !function_exists('completedObjectsArray') ){
		/** Cette fonction permet de récupérer pour un article tous les liens de celui-ci : ses catégories et sa marque.
		 * 	Elle utilise des variables globales propre à ce script :
		 * 		- $ar_prds : tableau des articles
		 * 		- $ar_cats : tableau des catégories
		 * 		- $ar_brds : tableau des marques
		 * 	@param array $stats_seller Obligatoire, statistique lié aux représentant
		 * 	@param int $prd_id Obligatoire, identifiant du produit
		 */
		function completedObjectsArray( $stats_seller, $prd_id ){
			global $ar_prds, $ar_cats, $ar_brds;

			if( isset($ar_prds[ $prd_id ]) ){
				return;
			}

			foreach( $stats_seller as $key => $data ){
				$stats_seller[ $key ]['src_cls_id'] = CLS_PRODUCT;
				$stats_seller[ $key ]['src_obj_id_0'] = $prd_id;
			}

			// Ajout du produit dans le tableau
			$ar_prds[ $prd_id ] = [
				'id' => $prd_id,
				'seller' => $stats_seller
			];

			// Charge toutes les catégories liées à ce produit
			$r_cly = prd_classify_get( false, $prd_id );
			if( $r_cly ){
				while( $cly = ria_mysql_fetch_assoc($r_cly) ){
					$ar_prds[ $prd_id ]['cats'][ $cly['cat'] ] = $cly['cat'];

					// Recherche les catégories parentes
					$r_cat_parents = prd_categories_parents_get( $cly['cat'] );
					if( $r_cat_parents ){
						while( $cat_parents = ria_mysql_fetch_assoc($r_cat_parents) ){
							$ar_prds[ $prd_id ]['cats'][ $cat_parents['id'] ] = $cat_parents['id'];

							if( isset($ar_cats[ $cat_parents['id'] ]) ){
								continue;
							}

							foreach( $stats_seller as $key => $data ){
								$stats_seller[ $key ]['src_cls_id'] = CLS_CATEGORY;
								$stats_seller[ $key ]['src_obj_id_0'] = $cat_parents['id'];
							}

							$ar_cats[ $cat_parents['id'] ] = [
								'id' => $cat_parents['id'],
								'seller' => $stats_seller
							];
						}
					}

					if( isset($ar_cats[ $cly['cat'] ]) ){
						continue;
					}

					foreach( $stats_seller as $key => $data ){
						$stats_seller[ $key ]['src_cls_id'] = CLS_CATEGORY;
						$stats_seller[ $key ]['src_obj_id_0'] = $cly['cat'];
					}

					$ar_cats[ $cly['cat'] ] = [
						'id' => $cly['cat'],
						'seller' => $stats_seller
					];
				}
			}

			// Charge la marque du produit
			$prd_brd_id = prd_products_get_brand( $prd_id );
			if( is_numeric($prd_brd_id) && $prd_brd_id > 0 ){
				$ar_prds[ $prd_id ]['brd'] = $prd_brd_id;

				if( !isset($ar_brds[ $prd_brd_id ]) ){
					foreach( $stats_seller as $key => $data ){
						$stats_seller[ $key ]['src_cls_id'] = CLS_BRAND;
						$stats_seller[ $key ]['src_obj_id_0'] = $prd_brd_id;
					}

					$ar_brds[ $prd_brd_id ] = [
						'id' => $prd_brd_id,
						'seller' => $stats_seller
					];
				}
			}
		}
	}

	if( !function_exists('set_stats') ){
		/** Cette fonction permet de mettre à jour les stats globales pour un représentant.
		 * 	Elle utilise deux variables global
		 * 		- $stats : correspond au tableau des stats pour les représentants
		 * 		- $ar_data_update : correspondant au contexte des objectifs en place
		 * 	@param int $timestamp Obligatoire, timestamp de la journée en cours de mise à jour
		 * 	@param int $seller Obligatoire, identifiant du compte représentant
		 * 	@param string $code Obligatoire, code du kpi
		 * 	@param string $action Obligatoire, l'action a réalisé sur la valeur ('+' : ajoute $value, '=' : initialise avec $value)
		 * 	@param int|float $value Obligatoire, valeur pour ce kpi à cette date
		 */
		function set_stats( $timestamp, $seller, $code, $action, $value ){
			global $stats, $ar_data_update;

			// Contrôle que le kpi est bien active
			if( !array_key_exists($code, $ar_data_update[ $timestamp ][ $seller ]['kpi']) ){
				return;
			}

			if( isset($stats[ $seller ][ $code ]) ){
				switch( $action ){
					case '+':
						$stats[ $seller ][ $code ] += $value;
						break;
					default:
						$stats[ $seller ][ $code ] = $value;
						break;
				}
			}
		}
	}

	if( !function_exists('set_stats_cust') ){
		/** Cette fonction permet de mettre à jour les stats globales pour un combo représentant/client.
		 * 	Elle utilise deux variables global
		 * 		- $ar_stats_customers : correspond au tableau des stats pour les représentants/client
		 * 		- $ar_data_update : correspondant au contexte des objectifs en place
		 * 	@param int $timestamp Obligatoire, timestamp de la journée en cours de mise à jour
		 * 	@param int $user Obligatoire, identifiant du compte client
		 * 	@param int $seller Obligatoire, identifiant du compte représentant
		 * 	@param string $code Obligatoire, code du kpi
		 * 	@param string $action Obligatoire, l'action a réalisé sur la valeur ('+' : ajoute $value, '=' : initialise avec $value)
		 * 	@param int|float $value Obligatoire, valeur pour ce kpi à cette date
		 */
		function set_stats_cust( $timestamp, $user, $seller, $code, $action, $value ){
			global $ar_stats_customer, $ar_data_update;

			// Contrôle que le kpi est bien active
			if(
				( !isset($ar_data_update[ $timestamp ][ $seller ]['kpi-cust']) || !array_key_exists($code, $ar_data_update[ $timestamp ][ $seller ]['kpi-cust']) )
				|| !array_key_exists($user, $ar_data_update[ $timestamp ][ $seller ]['cust']) 
			){
				return;
			}

			if( isset($ar_stats_customer[ $user ][ $seller ][ $code ]) ){
				switch( $action ){
					case '+':
						$ar_stats_customer[ $user ][ $seller ][ $code ] += $value;
						break;
					default:
						$ar_stats_customer[ $user ][ $seller ][ $code ] = $value;
						break;
				}
			}
		}
	}

	if( !function_exists('refresh_goals_stats') ){
		/** Cette fonction permet de mettre à jour les statistiques selon le contexte d'objectifs défini sur la journée.
		 * 	@param DateTime $date Obligatoire, il s'agit de la journée à mettre à jour
		 * 	@param array $obj_data Obligatoire, il s'agit du contexte d'objectifs
		 */
		function refresh_goals_stats( $date, $obj_data ){
			global $show_process, $r_kpi, $ar_params;
			global $ar_prds, $ar_cats, $ar_brds, $stats, $ar_stats_customer;
			global $yuto_website, $config;

			$timestamp = $date->getTimestamp();
			$date_fr = $date->format('d/m/Y');

			if ($show_process) {
				print 'refresh_goals_stats date='.$date_fr.' obj_data=';
				print_r($obj_data);
				print PHP_EOL;
			}

			$year  = $date->format('Y');
			$month = $date->format('m');
			$day   = $date->format('d');

			// $date contiendra à partir de maintenant un string correspondant à la date en anglais
			// Utiliser pour toutes les utilisations des fonctions moteurs permettant de récupérer les statistiques
			$date = $date->format('Y-m-d');

			// Initialise un tableau d'identifiant des commandes et des factures pour permettre le calcul des statistiques liées aux objets
			$ar_ord_ids = [];
			$ar_inv_ids = [];

			{ // Calcul des statistiques globales
				//Initialise les statistiques
				$ar_init_stats = $stats;

				foreach ($stats as $seller => $value) {
					$stats[$seller]['year'] = $year;
					$stats[$seller]['month'] = $month;
					$stats[$seller]['day'] = $day;
					$stats[$seller]['date'] = $timestamp;

					{ // Dépendance entre kpi
						// Si un des kpi est détecté, les trois sont inclus
						if( array_key_exists('_STATS_AVG_BASKET', $obj_data['kpi']) ){
							$obj_data['kpi']['_STATS_CA_ORDER'] = '_STATS_CA_ORDER';
							$obj_data['kpi']['_STATS_NB_ORDER'] = '_STATS_NB_ORDER';
						}
					}

					ria_mysql_data_seek( $r_kpi, 0 );
					while ($kpi = ria_mysql_fetch_assoc($r_kpi)) {
						$stats[$seller][$kpi['code']] = 0;
					}
				}

				$ar_init_stats = $stats;

				// récupère les commandes de la journée (panier, devis, commande validée)
				$ord_filter = array(
					'wst_id' => $yuto_website['id'],
					'state_id' => array_merge(array(_STATE_BASKET, _STATE_DEVIS), ord_states_get_ord_valid())
				);

				if (isset($config['goals_stats_use_all_ord_origins']) && $config['goals_stats_use_all_ord_origins']) {
					$ord_filter = array(
						'state_id' => array_merge(array(_STATE_BASKET, _STATE_DEVIS), ord_states_get_ord_valid())
					);
				}

				$ar_stats_customer = [];

				$r_order = ord_orders_get_simple(
					array(),
					array(
						'start' => $date,
						'end'   => $date
					),
					$ord_filter,
					array(),
					array(
						'type'      => 'complete',
						'columns'   => array(
							'ord_stats_products' => 'stats_products'
						)
					)
				);

				// Statistiques liées aux paniers, devis et commandes
				if ($r_order) {

					while ($order = ria_mysql_fetch_assoc($r_order)) {
						$ar_states = ord_orders_states_get_array(
							$order['id'], true,
							['start' => date('Y-m-d 00:00:00', $timestamp), 'end' => date('Y-m-d 23:59:59', $timestamp)]
						);

						if( $config['goals_stats_use_author_ord'] ){
							// Charge un tableau des auteurs de changement de status
							if (!is_array($ar_states) || !count($ar_states)) {
								continue;
							}
						}else{
							if( !is_numeric($order['seller_id']) || $order['seller_id'] <= 0 ){
								continue;
							}

							$user = getGoalsSellerID($order['seller_id']);
							if (!is_numeric($user) || $user <= 0) {
								continue;
							}

							if (!is_array($ar_states) || !count($ar_states)) {
								$state_id = $order['state_id'];
								if (!in_array($state_id, array(_STATE_DEVIS ))) {
									$state_id = _STATE_WAIT_PAY;
								}

								$ar_states[$state_id] = 0;
							}

							foreach ($ar_states as $key => $value) {
								$ar_states[$key] = $user;
							}
						}

						if( !array_key_exists($order['usr_id'], $ar_stats_customer) ){
							$ar_stats_customer[ $order['usr_id'] ] = $ar_init_stats;
						}

						{ // Nombre de commande + Nombre de produits + Nombre de vente à 3 chiffres : l'auteur du statut 3 est égal au représentant
							if (array_key_exists(_STATE_WAIT_PAY, $ar_states) && array_key_exists($ar_states[_STATE_WAIT_PAY], $stats)) {
								$ar_ord_ids[] = $order['id'];

								set_stats( $timestamp, $ar_states[_STATE_WAIT_PAY], '_STATS_NB_ORDER', '+', 1);
								set_stats( $timestamp, $ar_states[_STATE_WAIT_PAY], '_STATS_NB_PRD_ORDER', '+', $order['stats_products'] );

								set_stats_cust( $timestamp, $order['usr_id'], $ar_states[_STATE_WAIT_PAY], '_STATS_NB_ORDER', '+', 1 );
								set_stats_cust( $timestamp, $order['usr_id'], $ar_states[_STATE_WAIT_PAY], '_STATS_NB_PRD_ORDER', '+', $order['stats_products'] );

								if ($order['total_ht'] >= 100) {
									set_stats( $timestamp, $ar_states[_STATE_WAIT_PAY], '_STATS_BIG_SALE', '+', 1);
									set_stats_cust( $timestamp, $order['usr_id'], $ar_states[_STATE_WAIT_PAY], '_STATS_BIG_SALE', '+', 1 );
								}
							}
						}

						{ // Chiffre d'affaire commandé : l'auteur du statut 3 est égal au représentant
							if (array_key_exists(_STATE_WAIT_PAY, $ar_states) && array_key_exists($ar_states[_STATE_WAIT_PAY], $stats)) {
								set_stats( $timestamp, $ar_states[_STATE_WAIT_PAY], '_STATS_CA_ORDER', '+', $order['total_ht']);
								set_stats_cust( $timestamp, $order['usr_id'], $ar_states[_STATE_WAIT_PAY], '_STATS_CA_ORDER', '+', $order['total_ht'] );
							}
						}

						{ // Marge commandée : l'auteur du statut 3 est égal au représentant
							if (array_key_exists(_STATE_WAIT_PAY, $ar_states) && array_key_exists($ar_states[_STATE_WAIT_PAY], $stats)) {
								$r_marge = ord_orders_get_profit(0, 0, false, false, 0, null, null, 0, $order['id']);
								if ($r_marge && ria_mysql_num_rows($r_marge)) {
									$marge = ria_mysql_fetch_assoc($r_marge);

									set_stats( $timestamp, $ar_states[_STATE_WAIT_PAY], '_STATS_MARGE_ORDER', '+', $marge['total_ht'] );
									set_stats_cust( $timestamp, $order['usr_id'], $ar_states[_STATE_WAIT_PAY], '_STATS_MARGE_ORDER', '+', $marge['total_ht'] );
								}
							}
						}

						{ // Création de devis : l'auteur du statut devis est égal au représentant
							if (array_key_exists(_STATE_DEVIS, $ar_states) && array_key_exists($ar_states[_STATE_DEVIS], $stats)) {
								set_stats( $timestamp, $ar_states[_STATE_DEVIS], '_STATS_NB_DEVIS', '+', 1 );
								set_stats_cust( $timestamp, $order['usr_id'], $ar_states[_STATE_DEVIS], '_STATS_NB_DEVIS', '+', 1 );
							}
						}

						{ // Nouveau client : première commande dont l'auteur est le commerciale depuis $config['devices_orders_months_history']
							if (array_key_exists(_STATE_WAIT_PAY, $ar_states) && array_key_exists($ar_states[_STATE_WAIT_PAY], $stats)) {
								$ord_filter = array(
									'wst_id' => $yuto_website['id'],
									'state_id' => ord_states_get_ord_valid(false, false),
								);

								if (isset($config['goals_stats_use_all_ord_origins']) && $config['goals_stats_use_all_ord_origins']) {
									$ord_filter = array(
										'state_id' => ord_states_get_ord_valid(false, false),
									);
								}

								// Filtre sur le compte client pour la recherche de sa première commande
								$ord_filter['usr_id'] = $order['usr_id'];

								// Récupère la première commande du compte client
								$r_ord_first = ord_orders_get_simple(
									array(),
									array(
										'start' => date('Y-m-d', strtotime('-'.$config['devices_orders_months_history'].' month', $timestamp))
									),
									$ord_filter,
									array(
										'sort' => array('date' => 'asc')
									),
									array(
										'type' => 'replace',
										'columns' => array(
											'ord_id' => 'id',
											'ord_usr_id' => 'usr_id'
										)
									)
								);

								if ($r_ord_first && ria_mysql_num_rows($r_ord_first)) {
									$ord_first = ria_mysql_fetch_assoc($r_ord_first);

									if ($ord_first['id'] == $order['id']) {
										set_stats( $timestamp, $ar_states[_STATE_WAIT_PAY], '_STATS_NB_CUSTOMERS', '+', 1 );
									}
								}
							}
						}
					}

					{ // Panier moyen : l'auteur du statut panier ou devis est égal au représentant
						foreach ($stats as $key => $data) {
							if(
								ria_array_key_exists(['_STATS_AVG_BASKET', '_STATS_CA_ORDER', '_STATS_NB_ORDER'], $data)
								&& $data['_STATS_NB_ORDER'] > 0
							){
								set_stats( $timestamp, $key, '_STATS_AVG_BASKET', '=', ($data['_STATS_CA_ORDER'] / $data['_STATS_NB_ORDER']) );
							}
						}
					}
				}

				// Statistiques liées aux factures
				$r_invoice = ord_invoices_get(0, 0, 0, false, false, true, false, 0, $date, $date);
				if ($r_invoice) {
					while ($invoice = ria_mysql_fetch_assoc($r_invoice)) {
						if( !array_key_exists($invoice['usr_id'], $ar_stats_customer) ){
							$ar_stats_customer[ $invoice['usr_id'] ] = $ar_init_stats;
						}

						$seller_usr_ids = getSellerIds($invoice['usr_id'], $invoice['seller_id']);

						// Récupère la ou les commandes rattachés à la facture
						if ($config['goals_stats_use_author_ord']) {
							$r_inv_orders = ord_invoices_orders_get($invoice['id']);
							if ($r_inv_orders && ria_mysql_num_rows($r_inv_orders)) {
								while ($inv_orders = ria_mysql_fetch_assoc($r_inv_orders)) {

										$ar_states = ord_orders_states_get_array($inv_orders['id']);

										if (!is_array($ar_states) || !count($ar_states)) {
											continue;
										}

										foreach (array(_STATE_BASKET, _STATE_DEVIS) as $state) {
											if (array_key_exists($state, $ar_states)) {
												$seller_usr_ids[] = $ar_states[$state];
												break;
											}
										}

								}
							}
						}

						$seller_usr_ids = array_unique($seller_usr_ids);
						

						$ar_marge = ord_invoices_get_average_totals(false, false, $invoice['id']);

						foreach( $seller_usr_ids as $usr_id ){
							if ( array_key_exists($usr_id, $stats)) {
								$ar_inv_ids[ $usr_id ][] = $invoice['id'];

								if( $show_process ){
									print 'Facture '.$invoice['id'].' sellers:'.$usr_id.PHP_EOL;
								}

								{ // Chiffre d'affaire facturé : l'auteur du statut panier est égal au représentant
									set_stats( $timestamp, $usr_id, '_STATS_CA_INVOICE', '+', $invoice['total_ht'] );
									set_stats_cust( $timestamp, $invoice['usr_id'], $usr_id, '_STATS_CA_INVOICE', '+', $invoice['total_ht'] );
								}

								{ // Marge facturée : l'auteur du statut panier est égal au représentant
									if (is_array($ar_marge) && array_key_exists('marge', $ar_marge)) {
										set_stats( $timestamp, $usr_id, '_STATS_MARGE_INVOICE', '+', $ar_marge['marge'] );
										set_stats_cust( $timestamp, $invoice['usr_id'], $usr_id, '_STATS_MARGE_INVOICE', '+', $ar_marge['marge'] );
									}
								}
								break;
							}
						}
					}
				}

				{ // Nombre d'appels + Durée des appels : L'auteur de l'appel correspond au représentant (gsl_answered = 1 pour avoir les appels réussi seulement)
					$periods = array('date1' => date('Y-m-d H:i:s', $timestamp), 'date2' => date('Y-m-d H:i:s', $timestamp + 86399));
					$calls = gcl_calls_get_by_view("", 0, 0, 0, $periods);

					if (is_array($calls) && count($calls)) {
						foreach ($calls as $call) {
							if (!array_key_exists($call['gcl_author_id'], $stats) || (isset($call['gcl_answered']) && $call['gcl_answered'] != 1)) {
								continue;
							}

							set_stats( $timestamp, $call['gcl_author_id'], '_STATS_NB_CALLS', '+', 1 );
							set_stats( $timestamp, $call['gcl_author_id'], '_STATS_TIME_CALLS', '+', $call['gcl_duration'] );
						}
					}
				}

				{ // Nombre de rendez-vous + Temps total des rendez-vous : L'auteur des rapports correspond au représentant
					foreach ($stats as $author_id => $data) {
						$r_report = stats_get_report_time_grouped($date, $date, '', 0, $author_id);
						if ($r_report) {
							while ($report = ria_mysql_fetch_assoc($r_report)) {
								set_stats( $timestamp, $author_id, '_STATS_NB_REPORT', '+', $report['rpr_total'] );
								set_stats( $timestamp, $author_id, '_STATS_TIME_REPORT', '+', $report['total_time'] );
							}
						}
					}
				}

				{ // Statistique lié au création / mise à jour de prospect, contact et client
					$notifs_add = nt_notifications_get_by_view(0, array('start' => date('Y-m-d 00:00:00', $timestamp), 'end' => date('Y-m-d 23:59:59', $timestamp)), 'asc', 0, 0);

					foreach ($notifs_add as $notif) {
						if (!array_key_exists($notif['nt_author_id'], $stats)) {
							continue;
						}

						if (!in_array($notif['nt_type_id'], array(NT_TYPE_USER_ADD, NT_TYPE_USER_UPD))) {
							continue;
						}

						// L'identifiant du compte client est manquant
						if (!isset($notif['objects'][0]['obj_id_0'])) {
							continue;
						}

						$parent_id = gu_users_get_parent_id($notif['objects'][0]['obj_id_0']);

						{ // Création de prospect
							if (!$parent_id && $notif['nt_type_id'] == NT_TYPE_USER_ADD) {
								set_stats( $timestamp, $notif['nt_author_id'], '_STATS_NB_PROSPECT', '+', 1 );

								// Récupère la date de premier appel
								{
									$first_call = gcl_calls_get_first_call($notif['objects'][0]['obj_id_0']);
									if (isdateheure($first_call)) {
										// Durée entre création de la fiche et premier appel (en seconde)
										$t_first_call = strtotime($first_call);
										if ($t_first_call < $timestamp) {
											$between = $timestamp - $t_first_call;

											if ($stats[$notif['nt_author_id']]['_STATS_FIRST_CALL'] > 0) {
												$avg = ($stats[$notif['nt_author_id']]['_STATS_FIRST_CALL'] + $between) / 2;
												set_stats( $timestamp, $notif['nt_author_id'], '_STATS_FIRST_CALL', '=', $avg );
											} else {
												set_stats( $timestamp, $notif['nt_author_id'], '_STATS_FIRST_CALL', '=', $between );
											}
										}
									}
								}
							}
						}

						{ // Mise à jour de fiche client
							if (!$parent_id && $notif['nt_type_id'] == NT_TYPE_USER_UPD) {
								set_stats( $timestamp, $notif['nt_author_id'], '_STATS_UPDATED_CUSTOMERS', '+', 1 );
							}
						}

						{ // Création de contact
							if ($parent_id && $notif['nt_type_id'] == NT_TYPE_USER_ADD) {
								set_stats( $timestamp, $notif['nt_author_id'], '_STATS_NB_CONTACT', '+', 1 );
							}
						}

						{ // Mise à jour de contact
							if ($parent_id && $notif['nt_type_id'] == NT_TYPE_USER_UPD) {
								set_stats( $timestamp, $notif['nt_author_id'], '_STATS_UPDATED_CONTACT', '+', 1 );
							}
						}
					}
				}

				{ // Statistique lié au relevé de linéaire
					$LinearRaisedGetter = new LinearRaisedGetter;
					$LinearRaisedGetter->fromTo(new DateTime(date('Y-m-d H:i:s', $timestamp), new DateTimeZone("UTC")), new DateTime(date('Y-m-d H:i:s', $timestamp+ 86399), new DateTimeZone("UTC")));
					$r_linear = $LinearRaisedGetter->query();
					if( $r_linear && ria_mysql_num_rows($r_linear) ){
						while ($linear = ria_mysql_fetch_assoc($r_linear)) {
							if( array_key_exists($linear['author_id'], $stats)){
								set_stats( $timestamp, $linear['author_id'], '_STATS_NB_LINEAR_RAISED', '+', 1 );
							}
						}
					}
				}

				{ // Statistiques lié aux points de fidelité
					$rewards_stats = rwd_stats_get_average_totals($date, $date);
					if ($rewards_stats) {
						while ($reward_stats = ria_mysql_fetch_assoc($rewards_stats)) {
							if (isset($reward_stats["usr_id"]) && $reward_stats["usr_id"] != null && isset($stats[$reward_stats["usr_id"]]) ){
								set_stats( $timestamp, $reward_stats["usr_id"], '_STATS_NB_REWARDS', '=', $reward_stats['points'] );
							}
						}
					}
				}
			}

			// Enregistrement des statistiques globales
			stats_goals_add( $stats, $date );

			if( isset($config['goals_on_objects_is_actived']) && $config['goals_on_objects_is_actived'] ){
				foreach( $ar_stats_customer as $k_usr_id=>$one_stat_cust ){
					foreach( $one_stat_cust as $k=>$d ){
						$one_stat_cust[ $k ]['cust_id'] = $k_usr_id;
					}

					stats_goals_add( $one_stat_cust, $date, [], $k_usr_id );
				}

				// Initialise le tableau des statistiques liées aux objets
				$ar_prds = $ar_cats = $ar_brds = [];

				{ // Calcul des statistiques liées aux objets
					// Réinitialise le résultat
					$result = $stats;

					// Clé kpi ignoré pour les statistiques objets
					$k_exclude = [
						'_STATS_NB_CALLS', '_STATS_TIME_CALLS', '_STATS_NB_REPORT', '_STATS_TIME_REPORT', '_STATS_BIG_SALE', '_STATS_NB_CUSTOMERS',
						'_STATS_UPDATED_CUSTOMERS', '_STATS_FIRST_CALL', '_STATS_NB_CONTACT', '_STATS_UPDATED_CONTACT', '_STATS_NB_PROSPECT',
						'_STATS_NB_LINEAR_RAISED', '_STATS_NB_REWARDS', '_STATS_AVG_BASKET'
					];

					foreach( $result as $key => $data ){
						foreach( $data as $k=>$v ){
							if( strstr($k, '_STATS_') ){
								$data[ $k ] = 0;
							}

							if( in_array($k, $k_exclude) ){
								unset( $data[$k] );
							}
						}

						$result[ $key ] = $data;
					};

					if( count($ar_ord_ids) > 0 ){
						// Le code qui suit est chargé d'initialisé les tableaux d'objets (produit, catégorie et marque)
						// En fonction des produits commandés, le tableau des catégories et des marques est chargé
						$r_ord_product = ord_products_get( $ar_ord_ids );
						if( $r_ord_product ){
							while( $ord_product = ria_mysql_fetch_assoc($r_ord_product) ){
								if( array_key_exists($ord_product['id'], $ar_prds) ){
									continue;
								}

								completedObjectsArray( $result, $ord_product['id'] );
							}
						}

						foreach( $ar_prds as $key_prd => $data_prd ){
							// Récupère les informations sur les commandes
							$r_ord_product = ord_products_get( $ar_ord_ids, false, $key_prd, '', null, false, 0, 0, -1, false, false, 0, false, false, false, false, false, false, false, null );
							if( !$r_ord_product || !ria_mysql_num_rows($r_ord_product) ){
								continue;
							}

							$ar_states = [];

							foreach( $data_prd['seller'] as $key_prd_seller => $data_prd_seller ){
								if( $r_ord_product ){
									ria_mysql_data_seek( $r_ord_product, 0 );

									$ar_ords = $ar_devis = [];
									$ca = $marge = 0;

									while( $ord_product = ria_mysql_fetch_assoc($r_ord_product) ){
										if( !array_key_exists($ord_product['ord_id'], $ar_states) ){
											$ar_states[ $ord_product['ord_id'] ] = ord_orders_states_get_array(
												$ord_product['ord_id'], true, array('start' => date('Y-m-d 00:00:00', $timestamp), 'end' => date('Y-m-d 23:59:59', $timestamp))
											);
										}

										// Contrôle que le créateur du devis est bien le représentant
										if(
											array_key_exists(_STATE_DEVIS, $ar_states[ $ord_product['ord_id'] ])
											&& $ar_states[ $ord_product['ord_id'] ][_STATE_DEVIS] == $key_prd_seller
										){
											$ar_devis[ $ord_product['ord_id'] ] = 1;
										}

										// Contrôle que le valideur de devis/panier est bien le représentant
										if(
											array_key_exists(_STATE_WAIT_PAY, $ar_states[ $ord_product['ord_id'] ])
											&& $ar_states[ $ord_product['ord_id'] ][_STATE_WAIT_PAY] == $key_prd_seller
										){
											$ar_ords[ $ord_product['ord_id'] ] = 1;
											$ca += $ord_product['price_ht'] * $ord_product['qte'];
											$marge += $ord_product['marge'];
										}
									}

									if(count($ar_ords)){
										if( isset($ar_prds[ $key_prd ]['seller'][ $key_prd_seller ]['_STATS_NB_ORDER']) ){
											$ar_prds[ $key_prd ]['seller'][ $key_prd_seller ]['_STATS_NB_ORDER'] = $ar_ords;
										}
										if( isset($ar_prds[ $key_prd ]['seller'][ $key_prd_seller ]['_STATS_NB_DEVIS']) ){
											$ar_prds[ $key_prd ]['seller'][ $key_prd_seller ]['_STATS_NB_DEVIS'] = $ar_devis;
										}
										if( isset($ar_prds[ $key_prd ]['seller'][ $key_prd_seller ]['_STATS_CA_ORDER']) ){
											$ar_prds[ $key_prd ]['seller'][ $key_prd_seller ]['_STATS_CA_ORDER'] += $ca;
										}
										if( isset($ar_prds[ $key_prd ]['seller'][ $key_prd_seller ]['_STATS_MARGE_ORDER']) ){
											$ar_prds[ $key_prd ]['seller'][ $key_prd_seller ]['_STATS_MARGE_ORDER'] += $marge;
										}

										if( isset($ar_prds[ $key_prd ]['cats']) ){
											foreach( $ar_prds[ $key_prd ]['cats'] as $one_cat ){
												if( isset($ar_cats[ $one_cat ]['seller'][ $key_prd_seller ]['_STATS_NB_ORDER']) ){
													$ar_cats[ $one_cat ]['seller'][ $key_prd_seller ]['_STATS_NB_ORDER'] = $ar_ords;
												}
												if( isset($ar_cats[ $one_cat ]['seller'][ $key_prd_seller ]['_STATS_NB_DEVIS']) ){
													$ar_cats[ $one_cat ]['seller'][ $key_prd_seller ]['_STATS_NB_DEVIS'] = $ar_devis;
												}
												if( isset($ar_cats[ $one_cat ]['seller'][ $key_prd_seller ]['_STATS_CA_ORDER']) ){
													$ar_cats[ $one_cat ]['seller'][ $key_prd_seller ]['_STATS_CA_ORDER'] += $ca;
												}
												if( isset($ar_cats[ $one_cat ]['seller'][ $key_prd_seller ]['_STATS_MARGE_ORDER']) ){
													$ar_cats[ $one_cat ]['seller'][ $key_prd_seller ]['_STATS_MARGE_ORDER'] += $marge;
												}
											}
										}

										$brd_id = isset($ar_prds[ $key_prd ]['brd']) ? $ar_prds[ $key_prd ]['brd'] : 0;
										if( is_numeric($brd_id) && $brd_id > 0 ){
											if( isset($ar_brds[ $brd_id ]['seller'][ $key_prd_seller ]['_STATS_NB_ORDER']) ){
												$ar_brds[ $brd_id ]['seller'][ $key_prd_seller ]['_STATS_NB_ORDER'] = $ar_ords;
											}
											if( isset($ar_brds[ $brd_id ]['seller'][ $key_prd_seller ]['_STATS_NB_DEVIS']) ){
												$ar_brds[ $brd_id ]['seller'][ $key_prd_seller ]['_STATS_NB_DEVIS'] = $ar_devis;
											}
											if( isset($ar_brds[ $brd_id ]['seller'][ $key_prd_seller ]['_STATS_CA_ORDER']) ){
												$ar_brds[ $brd_id ]['seller'][ $key_prd_seller ]['_STATS_CA_ORDER'] += $ca;
											}
											if( isset($ar_brds[ $brd_id ]['seller'][ $key_prd_seller ]['_STATS_MARGE_ORDER']) ){
												$ar_brds[ $brd_id ]['seller'][ $key_prd_seller ]['_STATS_MARGE_ORDER'] += $marge;
											}
										}
									}
								}
							}
						}
					}
				}

				// Seulement s'il existe des objects de CA facturé ou de marge facturé sur les objects
				if(
					isset($obj_data['kpi-objs'])
					&& (
						in_array('_STATS_CA_INVOICE', $obj_data['kpi-objs'])
						|| in_array('_STATS_MARGE_INVOICE', $obj_data['kpi-objs'])
					)
				){ // Calcul des statistiques de facturations liées aux objects
					$cache_relation = [];
					if( !count($ar_inv_ids) && !$config['goals_stats_use_author_ord'] ){
						// Récupération des factures de la journée
						$r_invoice = ord_invoices_get(0, 0, 0, false, false, true, false, 0, $date, $date);
						if( $r_invoice ){
							while( $invoice = ria_mysql_fetch_assoc($r_invoice) ){
								if( is_numeric($invoice['seller_id']) && $invoice['seller_id'] > 0 ){
									$ar_inv_ids[ $invoice['seller_id'] ][] = $invoice['id'];
								}

								if( isset($config['goals_stats_use_relations_sellers']) && $config['goals_stats_use_relations_sellers'] ){
									// Recherche les relations entre le client de la facture et un ou des représentants
									if( array_key_exists($invoice['usr_id'], $cache_relation) ){
										// Utilise un cache qui se construit au fur et à mesure que le script s'exécute
										foreach( $cache_relation[ $invoice['usr_id'] ] as $one_seller ){
											$ar_inv_ids[ $one_seller ][] = $invoice['id'];
										}
									}else{
										foreach(getSellerIds($invoice['usr_id'], $invoice['seller_id']) as $slr){
											$ar_inv_ids[ $slr ][] = $invoice['id'];
											$cache_relation[ $invoice['usr_id'] ][] = $slr;
										}
									}
								}
							}
						}
					}

					foreach( $ar_inv_ids as $seller=>$inv_ids ){
						// Récupère les lignes de factures
						$r_inv_product = ord_inv_products_get( $inv_ids );

						if( $r_inv_product ){
							while( $inv_product = ria_mysql_fetch_assoc($r_inv_product) ){
								// Si le produit n'est pas déjà présent dans les stats, on initisalise son tableau de stats
								if( !array_key_exists($inv_product['id'], $ar_prds) ){
									completedObjectsArray( $result, $inv_product['id'] );
								}

								if( isset($ar_prds[ $inv_product['id'] ]['seller'][ $seller ]['_STATS_CA_INVOICE']) ){
									$ar_prds[ $inv_product['id'] ]['seller'][ $seller ]['_STATS_CA_INVOICE'] += $inv_product['total_ht'];
								}
								if( isset($ar_prds[ $inv_product['id'] ]['seller'][ $seller ]['_STATS_MARGE_INVOICE']) ){
									$ar_prds[ $inv_product['id'] ]['seller'][ $seller ]['_STATS_MARGE_INVOICE'] += $inv_product['total_ht'] - ($inv_product['purchase_avg'] * $inv_product['qte']);
								}

								if( isset($ar_prds[ $inv_product['id'] ]['cats']) ){
									foreach( $ar_prds[ $inv_product['id'] ]['cats'] as $one_cat ){
										if( isset($ar_cats[ $one_cat ]['seller'][ $seller ]['_STATS_CA_INVOICE']) ){
											$ar_cats[ $one_cat ]['seller'][ $seller ]['_STATS_CA_INVOICE'] += $inv_product['total_ht'];
										}
										if( isset($ar_cats[ $one_cat ]['seller'][ $seller ]['_STATS_MARGE_INVOICE']) ){
											$ar_cats[ $one_cat ]['seller'][ $seller ]['_STATS_MARGE_INVOICE'] += $inv_product['total_ht'] - ($inv_product['purchase_avg'] * $inv_product['qte']);
										}
									}
								}

								$brd_id = isset($ar_prds[ $inv_product['id'] ]['brd']) ? $ar_prds[ $inv_product['id'] ]['brd'] : 0;
								if( is_numeric($brd_id) && $brd_id > 0 ){
									if( isset($ar_brds[ $brd_id ]['seller'][ $seller ]['_STATS_CA_INVOICE']) ){
										$ar_brds[ $brd_id ]['seller'][ $seller ]['_STATS_CA_INVOICE'] += $inv_product['total_ht'];
									}
									if( isset($ar_brds[ $brd_id ]['seller'][ $seller ]['_STATS_MARGE_INVOICE']) ){
										$ar_brds[ $brd_id ]['seller'][ $seller ]['_STATS_MARGE_INVOICE'] += $inv_product['total_ht'] - ($inv_product['purchase_avg'] * $inv_product['qte']);
									}
								}
							}
						}
					}
				}

				// Enregistre les statistiques de commandes liées aux produits
				// Si aucun objectif ne porte sur des produits, les statistiques ne sont pas sauvegardées
				if( isset($obj_data['objs']) && array_key_exists(CLS_PRODUCT, $obj_data['objs']) ){
					foreach( $ar_prds as $key_prd => $data_prd ){
						// Contrôle que le produit est bien inclut dans un objectifs avant de sauvegarder ses statistiques
						if( array_key_exists($key_prd, $obj_data['objs'][CLS_PRODUCT]) ){
							foreach( $data_prd['seller'] as &$one_seller ){
								if( isset($one_seller['_STATS_NB_ORDER']) && is_array($one_seller['_STATS_NB_ORDER']) ){
									$one_seller['_STATS_NB_ORDER'] = count($one_seller['_STATS_NB_ORDER']);
								}else{
									$one_seller['_STATS_NB_ORDER'] = 0;
								}

								if( isset($one_seller['_STATS_NB_DEVIS']) && is_array($one_seller['_STATS_NB_DEVIS']) ){
									$one_seller['_STATS_NB_DEVIS'] = count($one_seller['_STATS_NB_DEVIS']);
								}else{
									$one_seller['_STATS_NB_DEVIS'] = 0;
								}
							}

							stats_goals_add( $data_prd['seller'], $date, ['cls_id' => CLS_PRODUCT, 'obj_id_0' => $key_prd] );
						}
					}
				}

				// Enregistre les statistiques liées aux catégories
				// Si aucun objectif ne porte sur des catégories, les statistiques ne sont pas sauvegardées
				if( isset($obj_data['objs']) && array_key_exists(CLS_CATEGORY, $obj_data['objs']) ){
					foreach( $ar_cats as $key_cat => $data_cat ){
						// Contrôle que la catégorie est bien inclut dans un objectifs avant de sauvegarder ses statistiques
						if( array_key_exists($key_cat, $obj_data['objs'][CLS_CATEGORY]) ){
							foreach( $data_cat['seller'] as &$one_seller ){
								if( isset($one_seller['_STATS_NB_ORDER']) && is_array($one_seller['_STATS_NB_ORDER']) ){
									$one_seller['_STATS_NB_ORDER'] = count($one_seller['_STATS_NB_ORDER']);
								}else{
									$one_seller['_STATS_NB_ORDER'] = 0;
								}

								if( isset($one_seller['_STATS_NB_DEVIS']) && is_array($one_seller['_STATS_NB_DEVIS']) ){
									$one_seller['_STATS_NB_DEVIS'] = count($one_seller['_STATS_NB_DEVIS']);
								}else{
									$one_seller['_STATS_NB_DEVIS'] = 0;
								}
							}

							stats_goals_add( $data_cat['seller'], $date, ['cls_id' => CLS_CATEGORY, 'obj_id_0' => $key_cat] );
						}
					}
				}

				// Enregistre les statistiques liées aux marques
				// Si aucun objectif ne porte sur des marques, les statistiques ne sont pas sauvegardées
				if( isset($obj_data['objs']) && array_key_exists(CLS_BRAND, $obj_data['objs']) ){
					foreach( $ar_brds as $key_brd => $data_brd ){
						// Contrôle que la marque est bien inclut dans un objectifs avant de sauvegarder ses statistiques
						if( array_key_exists($key_brd, $obj_data['objs'][CLS_BRAND]) ){
							foreach( $data_brd['seller'] as &$one_seller ){
								if( isset($one_seller['_STATS_NB_ORDER']) && is_array($one_seller['_STATS_NB_ORDER']) ){
									$one_seller['_STATS_NB_ORDER'] = count($one_seller['_STATS_NB_ORDER']);
								}else{
									$one_seller['_STATS_NB_ORDER'] = 0;
								}

								if( isset($one_seller['_STATS_NB_DEVIS']) && is_array($one_seller['_STATS_NB_DEVIS']) ){
									$one_seller['_STATS_NB_DEVIS'] = count($one_seller['_STATS_NB_DEVIS']);
								}else{
									$one_seller['_STATS_NB_DEVIS'] = 0;
								}
							}

							stats_goals_add( $data_brd['seller'], $date, ['cls_id' => CLS_BRAND, 'obj_id_0' => $key_brd] );
						}
					}
				}
			}
		}
	}

	foreach( $configs as $config ){
		{ // Récupère le site yuto du locataire
			$yuto_website = false;

			$r_yuto_website = wst_websites_get(0, false, $config['tnt_id'], false, 6);
			if( $r_yuto_website && ria_mysql_num_rows($r_yuto_website) ){
				$yuto_website = ria_mysql_fetch_assoc($r_yuto_website);
			}

			if ($yuto_website === false) {
				continue;
			}
		}

		// Variable globale à tout le script
		$ar_data_update = []; // Contexte des objectifs en place (cf. "Création du contexte des objectifs")
		$ar_prds = $ar_cats = $ar_brds = []; // Tableau des statistiques liées aux objets
		$stats = []; // Tableau des stats pour les représentants
		$ar_stats_customer = []; // Tableau des stats pour les représentants/client

		// Récupère la date de dernière mise à jour de stats
		// $last_execution = new DateTime( '1972-01-01 00:00:00' );
		$string_date = cfg_overrides_get_value( 'goals_stats_last_execution', $yuto_website['id'] );
		if( trim($string_date) == '' ){
			$string_date = '2021-02-01 01:00:00';
		}

		$last_execution = new DateTime( $string_date, new DateTimeZone("UTC") );

		{// Création du contexte des objectifs
			// À la fin de ce chargement, $ar_data_update contiendra pour chaque jours quels sont les objectifs en place
			// pour quel(s) représentant(s), pour le(s)quel(s) de ces clients et pour quel(s) objet(s) (produits, catégories, marques)
			// $ar_data_update ne contient que des jours pour lesquels un objectif est en place et seulement si cet objectif :
			// 	- a été créé ou mis à jour depuis la dernière exécution du script
			// 	- est en vigueur pour la période entre la dernière exécution et maintenant

			// Récupération des objectifs ayant été créé / mis à jour depuis le dernier lancement
			$r_obj = obj_periods_get( null, null, null, null, null, false );
			if( $r_obj ){
				while( $obj = ria_mysql_fetch_assoc($r_obj) ){
					// Les statistiques seront mise à jour si
					// - l'objectif a été créé ou mise à jour depuis le dernier lancement
					// - la période de l'objectif est concerné par une date entre le dernier lancement et maintenant
					$include = false;

					$d_add 		= new DateTime( $obj['date_created'], new DateTimeZone("UTC") );
					$d_upd 		= new DateTime( $obj['date_modified'], new DateTimeZone("UTC") );
					$d_start 	= new DateTime( $obj['date_start'], new DateTimeZone("UTC") );
					$d_end 		= new DateTime( $obj['date_end'], new DateTimeZone("UTC"));


					if( isset($ar_params['start'], $ar_params['end']) && isdate($ar_params['start']) && isdate($ar_params['end']) ){
						$t1 = new DateTime( $ar_params['start'], new DateTimeZone("UTC") );
						$t2 = new DateTime( $ar_params['end'], new DateTimeZone("UTC") );

						if( ( $d_start->getTimestamp() >= $t1->getTimestamp() && $d_start->getTimestamp() <= $t2->getTimestamp() )
						|| ( $d_end->getTimestamp() >= $t1->getTimestamp() && $d_end->getTimestamp() <= $t2->getTimestamp() )
					){
							$include = true;
						}
					}


					if( !$include ){
						if( $d_add->getTimestamp() > $last_execution->getTimestamp() ){
							// Objectif créé depuis la dernière mise à jour des statistiques
							$include = true;
						}elseif( $d_upd->getTimestamp() > $last_execution->getTimestamp() ){
							// Objectif mis à jour depuis la dernière mise à jour des statistiques
							$include = true;
						}elseif(
								$last_execution->getTimestamp() >= $d_start->getTimestamp() && $last_execution->getTimestamp() <= $d_end->getTimestamp()
							|| time() >= $d_start->getTimestamp() && time() <= $d_end->getTimestamp()
						){
							// L'objectif correspond à la période entre la dernière mise à jour des statistiques et aujourd'hui
							$include = 'now';
						}

						if( $include === false ){
							continue;
						}
					}

					for( $i = $d_start->getTimestamp() ; $i <= $d_end->getTimestamp() ; $i=$i+(24*60*60) ){
						// Seules les statistiques des journées, au plus tard aujourd'hui, sont inclues
						if( $i > time() ){
							continue;
						}

						// Si l'objectif est inclut car il correspond à la période d'aujourd'hui
						// Alors seuls les 7 derniers jours seront rafraichit
						if( $include === 'now' ){
							$tmp = new DateTime('now', new DateTimeZone("UTC"));
							$tmp->modify('-7 days');
							if( $i < $tmp->getTimestamp() ){
								continue;
							}
						}

						$seller_id = $cust_id = 0;
						if( is_numeric($obj['seller_id']) && $obj['seller_id'] > 0 ){
							$seller_id = $obj['seller_id'];
							$cust_id = $obj['usr_id'];
						}else{
							$seller_id = $obj['usr_id'];
						}

						// Si aucune données pour cette date, on initialise le tableau
						if( !isset($ar_data_update[ $i ][ $seller_id ]) ){
							$ar_data_update[ $i ][ $seller_id ] = [
								// 'seller' => [],
								'cust' => [],
								'objs' => [],
								'kpi' => [],
							];
						}

						if( $cust_id > 0 ){
							$ar_data_update[ $i ][ $seller_id ]['cust'][ $cust_id ] = $cust_id;
							$ar_data_update[ $i ][ $seller_id ]['kpi-cust'][ $obj['obj_code'] ] = $obj['obj_code'];
						}elseif( $obj['cls_id'] > 0 ){
							$ar_data_update[ $i ][ $seller_id ]['kpi-objs'][ $obj['obj_code'] ] = $obj['obj_code'];
						}else{
							$ar_data_update[ $i ][ $seller_id ]['kpi'][ $obj['obj_code'] ] = $obj['obj_code'];
						}

						if( is_numeric($obj['cls_id']) && $obj['cls_id'] > 0 ){
							$ar_data_update[ $i ][ $seller_id ]['objs'][ $obj['cls_id'] ][ $obj['obj_id_0'] ] = $obj['obj_id_0'];
						}
					}
				}

				ksort($ar_data_update);

				if( $show_process ){
					print 'Création du contexte des objectifs size ar_data_update= '.sizeof($ar_data_update).PHP_EOL;
				}
			}
		}

		{ // Chargement de la configuration et des données générales
			// Le contexte est défini par défaut sur le site "Yuto"
			$config['wst_id'] = $yuto_website['id'];
			cfg_variables_load($config);

			if (!isset($config['goals_stats_use_author_ord'])) {
				$config['goals_stats_use_author_ord'] = false;
			}

			// Récupère tous les représentants
			$r_seller = gu_users_get(0, '', '', array(PRF_ADMIN, PRF_SELLER));
			if (!$r_seller || !ria_mysql_num_rows($r_seller)) {
				continue;
			}

			// Récupère tous les types d'objectifs (kpi)
			$r_kpi = obj_objectifs_get_kpi();
			if( !$r_kpi || !ria_mysql_num_rows($r_kpi) ){
				continue;
			}

			if( !isset($config['goals_on_objects_is_actived']) || !$config['goals_on_objects_is_actived'] ){
				$ar_data_update = [];

				$date1 = new DateTime('now', new DateTimeZone("UTC"));
				$date2 = new DateTime('now', new DateTimeZone("UTC"));

				// Calcul sur les 7 derniers jours par défaut
				$date1->modify( '-7 days' );

				if( isset($ar_params['start'], $ar_params['end']) && isdate($ar_params['start']) && isdate($ar_params['end']) ){
					$t1 = new DateTime( $ar_params['start'], new DateTimeZone("UTC") );
					$t2 = new DateTime( $ar_params['end'], new DateTimeZone("UTC") );

					if( $t1->getTimestamp() <= $t2->getTimestamp() ){
						$date1 = $t1;
						$date2 = $t2;
					}
				}

				for( $i = $date1->getTimestamp() ; $i <= $date2->getTimestamp() ; $i = ($i + (24 * 60 * 60)) ){
					while( $seller = ria_mysql_fetch_assoc($r_seller) ){
						$ar_data_update[ $i ][ $seller['id'] ] = [
							'cust' => [],
							'objs' => [],
							'kpi'  => [],
						];

						while( $kpi = ria_mysql_fetch_assoc($r_kpi) ){
							$ar_data_update[ $i ][ $seller['id'] ]['kpi'][ $kpi['code'] ] = $kpi['code'];
						}

						ria_mysql_data_seek( $r_kpi, 0 );
					}

					ria_mysql_data_seek( $r_seller, 0 );
				}
			}
		}

		// Début de la mise à jour des statistiques
		// Pour chaque jours
		foreach( $ar_data_update as $time=>$time_data ){
			// Pour chaque représentant ayant un objectif sur cette journée là
			foreach( $time_data as $seller_id=>$obj_data ){
				$date = new DateTime('now', new DateTimeZone("UTC"));
				$date->setTimestamp( $time );

				$stats = [];
				$ar_stats_customer = [];

				// Initialise le tableau de statistiques du représentant
				ria_mysql_data_seek( $r_seller, 0 );
				while ($seller = ria_mysql_fetch_assoc($r_seller)) {
					// Seuls les représentants ayant un objectifs de définir seront prient en compte
					if( $seller['id'] != $seller_id ){
						continue;
					}

					$stats[$seller['id']] = array(
						'firstname' => $seller['adr_firstname'],
						'lastname'  => $seller['adr_lastname'],
						'email'     => $seller['adr_email'],
						'ref'       => $seller['ref'],
						'usr_id'    => $seller['id'],
						'seller_id' => $seller['seller_id'],
						'wst_id'    => $yuto_website['id'],
					);
				}

				if( $show_process ){
					print "refresh_goals_stats for seller = ".$seller_id.PHP_EOL;
				}

				// Mise à jour des statistiques pour la journée
				refresh_goals_stats( $date, $obj_data );
			}
		}

		// Mise à jour de la date de dernière exécution
		$now = new DateTime('now');
		cfg_overrides_set_value( 'goals_stats_last_execution', $now->format('Y-m-d H:i:s'), $yuto_website['id'] );
	}