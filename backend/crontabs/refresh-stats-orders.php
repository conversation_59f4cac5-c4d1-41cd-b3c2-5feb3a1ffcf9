<?php
	if (!isset($ar_params)) {
		die("L'exécution de ce script nécessite l'appel de execute-script.php." . PHP_EOL);
	}

	require_once('tenants.inc.php');
	require_once('websites.inc.php');
	require_once('stats.inc.php');

	foreach( $configs as $config ){
		// Efface les données précédentes
		update_stats_column( 'stat_orders_completed', '0' );
		update_stats_column( 'stat_carts', '0' );
		update_stats_column( 'stat_orders_canceled', '0' );
		update_stats_column( 'stat_orders_ca_completed', '0' );
		update_stats_column( 'stat_orders_ca_canceled', '0' );
		
		// s'assure d'une ligne par tenant, datetime et wst
		$orders = get_orders();
		while( $ord = ria_mysql_fetch_array($orders) ){
			stats_ensure_save( "'".$ord['date_en']."'", $ord['ord_wst_id'] );
		}
		
		// Nombre de commandes
		$orders = get_orders( ord_states_get_ord_valid( true ), 'count(*)', 'count' );
		while( $ord = ria_mysql_fetch_array($orders) ){
			update_stats_column( 'stat_orders_completed', $ord['count'], $ord['ord_wst_id'], $ord['date_en'] );
		}
		
		// Nombre de panier
		$orders = get_orders( ord_states_get_uncompleted(), 'count(*)', 'count' );
		while( $ord = ria_mysql_fetch_array($orders) ){
			update_stats_column( 'stat_carts', $ord['count'], $ord['ord_wst_id'], $ord['date_en'] );
		}
		
		// Nombres de commandes annulées
		$orders = get_orders( ord_states_get_canceled( true ), 'count(*)', 'count' );
		while( $ord = ria_mysql_fetch_array($orders) ){
			update_stats_column( 'stat_orders_canceled', $ord['count'], $ord['ord_wst_id'], $ord['date_en'] );
		}
		
		// Chiffre d'affaires
		$orders = get_orders( ord_states_get_ord_valid( true ), 'sum(ord_total_ht)', 'sum' );
		while( $ord = ria_mysql_fetch_array($orders) ){
			update_stats_column( 'stat_orders_ca_completed', $ord['sum'], $ord['ord_wst_id'], $ord['date_en'] );
		}
		
		// Chiffre d'affaires annulé
		$orders = get_orders( ord_states_get_canceled( true ), 'sum(ord_total_ht)', 'sum' );
		while( $ord = ria_mysql_fetch_array($orders) ){
			update_stats_column( 'stat_orders_ca_canceled', $ord['sum'], $ord['ord_wst_id'], $ord['date_en'] );
		}
		
	}
	
	/**	Récupère une liste de commande selon un contexte spécifié
	 *	\param $states Optionnel, tableau des états
	 *	\param $calc Optionnel, fonction d'agrégat et colonne utilisé (ex : "sum(ord_total_ht)")
	 *	\param $alias Optionnel, alias pour le résultat de l'agrégat
	 *	\return Un résultat de requête MySQL comprenant les colonnes suivantes :
	 *		- date_en : Date / Heure au format EN
	 *		- ord_wst_id : Identifiant du site
	 *		- $alias (optionnel) : Résultat de l'agrégat
	 */
	function get_orders( $states=false, $calc='', $alias='' ){
		global $config;
		
		$sql = '
			select
				date_format(ord_date,"%Y-%m-%d %H:00:00") as "date_en",
				ord_wst_id
				'.( trim($calc)!='' && trim($alias)!='' ? ', '.$calc.' as "'.$alias.'"' : '' ).'
			from
				ord_orders
			where
				ord_tnt_id='.$config['tnt_id'].'
				and ord_wst_id is not null
				'.( is_array($states) && sizeof($states) ? 'and ord_state_id in ('.implode(', ', $states).')' : '' ).'
			group by
				date_en, ord_wst_id
		';
		
		$r = ria_mysql_query($sql);
		
		if( ria_mysql_errno() ){
			error_log( __FILE__.':'.__LINE__.' '.mysql_error().' '.$sql );
		}
		
		return $r;
	}
	
	/**	Met à jour une colonne de la table stats_hourly pour un contexte spécifié
	 *	\param $col Nom de la colonne
	 *	\param $val Valeur (numérique) de la colonne
	 *	\param $wst Optionnel, identifiant du site
	 *	\param $date Optionnel, Date / Heure au format EN
	 *	\return True en cas de succès, False en cas d'échec
	 */
	function update_stats_column( $col, $val, $wst=0, $date='' ){
		global $config;
		
		$sql = '
			update stats_hourly
			set '.$col.' = '.$val.'
			where
				stat_tnt_id = '.$config['tnt_id'].'
				'.( $wst > 0 ? ' and stat_wst_id = '.$wst : '' ).'
				'.( trim($date)!='' ? 'and stat_datetime = "'.$date.'"' : '' ).'
		';
		
		$r = ria_mysql_query($sql);
		
		if( ria_mysql_errno() ){
			error_log( __FILE__.':'.__LINE__.' '.mysql_error().' '.$sql );
		}
		
		return $r;
	}