<?php

	/** \file export-netaffiliation.php
	 *
	 * 	Ce script est destiné à mettre à jour les fichiers d'export vers la plateforme d'affiliation NetAffiliation
	 *	des clients ayant activé cette option. Il est lancé chaque jour.
	 *
	 */

	if (!isset($ar_params)) {
		die("L'exécution de ce script nécessite l'appel de execute-script.php." . PHP_EOL);
	}

	require_once('delivery.inc.php');
	
	foreach( $configs as $config ){
		Sitemap::resetClassLists();
		// franco de port
		$config['tmp_franco'] = 0;
		$rfranco = dlv_services_get( 0, true, 0, array('srv_dealer_free_ht'=>'asc') );
		if( $rfranco && ria_mysql_num_rows($rfranco) ){
			$config['tmp_franco'] = ceil( ria_mysql_result($rfranco, 0, 'dealer-free-ht') * _TVA_RATE_DEFAULT );
		}
		
		// frais de port
		$config['tmp_port'] = 0;
		$rdlv = dlv_services_get( 0, true, 0, array('srv_price_ht'=>'asc') );
		if( $rdlv && ria_mysql_num_rows($rdlv) ){
			$config['tmp_port'] = number_format(ria_mysql_result( $rdlv, 0, 'price-ttc' ), 2, ',', ' ');
		}

		if( !isset($config['netaffiliation_is_active']) || !$config['netaffiliation_is_active'] ){
			continue;
		}
		
		// lien utilisé pour la création du fichier d'export du catalogue
		$dirname = $config['ctr_dir'].'/'.md5( $config['tnt_id'].$config['date-created'] ).'/';
		$file = $dirname.'netaffiliation.xml';
		$url_file = $config['site_url'].'/shopbots/'.md5( $config['tnt_id'].$config['date-created'] ).'/netaffiliation.xml';
		
		// si le dossier contenant les fichiers n'existe pas, on le créé avec les droits apache
		if( !file_exists($dirname) ){
			mkdir( $dirname, 0755 );
			chgrp( $dirname, 'apache' );
			chown( $dirname, 'apache' );
		}
		
		// création du fichier
		$f = fopen( $file, 'w' );
		
		// récupère le catalogue exporté sur Google Shopping
		$rp = prd_products_get_simple( 0, '', false, 0, false, false, true, false, array('childs'=>true) );
		
		if( $rp && ria_mysql_num_rows($rp) ){
			fwrite( $f, '<?xml version="1.0" encoding="UTF-8"?>'."\n" );
			fwrite( $f, '<products>'."\n" );
			while( $p = ria_mysql_fetch_array($rp) ){
				if( !$p['childonly'] && !$p['publish'] ){
					continue;
				}

				if( $p['stock']<=0 ){
					continue;
				}

				$desc = $desc = mb_ereg_replace('/\s\s+/', ' ', html_strip_tags($p['desc-long']) );
				$desc = html_entity_decode2( str_replace('&nbsp;', ' ', $desc) );
				$desc = substr( $desc, 0, 247 );
				$desc = substr( $desc, 0, strrpos($desc,' ') );
				$desc = $desc." ...";
				
				// récupère l'un de ses catégories
				$cat_id = 0; $cat_name = '';
				$rcly = prd_classify_get( false, $p['id'] );
				if( $rcly && ria_mysql_num_rows($rcly) ){
					$cly = ria_mysql_fetch_array( $rcly );
					$rcat = prd_categories_get( $cly['cat'] );
					if( $rcat && ria_mysql_num_rows($rcat) ){
						$cat = ria_mysql_fetch_array( $rcat );
						$cat_id = $cat['id'];
						$cat_name = $cat['title'];
					}
				}
				
				// récupère l'url du produit
				$url = prd_products_get_url( $p['id'], true );
				if( trim($url)=='' ){
					// si le produit ne dispose pas d'une url publique, on essaye de voir s'il s'agit d'un produit enfant
					$rparent = prd_parents_get( $p['id'] );
					if( $rparent && ria_mysql_num_rows($rparent) ){
						while( $parent = ria_mysql_fetch_array($rparent) ){
							$url = prd_products_get_url( $parent['id'], true );
							if( trim($url) ){
								continue;
							}
						}
					}
				}

				$url = rew_strip( $url );
				
				// Seules les urls valides seront inclues
				$infos = Sitemap::checkUrl( $config['site_url'].$url, false, '', true );
				if( !$infos ){
					continue;
				}

				// Récupère l'url directe du produit (sans redirection)
				$url = str_replace( array(str_replace('https', 'http', $config['site_url']), $config['site_url'], '?testadminerror404=1'), '', $infos['url'] );
				if( !trim($url)|| $url=='/' ){
					continue;
				}

				$info['qte_col'] = 0;
				$info['qte_pack'] = 1;
				if( isset($config['ctr_unity_qty_pack']) && !$config['ctr_unity_qty_pack'] ){
					if( !$p['sell_weight'] ){
						$rcol = prd_colisage_classify_get( 0, $p['id'], 0, array('qte'=>'asc') );
						if( $rcol && ria_mysql_num_rows($rcol) ){
							$info['qte_col'] = ria_mysql_result($rcol, 0, 'col_id');
							$info['qte_pack'] = ria_mysql_result($rcol, 0, 'qte');
						}
						
						if( $rcol && ria_mysql_num_rows($rcol)>1 ){
							error_log( 'Export NetAffiliation : il existe plusieurs conditionnements pour le produit '.$p['id'].'. Un seul va être exporté.' );
						}
					}
				}

				// récupère une promotion
				$promo = prc_promotions_get( $p['id'], 0, 0, 1, $info['qte_col'] );
				$is_promo = false;
				if( is_array($promo) && sizeof($promo) ){
					$is_promo = true;
					$p['price_ttc'] = $promo['price_ttc'];
				}
				
				if( $p['price_ttc']<=0 || trim($url)=='' ){
					continue;
				}
				
				$p['price_ttc'] = $p['price_ttc'] * $info['qte_pack'];

				// récupère une image
				$img_id = $p['img_id'];
				if( !$img_id ){
					$rimg = prd_images_get( $p['id'] );
					if( $rimg && ria_mysql_num_rows($rimg) ){
						$img_id = ria_mysql_result( $rimg, 0, 'id' );
					}
				}
				
				// si aucune notion de stock alors le stock est mis à 9999
				if( !prd_products_is_follow_stock($p['id']) ){
					$p['stock'] = 9999;
				}

				fwrite( $f, '	<product>'."\n" );
				fwrite( $f, '		<sku>'.$p['id'].'</sku>'."\n" );
				fwrite( $f, '		<title><![CDATA['.ucfirst2($p['title']).']]></title>'."\n" );
				if (isset($config['netaffiliation_for_kwanko']) && $config['netaffiliation_for_kwanko']) {
					fwrite( $f, '		<price>'.number_format($p['price_ttc'], 2, ',', '').'</price>'."\n" );
					fwrite( $f, '		<category_id>'.$cat_id.'</category_id>'."\n" );
					fwrite( $f, '		<category><![CDATA['.ucfirst2($cat_name).']]></category>'."\n" );
					fwrite( $f, '		<shipping>'.( $p['price_ttc']>=$config['tmp_franco'] ? '0' : $config['tmp_port'] ).'</shipping>'."\n" );
				}else{
					fwrite( $f, '		<price devise="euro">'.number_format($p['price_ttc'], 2, ',', '').'</price>'."\n" );
					fwrite( $f, '		<category id="'.$cat_id.'"><![CDATA['.ucfirst2($cat_name).']]></category>'."\n" );
					fwrite( $f, '		<shipping devise="euro">'.( $p['price_ttc']>=$config['tmp_franco'] ? '0' : $config['tmp_port'] ).'</shipping>'."\n" );
				}
				fwrite( $f, '		<link>'.$config['site_url'].$url.'</link>'."\n" );
				fwrite( $f, '		<model />'."\n" );
				fwrite( $f, '		<brand><![CDATA['.ucfirst2($p['brd_title']).']]></brand>'."\n" );
				fwrite( $f, '		<gtin>'.$p['barcode'].'</gtin>'."\n" );
				fwrite( $f, '		<description><![CDATA['.$desc.']]></description>'."\n" );
				fwrite( $f, '		<garanty>0</garanty>'."\n" );
				fwrite( $f, '		<stock>'.$p['stock'].'</stock>'."\n" );
				fwrite( $f, '		<availability>'.( $p['stock']>0 ? '' : 'En cours de réapprovisionnement' ).'</availability>'."\n" );
				fwrite( $f, '		<note>20</note>'."\n" );
				fwrite( $f, '		<promo>'.( $is_promo ? '1' : '0' ).'</promo>'."\n" );
				fwrite( $f, '		<new>'.( $p['new'] ? '1' : '0' ).'</new>'."\n" );
				if( $img_id ){
					$small = $config['img_sizes']['small'];
					$medium = $config['img_sizes']['medium'];
					$high = $config['img_sizes']['high'];
					
					$src = $config['img_url'].'/[width]x[height]/'.$img_id.'.[format]';
					
					$replace = array( '[width]', '[height]', '[format]' );
					$small_image = str_replace( $replace, array($small['width'], $small['height'], $small['format']), $src );
					$medium_image = str_replace( $replace, array($medium['width'], $medium['height'], $medium['format']), $src );
					$high_image = str_replace( $replace, array($high['width'], $high['height'], $high['format']), $src );
					
					fwrite( $f, '		<link_img_small>'.$small_image.'</link_img_small>'."\n" );
					fwrite( $f, '		<link_img_medium>'.$medium_image.'</link_img_medium>'."\n" );
					fwrite( $f, '		<link_img_high>'.$high_image.'</link_img_high>'."\n" );
				}
				fwrite( $f, '	</product>'."\n" );
			}
			fwrite( $f, '</products>'."\n" );
		}
		// fermeture du fichier
		fclose( $f );
	}