<?php 
/** \file devices-send-notifications.php
 *    \ingroup crontabs Devices
 *
 *    Ce script permet d'envoyer des notifications et forcer la sync pour les devices
 */
if (!isset($ar_params)) {
	die("L'exécution de ce script nécessite l'appel de execute-script.php." . PHP_EOL);
}

require_once( 'tasks.inc.php' );


$debug = isset($ar_params['debug']) && $ar_params['debug'] == '1' ? true : false;

$errors = array();

foreach( $configs as $config ){

	// récupère l'ensemble des devices 
	$rdev = dev_devices_get();
	if( $rdev ){
		while( $dev = ria_mysql_fetch_assoc($rdev) ){

			if( $dev['brand']!="Apple" ) continue; // limite pour le moment qu'au appareil Apple.
			if( trim($dev['notify_token']) == '' ) continue; // si pas de notify key c'est pas la peine d'aller plus loin

			RiaQueue::getInstance()->addJob(RiaQueue::WORKER_DEVICE_REQUEST_SYNC, array('id'=>$dev['id']));
		}
	}

}
