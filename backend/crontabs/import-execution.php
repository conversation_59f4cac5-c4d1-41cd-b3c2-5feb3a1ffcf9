<?php
    /** \file import-execution.php
     *	\ingroup crontabs imports
     * 	Ce script est destiné à réaliser les imports récurrents.
     */

	if (!isset($ar_params)) {
		die("L'exécution de ce script nécessite l'appel de execute-script.php." . PHP_EOL);
	}

	require_once('imports.inc.php');

	{ //initialisation des variables
		$imp_id = 0;
		$forced = $is_backup = false;

		// check pour la valeur de l'import
		if (isset($ar_params['imp_id'])) {
			if (!is_numeric($ar_params['imp_id']) || $ar_params['imp_id'] < 0) {
				print "Veuillez renseigner un identifiant d'import." . PHP_EOL;
				return;
			}

			$imp_id = $ar_params['imp_id'];
		}

		//check si backup
		if (isset($ar_params['is_backup']) && $ar_params['is_backup']) {
			$is_backup = true;
		}

		// permet de forcer l'exécution d'un import périodique
		if (isset($ar_params['forced']) && $ar_params['forced']) {
			$forced = true;
		}
	}

	foreach ($configs as $config) {
		$rWst = wst_websites_get();
		if( !$rWst || !ria_mysql_num_rows($rWst) ){
			continue;
		}

		while( $wst = ria_mysql_fetch_assoc($rWst) ){
			$rlng = wst_websites_languages_get( $wst['id'] );
			if( $rlng && ria_mysql_num_rows($rlng) ){

				while( $lng = ria_mysql_fetch_array($rlng) ){
					if( $lng['is_main'] )
						$config['i18n_lng'] = strtolower( $lng['lng_code'] );

					$config['i18n_lng_used'][] = strtolower( $lng['lng_code'] );
				}

			} else {

				$config['i18n_lng'] = 'fr';
				$config['i18n_lng_used'] = array( 'fr' );

			}

			$rImport = ipt_imports_get( $imp_id, $is_backup, false, '', 0, 0, 'pending', false, '', '', array(), null, $wst['id'], null );
			$imports_to_execute = array();
			if( $rImport ){
				while( $import = ria_mysql_fetch_assoc($rImport) ){
					$execute = false;
					if( trim($import['period']) != '' && !$forced){
						// vérification que les critère de périod son correcte
						switch( $import['period'] ){
							case 'day':
								$hour = date('G');

								if( $import['period_value'] != $hour ){
									continue 2;
								}
								break;
							case 'week':
								// Jour de la semaine
								$weekday = date( 'N' );

								if( $import['period_value'] != $weekday ){
									continue 2;
								}
								break;
							case 'month':
								// Jour du mois
								$day = date( 'j' );

								// Nombre de jours dans le mois
								$day_in_month = date( 't' );

								$import['period_value'] = $import['period_value'] > $day_in_month ? $day_in_month : $import['period_value'];

								if( $import['period_value'] != $day ){
									continue 2;
								}
								break;
						}

						$execute = true;
					}elseif ($forced) {
						$execute = true;
					}
					// ajout de l'import de la liste des imports a exécuter
					if ($execute) {
						$imports_to_execute[] = $import;
					}
				}
			}

			// Exécution de la liste des imports
			foreach ($imports_to_execute as $import) {
				ipt_imports_set_last_execution( $import['id'] );

				try{
					if( !ipt_imports_exec($import) ){
						error_log( '['.$config['tnt_id'].'] Erreur lors de l\'exectution de l\'import '.$import['id'] );
						if( $import['period'] ){
							ipt_imports_set_state( $import['id'], 'pending' );
						}
					}
				}catch( Exception $e ){
					error_log( '['.$config['tnt_id'].'] Erreur lors de l\'exectution de l\'import '.$import['id'].' => '.$e->getMessage() );
					if( $import['period'] ){
						ipt_imports_set_state( $import['id'], 'pending' );
					}
				}
			}
		}
	}