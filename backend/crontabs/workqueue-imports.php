<?php
	/** \file workqueue-imports.php
	 * 	Ce script est destiné à traiter les tâches contenues dans la file d'attente d'import.
	 *	Les tâches en question sont des demandes d'exécution d'import.
	 */

	if (!isset($ar_params)) {
		die("L'exécution de ce script nécessite l'appel de execute-script.php." . PHP_EOL);
	}

	require_once('imports.inc.php');

	$imp_id = 0;

	if( isset($ar_params['imp_id']) ){
		if( !is_numeric($ar_params['imp_id']) || $ar_params['imp_id'] <= 0 ){
			error_log($config['tnt_id'].' : Identifiant d\'import invalide');
			return;
		}else{
			$imp_id = $ar_params['imp_id'];
		}
	}
	
	$add_log = true;
	$starttime = time();
	
	foreach( $configs as $config ){
		$rTasks = tsk_imports_get(0, $imp_id);
		if( !$rTasks ){
			continue;
		}
		$tasks_ids = array();

		while( $task = ria_mysql_fetch_assoc($rTasks) ){
			$now = time()-$starttime;
			if ($now > 600) {
				error_log($config['tnt_id'].' : Fin de l\'exécution du script d\'import (workqueue-imports) après 10 minutes');
				$add_log = true;
				break(2);
			}
			if( trim($task['file_name']) != '' ){
				ipt_imports_upd( $task['imp_id'], false, '', null, null, null, null, 'pending', false, $task['file_name'] );
			}

			$is_sync = $task['is_sync'] == 1 ? true : false;
			$include_imgs = false;

			if( $task['include_imgs'] == 1 ){
				$include_imgs = true;
			}

			if( !ipt_imports_exec($task['imp_id'], false, $include_imgs) ){
				error_log($config['tnt_id']." : Une erreur a eu lieu à l'exécution de l'import ".$task['imp_id']);
			}

			tsk_imports_set_is_executed($task['id']);
		}

	}