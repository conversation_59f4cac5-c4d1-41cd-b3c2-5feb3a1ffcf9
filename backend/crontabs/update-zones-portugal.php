<?php
/** \file update-zones-portugal.php
 *	\ingroup system crontabs
 * 	Ce script est destiné à mettre à jour les zones (Régions, departement, Communes, Codes postaux) pour le Portugal.
 * 	Description du code postal portuguais
 * 	7 chiffres 
 * 	4 premier la zone
 *  3 dernier lieu dit ou adressage
 */
set_include_path(dirname(__FILE__) . '/../include/');

require_once('db.inc.php');
require_once('strings.inc.php');
require_once('sys.zones.inc.php');
require_once('geo-api.inc.php');

$file_text = api_geoname_get_zones('PT');
/* Identifiants de types de zones :
	*		- Région : 27
	*		- Province : 28
	*		- Code postal : 48
	*		- Ville : 29
	*/

if($file_text != ''){
	$cpt = 1;
	foreach ($file_text as $line) {

		$line = explode('	', $line);
		$ville       = $line[2];
		$zip_code    = $line[1];
		$region      = $line[5];
		$region_code = $line[6];
		$zone        = $line[3];
		$zone_code   = $line[4];

		//Recherche la région en base et l'a créée si elle n'existe pas
		$zone_get = api_geogouv_add_zone(_ZONE_REGION_PORTUGAL, $zone_code, $zone,0,'PT');
		if (!$zone_get) {
			$error = true;
			error_log(__FILE__.':'.__LINE__.'Erreur lors de la création / récupération de la région : "'.htmlspecialchars($zone_code.' - '.$zone).'"');
			continue;
		}

		$region_get = api_geogouv_add_zone(_ZONE_DEPARTEMENT_PORTUGAL, $region_code, $region, $zone_get);
		if (!$region_get) {
			$error = true;
			error_log(__FILE__.':'.__LINE__.'Erreur lors de la création / récupération du département : "'.htmlspecialchars($region_code.' - '.$region).'"');
			continue;
		}
		$ar_departement_ids[] = $region_get;
		$ar_region_ids[] = $zone_get;

		$ville = strtoupper2($ville);
		print 'Commune : '. $ville .PHP_EOL;
		$zipcode_zone = api_geogouv_add_zone(_ZONE_ZIPCODE_PROTUGAL, $zip_code, $zip_code, $region_get);
		if (!$zipcode_zone) {
			$error = true;
			error_log(__FILE__.':'.__LINE__.'Erreur lors de la création / récupération du code postal : "' . htmlspecialchars($zip_code) . '"');
			continue;
		}
		// Recherche la commune et l'a créée si elle n'existe pas
		$commune_zone = api_geogouv_add_zone(_ZONE_VILLE_PORTUGAL, $ville, $ville, $zipcode_zone);
		if (!$commune_zone) {
			$error = true;
			error_log(__FILE__.':'.__LINE__.'Erreur lors de la création / récupération de la commune : "'.htmlspecialchars($zip_code.' - '.$ville).'"');
			continue;
		}
		print 'Code postal : ' . $zip_code . PHP_EOL;
		$ar_codepostal_ids[] = $zipcode_zone;
		$ar_commune_ids[] = $commune_zone;
	}

	// Inclusion des zones spéciales (Armée)
	//array_push($ar_commune_ids, 361207, 361208);
	//array_push($ar_codepostal_ids, 361205, 361206);
	//
	// Deprecated des zones plus utilisées
	if (!isset($error)) {
		sys_zones_set_deprecated_out_ids(_ZONE_REGION_PORTUGAL, $ar_region_ids);
		sys_zones_set_deprecated_out_ids(_ZONE_DEPARTEMENT_PORTUGAL, $ar_departement_ids);
		sys_zones_set_deprecated_out_ids(_ZONE_VILLE_PORTUGAL, $ar_commune_ids);
		sys_zones_set_deprecated_out_ids(_ZONE_ZIPCODE_PROTUGAL, $ar_codepostal_ids);
		// Reconstruction de la hiérarchie
		sys_zones_hierarchy_rebuild(0,"PT");
	}

}