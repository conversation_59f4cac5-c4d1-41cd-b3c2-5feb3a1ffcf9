<?php
	/** \file import-documents-ftp.php
	 * 	Ce script permet l'import de documents et de type de documents à partir d'un service FTP.
	 *  Afin de faire fonctionner correctement cet import, il est nécessaire de renseigner les variables de configurations suivantes : 
	 *		- doc_ftp_url 				Obligatoire, URL du FTP
	 *		- doc_ftp_login 			Obligatoire, Identifiant de connexion
	 *		- doc_ftp_password 			Obligatoire, Mot de passe de connexion
	 *		- doc_ftp_json_directories 	Obligatoire, Tableau du matching entre les types de documents dans RiaShop et les dossiers dans le FTP
	 *		- doc_ftp_active 			Obligatoire, Active ou non le service (par défaut à false)
	 *		- doc_ftp_is_recursive		Facultatif, Permet de récupérer de façon récursive le contenu des dossiers présents dans le FTP
	 */
	
	// Script bloqué s'il n'est pas exécuter via execute-script.php (en mode 1 = pour tous les sites)
	if( !isset($ar_params) ){
		error_log("L'exécution du script ".__FILE__." nécessite l'appel de execute-script.php.".PHP_EOL);
		exit;
	}
	
	// Fichiers nécessaire au bon fonctionnement
	require_once('define.inc.php');
	require_once('documents.inc.php');
	require_once('imports.inc.php');
	
	foreach( $configs as $config ){		
		// Vérifie sur les variables de configuration ont bien été activées et paramétrées correctement
		if( !isset($config['doc_ftp_url'], $config['doc_ftp_json_directories'], $config['doc_ftp_login'], $config['doc_ftp_password'], $config['doc_ftp_active']) ){
			continue;
		}elseif( !trim($config['doc_ftp_url']) || !trim($config['doc_ftp_login']) || !trim($config['doc_ftp_password']) || !$config['doc_ftp_active'] ){
			continue;
		}
		
		// Information de connexion au ftp
		$url = $config['doc_ftp_url'];
		$login = $config['doc_ftp_login'];
		$password = $config['doc_ftp_password'];

		/*  Tableau des matchings type de document RiaShop / Nom du document dans le FTP
			Ce tableau est de la forme suivante : doc_id_riashop => doc_name_ftp
			Le format étant en JSON, il faut le décoder pour obtenir un tableau PHP
		*/
		$ar_directories = json_decode($config['doc_ftp_json_directories'], true);
		
		// Défini si l'on importe de façon récursive ou non les dossiers et documents présents dans le FTP
		$is_recursive = isset($config['doc_ftp_is_recursive']) && $config['doc_ftp_is_recursive'] ? true : false;

		{ // Connexion au FTP
			if( !($ftp = ftp_connect($url)) ){
				error_log('[tenant '.$config['tnt_id'].'] - Erreur import des documents - '.date('Y-m-d H:i:s').' - Impossible de se connecter au serveur FTP '.$url);
				exit;
			}
		
			// Identification au FTP
			if( !ftp_login($ftp, $login, $password) ){
				error_log('[tenant '.$config['tnt_id'].'] - Erreur import des documents - '.date('Y-m-d H:i:s').' - Impossible de se connecter avec les identifiants fournis : '.$login.' '.$password);
				exit;
			}
		
			// Active le mode passif (nécéssaire sinon la récupération des éléments du FTP échoue)
			$mode = ftp_pasv($ftp, true);
		}

		{ // Import des documents en fonction des types renseignés, sinon importe à la racine
			if( is_array($ar_directories) && count($ar_directories) ){
				foreach( $ar_directories as $doc_type_id => $doc_name_ftp ){
					// Importe les documents et types de documents du dossier défini dans les variables de configuration
					if( !ipt_import_documents_directory($ftp, $doc_name_ftp, $doc_type_id, $is_recursive) ){
						error_log('[tenant '.$config['tnt_id'].'] - Erreur import des documents - '.date('Y-m-d H:i:s').' - Une erreur est survenue lors de l\'import des documents du dossier "Photothèque".');
						continue;
					}
				}
			}else{
				#TODO mettre en place l'import des documents et types de documents à la racine.
			}
		}
		
		// Fermeture de l'accession au FTP
		ftp_close($ftp);
	}
