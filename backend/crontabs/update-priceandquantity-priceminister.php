<?php
	/** \file update-priceandquantity-priceminister.php
	 *	\ingroup crontabs priceminister
	*	Ce cron permet de mettre à jour le tarif et la quantité des produits exportés vers la place de marché Price Minister.
	*	Dan<PERSON> le cas de Price Minister, le poids des produits est également actualisé par ce script.
	*	Seuls les produits dont l'une de ces deux informations n'est pas à jour seront actualisés.
	*/

	if (!isset($ar_params)) {
		die("L'exécution de ce script nécessite l'appel de execute-script.php." . PHP_EOL);
	}

	require_once('comparators.inc.php');
	require_once('comparators/ctr.priceminister.inc.php');
	require_once('tsk.comparators.inc.php');
	require_once('prices.inc.php');

	$mode_test 	= isset($ar_params['mode_test']) && $ar_params['mode_test'] == 'true' ? true : false;

	foreach( $configs as $config ){
		// Vérifie que la place de marché est bien activée pour ce client, dans le cas contraire, passe au client suivant.			
		if( !ctr_comparators_actived(CTR_PRICEMINISTER) ){
			continue;
		}

		// Si la facturation au poids de la commande est activée alors la mise à jour du poids des produits est faite
		$actived = ctr_params_get_array( CTR_PRICEMINISTER, 'port_weight_invoice' );
		if( isset($actived['port_weight_invoice']) && $actived['port_weight_invoice']=='Oui' ){
			ctr_priceminister_update_weight();
		}
		
		$update = ctr_priceminister_update_pricequantity( $mode_test );
		if( $update === false ){
			// Module RiaShoppping plus suivi, plus d'envoi de message
		}elseif( $update === true ){
			continue;
		}

		$prds = $update['prds'];
		$xml  = ctr_priceminister_get_xml( $update['xml'] );
		
		// si on est pas en mode test, on envoi les messages à PriceMinister
		if( !$mode_test ){
			if( trim($xml) != '' ){
				if( !($itemid = ctr_priceminister_xml_send($xml)) ){
					// Module RiaShoppping plus suivi, plus d'envoi de message
				} else {
					$ar_exec = array();
					foreach( $prds as $p ){
						if( ($tsk = tsk_comparators_add( CTR_PRICEMINISTER, $p, 'update-priceqte' )) ){
							$ar_exec[] = $tsk;
						}
					}
					
					// mise à jour de la date d'exécution des tâches
					tsk_comparators_set_completed( $ar_exec );
					
					// mise à jour de l'identifiant d'import pour toutes les tâches consernées par l'envoi
					tsk_comparators_set_import_id( $ar_exec, $itemid );
				}
			}
		} else {
			print $xml."\n";
		}
	}