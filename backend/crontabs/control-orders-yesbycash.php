<?php
	/** \file control-orders-yesbycash.php
	 *
	 * 	Ce script est destiné à controler toutes les commandes YesByCash en attente de confirmation de commande. Selon le statut de la commande chez YesByCash, une action sera réalisée dans RiaShop :
	 *			- payée : validée dans RiaShop, passage au statut 4
	 *			- annulée / expirée : annulée dans RiaShop, passage au statut 9
	 */

	set_include_path(dirname(__FILE__) . '/../include/');
	require_once('orders.inc.php');
	require_once('YesByCash.inc.php');
	
	unset($config);
	$tnt_id = isset( $argv[1] ) && is_numeric( $argv[1] ) && $argv[1]>0 ? $argv[1] : 0;

	// Charge l'ensemble des configurations clients
	$configs = cfg_variables_get_all_tenants( $tnt_id );
	if( !is_array($configs) || !sizeof($configs) ){
		return false;
	}

	// Traitement
	foreach( $configs as $config ){
		// Récupère les commandes en attente de validation du paiement
		$rorder = ord_orders_get_with_adresses( 0, 0, array(_STATE_WAIT_PAY, _STATE_WAIT_VALIDATION), '', false, false, false, false, null, false, false, false, false, _PAY_YESBYCASH );

		if( $rorder && mysql_num_rows($rorder) ){
			$yesbycash = new YesByCash();

			while( $order = mysql_fetch_assoc($rorder) ){
				$yesbycash->controlStatutOrder( $order['id'], true );
				sleep(1);
			}
		}
	}
