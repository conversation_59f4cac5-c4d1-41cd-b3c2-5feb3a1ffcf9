<?php

/** \file update-priceandquantity-amazon.php
 *
 * 	Ce script est chargé de mettre à jour les tarifs et les stocks disponibles des produits exportés sur la place de marché Amazon.
 *	Seuls les produits dont l'une de ces deux informations n'est pas à jour seront actualisés.
 *	Il est lancé automatiquement deux fois par heure aux minutes 29 et 59.
 *
 */
if (!isset($ar_params)) {
    die("L'exécution de ce script nécessite l'appel de execute-script.php." . PHP_EOL);
}

define( 'DATE_FORMAT', 'Y-m-d\TH:i:s\Z' );


require_once( 'comparators.inc.php' );
require_once( 'comparators/ctr.amazon.inc.php' );


// Traitement

$TestAmazon = isset($ar_params['test']) && $ar_params['test'] == 'test';

// Traitement
foreach( $configs as $config ){

	$ar_ctr_amazon = ctr_amazon_get_marketplace();

	foreach( $ar_ctr_amazon as $ctr_amazon ){
		$config['tmp_ctr_amazon'] = $ctr_amazon;

		// Vérifie que la place de marché est bien activée pour ce client, dans le cas contraire, passe au client suivant.			
		if( !ctr_comparators_actived( $config['tmp_ctr_amazon'] ) ){
			continue;
		}
		
		if( $TestAmazon ){
			echo 'Traitement de ' . $config['tnt_id'] . "\n";
		}
		
		// Mise à jour des tarifs et des stocks, pour les produits exportés
		if( !ctr_amazon_update_price_and_quantity() ){
			throw new Exception('Erreur ctr_amazon_update_price_and_quantity !');
		}
	}
}
