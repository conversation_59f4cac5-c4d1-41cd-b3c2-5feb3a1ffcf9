<?php
	/** \file fields.php
	 *  Ce script est destiné à supprimer les valeurs des champs avancés liés aux objets inexistants
	 */

  // Liste des classes prisent en charge
  // $ar_cls_clear = [CLS_PRODUCT, CLS_CATEGORY, CLS_BRAND, CLS_ORDER, CLS_ORD_PRODUCT, CLS_USER, CLS_STORE, CLS_MESSAGE, CLS_CMS];
  $ar_cls_clear = [CLS_ORD_PRODUCT]; // Pour les tests

  // Exclusion de champs liés à des classes qui ne semble pas être la bonne
  $ar_fld_exclude = [ 5030, 5003, 5004, 2437, 4079, 616 ];

  // Liste des champs avancés qui semble lié à la mauvaise classe.
  $ar_false_fields = [];

  // Chargement des tuples de la table fld_objects_values + classe à laquelle à l'appartient
  $r_obj_values = ria_mysql_query('
    select pv_obj_id_0, pv_obj_id_1, pv_obj_id_2, fld_cls_id, pv_fld_id
    from fld_object_values
      left join fld_fields on (fld_tnt_id in (0, '.$config['tnt_id'].') and fld_id = pv_fld_id)
    where pv_tnt_id = '.$config['tnt_id'].'
      and fld_cls_id in ('.implode( ', ', $ar_cls_clear ).')
      and fld_id not in ('.implode( ', ', $ar_fld_exclude ).')
  ');

  $del = 0;
  while( $obj_values = ria_mysql_fetch_assoc($r_obj_values) ){
    $from = $where = '';
    print '.';

    switch( $obj_values['fld_cls_id'] ){
      case CLS_PRODUCT:
        if(
          (is_numeric($obj_values['pv_obj_id_1']) && $obj_values['pv_obj_id_1'] > 0)
          || (is_numeric($obj_values['pv_obj_id_2']) && $obj_values['pv_obj_id_2'] > 0)
        ){
          $ar_false_fields[ $obj_values['pv_fld_id'] ] = $obj_values['pv_fld_id'];
          continue;
        }

        $from = 'prd_products';
        $where = 'prd_tnt_id = '.$config['tnt_id'].' and prd_id = '.$obj_values['pv_obj_id_0'];
        break;
      case CLS_CATEGORY:
        if(
          (is_numeric($obj_values['pv_obj_id_1']) && $obj_values['pv_obj_id_1'] > 0)
          || (is_numeric($obj_values['pv_obj_id_2']) && $obj_values['pv_obj_id_2'] > 0)
        ){
          $ar_false_fields[ $obj_values['pv_fld_id'] ] = $obj_values['pv_fld_id'];
          continue;
        }

        $from = 'prd_categories';
        $where = 'cat_tnt_id = '.$config['tnt_id'].' and cat_id = '.$obj_values['pv_obj_id_0'];
        break;
      case CLS_BRAND:
        if(
          (is_numeric($obj_values['pv_obj_id_1']) && $obj_values['pv_obj_id_1'] > 0)
          || (is_numeric($obj_values['pv_obj_id_2']) && $obj_values['pv_obj_id_2'] > 0)
        ){
          $ar_false_fields[ $obj_values['pv_fld_id'] ] = $obj_values['pv_fld_id'];
          continue;
        }

        $from = 'prd_brands';
        $where = 'brd_tnt_id = '.$config['tnt_id'].' and brd_id = '.$obj_values['pv_obj_id_0'];
        break;
      case CLS_ORDER:
        if(
          (is_numeric($obj_values['pv_obj_id_1']) && $obj_values['pv_obj_id_1'] > 0)
          || (is_numeric($obj_values['pv_obj_id_2']) && $obj_values['pv_obj_id_2'] > 0)
        ){
          $ar_false_fields[ $obj_values['pv_fld_id'] ] = $obj_values['pv_fld_id'];
          continue;
        }

        $from = 'ord_orders';
        $where = 'ord_tnt_id = '.$config['tnt_id'].' and ord_id = '.$obj_values['pv_obj_id_0'];
        break;
      case CLS_ORD_PRODUCT:
        if(
          !(is_numeric($obj_values['pv_obj_id_0']) && $obj_values['pv_obj_id_0'] > 0)
          || !(is_numeric($obj_values['pv_obj_id_1']) && $obj_values['pv_obj_id_1'] > 0)
        ){
          $ar_false_fields[ $obj_values['pv_fld_id'] ] = $obj_values['pv_fld_id'];
          continue;
        }

        $from = 'ord_products';
        $where = 'prd_tnt_id = '.$config['tnt_id'].' and prd_ord_id = '.$obj_values['pv_obj_id_0'].' and prd_id = '.$obj_values['pv_obj_id_1'].' and prd_line_id = '.$obj_values['pv_obj_id_2'];
        break;
      case CLS_USER:
        if(
          (is_numeric($obj_values['pv_obj_id_1']) && $obj_values['pv_obj_id_1'] > 0)
          || (is_numeric($obj_values['pv_obj_id_2']) && $obj_values['pv_obj_id_2'] > 0)
        ){
          $ar_false_fields[ $obj_values['pv_fld_id'] ] = $obj_values['pv_fld_id'];
          continue;
        }

        $from = 'gu_users';
        $where = 'usr_tnt_id = '.$config['tnt_id'].' and usr_id = '.$obj_values['pv_obj_id_0'];
        break;
      case CLS_STORE:
        if(
          (is_numeric($obj_values['pv_obj_id_1']) && $obj_values['pv_obj_id_1'] > 0)
          || (is_numeric($obj_values['pv_obj_id_2']) && $obj_values['pv_obj_id_2'] > 0)
        ){
          $ar_false_fields[ $obj_values['pv_fld_id'] ] = $obj_values['pv_fld_id'];
          continue;
        }

        $from = 'dlv_stores';
        $where = 'str_tnt_id = '.$config['tnt_id'].' and str_id = '.$obj_values['pv_obj_id_0'];
        break;
      case CLS_MESSAGE:
        if(
          (is_numeric($obj_values['pv_obj_id_1']) && $obj_values['pv_obj_id_1'] > 0)
          || (is_numeric($obj_values['pv_obj_id_2']) && $obj_values['pv_obj_id_2'] > 0)
        ){
          $ar_false_fields[ $obj_values['pv_fld_id'] ] = $obj_values['pv_fld_id'];
          continue;
        }

        $from = 'gu_messages';
        $where = 'cnt_tnt_id = '.$config['tnt_id'].' and cnt_id = '.$obj_values['pv_obj_id_0'];
        break;
      case CLS_CMS:
        if(
          (is_numeric($obj_values['pv_obj_id_1']) && $obj_values['pv_obj_id_1'] > 0)
          || (is_numeric($obj_values['pv_obj_id_2']) && $obj_values['pv_obj_id_2'] > 0)
        ){
          $ar_false_fields[ $obj_values['pv_fld_id'] ] = $obj_values['pv_fld_id'];
          continue;
        }

        $from = 'cms_categories';
        $where = 'cat_tnt_id = '.$config['tnt_id'].' and cat_id = '.$obj_values['pv_obj_id_0'];
        break;
    }

    if( trim($from) != '' && trim($where) != '' ){
      $exists = ria_mysql_query('
        select 1 from '.$from.' where '.$where.'
      ');

      if( $exists === false ){
        ria_mysql_error();return;
      }

      if( $exists && ria_mysql_num_rows($exists) == 0 ){
        print '-';
        // Suppression du tuble
        // print 'select 1 from '.$from.' where '.$where.''.PHP_EOL;
        // print '
        //   delete from  fld_object_values
        //   where pv_tnt_id = '.$config['tnt_id'].'
        //     and pv_obj_id_0 = '.$obj_values['pv_obj_id_0'].'
        //     and pv_obj_id_1 = '.$obj_values['pv_obj_id_1'].'
        //     and pv_obj_id_2 = '.$obj_values['pv_obj_id_2'].'
        //     and pv_fld_id = '.$obj_values['pv_fld_id'].'
        // '.PHP_EOL;return;

        ria_mysql_query('
          delete from  fld_object_values
          where pv_tnt_id = '.$config['tnt_id'].'
            and pv_obj_id_0 = '.$obj_values['pv_obj_id_0'].'
            and pv_obj_id_1 = '.$obj_values['pv_obj_id_1'].'
            and pv_obj_id_2 = '.$obj_values['pv_obj_id_2'].'
            and pv_fld_id = '.$obj_values['pv_fld_id'].'
        ');
        $del++;
      }
    }
  }

  var_dump($del.'/'.ria_mysql_num_rows($r_obj_values));
  print_r($ar_false_fields);