<?php
	/** \file prices.php
	 *  Ce script est destiné à supprimer les tarifs ayant été supprimées
	 */

  $date_del_prices = new DateTime();
  $date_del_prices->modify( '-'.$cfg_days['prices']['days'].' days' );

  { // Suppression des tarifs supprimés depuis plus de $cfg_days['prices']['days']
    $sql_del = '
      delete from prc_prices
      where prc_tnt_id = '.$config['tnt_id'].'
        and prc_is_deleted = 1
        and (
          date(prc_date_deleted) <= "'.$date_del_prices->format('Y-m-d').'"
          or date(prc_date_modified) <= "'.$date_del_prices->format('Y-m-d').'"
        )
    ';

    $res_del = ria_mysql_query( $sql_del );
    if( !$res_del ){
      throw new Exception( 'Erreur lors de la suppression des tarifs supprimés. => '.ria_mysql_error().' | '.$sql_del );
    }

    if( $debug ){
      print ' Suppression des tarifs supprimés : '.ria_mysql_affected_rows().PHP_EOL;
    }
  }

  { // Suppression des conditions tarifs liées à tarifs qui n'existe plus
    $sql_del = '
      delete from prc_price_conditions
      where ppc_tnt_id = '.$config['tnt_id'].'
        and not exists (
          select 1 from prc_prices
          where prc_tnt_id = '.$config['tnt_id'].'
            and prc_id = ppc_prc_id
        )
    ';

    $res_del = ria_mysql_query( $sql_del );
    if( !$res_del ){
      throw new Exception( 'Erreur lors de la suppression des conditions tarifs liées à tarifs qui n\'existe plus. => '.ria_mysql_error().' | '.$sql_del );
    }

    if( $debug ){
      print ' Suppression des conditions tarifs liées à tarifs qui n\'existe plus : '.ria_mysql_affected_rows().PHP_EOL;
    }
  }
