<?php
	/** \file orders.php
	 *  Ce script est destiné à supprimer commandes, et leurs données liées, qui sont devenues obsolètes
   *  ATTENTION : Pour le moment les commandes ne sont pas supprimées dans le temps.
   *  Cette suppression implique une discution entre les différents services (yuto / sync / commerce)
	 */

  $date_del_orders = new DateTime();
  $date_del_orders->modify( '-'.$cfg_days['orders']['days'].' days' );

  { // Suppression de toutes les commandes qui sont plus vieille de plus de $cfg_days['orders']['days']
    $sql_del = '
      delete from ord_orders
      where ord_tnt_id = '.$config['tnt_id'].'
        and date(ord_date) <= "'.$date_del_orders->format('Y-m-d').'"
    ';

    $res_del = ria_mysql_query( $sql_del );
    if( !$res_del ){
      throw new Exception( 'Erreur lors de la suppression des commandes. => '.ria_mysql_error().' | '.$sql_del );
    }

    if( $debug ){
      print ' Suppression des commandes : '.ria_mysql_affected_rows().PHP_EOL;
    }
  }

  { // Suppression des lignes de commandes rattachées à une entête supprimée
    $sql_del = '
      delete from ord_products
      where prd_tnt_id = '.$config['tnt_id'].'
        and not exists (
              select 1
              from ord_orders
              where ord_tnt_id = '.$config['tnt_id'].'
                and ord_id = prd_ord_id
        )
    ';

    $res_del = ria_mysql_query( $sql_del );
    if( !$res_del ){
      throw new Exception( 'Erreur lors de la suppression des lignes de commandes rattachées à une entête supprimée. => '.ria_mysql_error().' | '.$sql_del );
    }

    if( $debug ){
      print ' Suppression des lignes de commandes rattachées à une entête supprimée : '.ria_mysql_affected_rows().PHP_EOL;
    }
  }

  { // Suppression des créneaux de livraison rattachées à une entête supprimée
    $sql_del = '
      delete from ord_orders_plage
      where oop_tnt_id = '.$config['tnt_id'].'
        and not exists (
              select 1
              from ord_orders
              where ord_tnt_id = '.$config['tnt_id'].'
                and ord_id = oop_ord_id
        )
    ';

    $res_del = ria_mysql_query( $sql_del );
    if( !$res_del ){
      throw new Exception( 'Erreur lors de la suppression des créneaux de livraison rattachées à une entête supprimée. => '.ria_mysql_error().' | '.$sql_del );
    }

    if( $debug ){
      print ' Suppression des créneaux de livraison rattachées à une entête supprimée : '.ria_mysql_affected_rows().PHP_EOL;
    }
  }

  { // Suppression des status rattachées à une entête supprimée
    $sql_del = '
      delete from ord_products_states
      where ops_tnt_id = '.$config['tnt_id'].'
        and not exists (
              select 1
              from ord_orders
              where ord_tnt_id = '.$config['tnt_id'].'
                and ord_id = ops_ord_id
        )
    ';

    $res_del = ria_mysql_query( $sql_del );
    if( !$res_del ){
      throw new Exception( 'Erreur lors de la suppression des status rattachées à une entête supprimée. => '.ria_mysql_error().' | '.$sql_del );
    }

    if( $debug ){
      print ' Suppression des status rattachées à une entête supprimée : '.ria_mysql_affected_rows().PHP_EOL;
    }
  }

  { // Suppression des codes promotion rattachées à une entête supprimée
    $sql_del = '
      delete from ord_orders_promotions
      where oop_tnt_id = '.$config['tnt_id'].'
        and not exists (
              select 1
              from ord_orders
              where ord_tnt_id = '.$config['tnt_id'].'
                and ord_id = oop_ord_id
        )
    ';

    $res_del = ria_mysql_query( $sql_del );
    if( !$res_del ){
      throw new Exception( 'Erreur lors de la suppression des codes promotion rattachées à une entête supprimée. => '.ria_mysql_error().' | '.$sql_del );
    }

    if( $debug ){
      print ' Suppression des codes promotion rattachées à une entête supprimée : '.ria_mysql_affected_rows().PHP_EOL;
    }
  }

  { // Suppression des signatures rattachées à une entête supprimée
    $sql_del = '
      delete from ord_orders_signature
      where sig_tnt_id = '.$config['tnt_id'].'
        and not exists (
              select 1
              from ord_orders
              where ord_tnt_id = '.$config['tnt_id'].'
                and ord_id = sig_ord_id
        )
    ';

    $res_del = ria_mysql_query( $sql_del );
    if( !$res_del ){
      throw new Exception( 'Erreur lors de la suppression des signatures rattachées à une entête supprimée. => '.ria_mysql_error().' | '.$sql_del );
    }

    if( $debug ){
      print ' Suppression des signatures rattachées à une entête supprimée : '.ria_mysql_affected_rows().PHP_EOL;
    }
  }
