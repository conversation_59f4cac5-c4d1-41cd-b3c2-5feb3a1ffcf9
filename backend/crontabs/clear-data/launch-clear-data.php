<?php
	/** \file launch-clear-data.php
	 * 	Ce script est destiné à centraliser toutes les suppressions de données qui sont devenu invalide ou bien trop veille
	 *
	 * 	Voici comment utilisé ce script :
	 * 		- Suppression de toutes les données inactifs ou obsolètes
	 * 				-> php execute-script.php --tnt_id [TNT_ID] --script clear-data/launch-clear-data
	 *
	 * 		- Suppression du partie ciblée des données (ex. les caches inactifs, les vieux paniers, etc...)
	 * 				-> php execute-script.php --tnt_id [TNT_ID] --script clear-data/launch-clear-data --other="data=[DONNEES_CIBLES]"
	 *
	 * 				Remplacer [DONNEES_CIBLES] par une de ces valeurs :
	 *					- all : toutes les données invalides ou obsolètes
	 *					- caches : suppression des caches inactifs
	 *					- users : suppression des comptes supprimés
	 *					- trading : suppression des règles de négociations obsolètes
	 *					- wishlists : suppression des listes de favoris personnalisées supprimées
	 *					- carts : suppression des vieux paniers
	 *					- orders : suppression des commandes (pas encore actif), et de leurs données liées, devenues obsolètes
	 *					- prices : suppression des tarifs supprimées
	 *					- imports : suppression des données sur les imports obsolètes
	 *					- fields : suppression des champs avancés liés à des objets inexistants (TODO : passer par une table archive avant de vraiment supp les lignes donc pas encore actif)
	 *
	 * 	ATTENTION : Pensez à maintenir le document Wiki des délais de conservations : https://riastudio.atlassian.net/wiki/x/AQDlp
	 */

	if (!isset($ar_params)) {
		print "L'exécution de ce script nécessite l'appel de execute-script.php.".PHP_EOL;
		exit;
	}

	$data = 'all';
	$ar_data_accept = ['all', 'caches', 'users', 'trading', 'wishlists', 'carts', 'orders', 'prices', 'imports', 'fields'];

	if( isset($ar_params['data']) ){
		$data = $ar_params['data'];
	}

	$debug = isset($ar_params['debug']) && $ar_params['debug'];

	if( !in_array($data, $ar_data_accept) ){
		print 'La demande de suppression n\'est pas valide. Seules ses entrées sont acceptées : '.PHP_EOL;
		print '	- all : toutes les données invalides ou obsolètes'.PHP_EOL;
		print '	- caches : suppression des caches inactifs'.PHP_EOL;
		print '	- users : suppression des comptes supprimés'.PHP_EOL;
		print '	- trading : suppression des règles de négociations obsolètes'.PHP_EOL;
		print '	- wishlists : suppression des listes de favoris personnalisées supprimées'.PHP_EOL;
		print '	- carts : suppression des vieux paniers'.PHP_EOL;
		print '	- orders : suppression des commandes, et de leurs données liées, devenues obsolètes'.PHP_EOL;
		print '	- prices : suppression des tarifs supprimées'.PHP_EOL;
		print '	- imports : suppression des données sur les imports obsolètes'.PHP_EOL;
		// print '	- fields : suppression des champs avancés liés à des objets inexistants'.PHP_EOL;
		return;
	}

	foreach( $configs as $config ){
		try{
			if( $debug ){
				print '--- Suppression des données obsolètes pour le tenant : '.tnt_tenants_get_name( $config['tnt_id'] ).' ---'.PHP_EOL;
			}

			// Chargement des délais de conservation
			// Ces délais peuvent être personnalisés pour chaque locataire
			$cfg_days = cfg_variables_get_clear_database_days( true );

			ria_mysql_query('SET FOREIGN_KEY_CHECKS = 0;');

			if( in_array($data, ['all', 'caches']) ){ // Ménage dans les clés de cache
				include( dirname(__FILE__).'/data/caches.php' );
			}

			if( in_array($data, ['all', 'users']) ){ // Ménage dans les comptes supprimés
				include( dirname(__FILE__).'/data/users.php' );
			}

			if( in_array($data, ['all', 'wishlists']) ){ // Ménage dans les listes de favoris personnalisées supprimées
				include( dirname(__FILE__).'/data/wishlists.php' );
			}

			if( in_array($data, ['all', 'trading']) ){ // Ménage dans les règles de négociations obsolètes
				include( dirname(__FILE__).'/data/trading-rules.php' );
			}

			if( in_array($data, ['all', 'carts']) ){ // Ménage dans les vieux paniers
				include( dirname(__FILE__).'/data/carts.php' );
			}

			if( in_array($data, ['all', 'orders']) ){ // Ménage dans les commandes devenues obsolètes
				include( dirname(__FILE__).'/data/orders.php' );
			}

			if( in_array($data, ['all', 'prices']) ){ // Ménage dans les tarifs supprimés
				include( dirname(__FILE__).'/data/prices.php' );
			}

			// if( in_array($data, ['all', 'imports']) ){ // Ménage dans les imports
			// 	include( dirname(__FILE__).'/data/imports.php' );
			// }

			// if( in_array($data, ['all', 'fields']) ){ // Ménage dans les valeurs des champs avancés liés à des objets indexistants
			// 	include( dirname(__FILE__).'/data/fields.php' );
			// }

			ria_mysql_query('SET FOREIGN_KEY_CHECKS = 1;');
		}catch( Exception $e ){
			print $e->getMessage();
		}
	}

	/* function riashop_clear_database_get_database_weight(){
		$w_bdd = ria_mysql_fetch_assoc( ria_mysql_query('
			select sum( round( ((DATA_LENGTH + INDEX_LENGTH) / 1024 / 1024), 2) ) as "weight"
			from information_schema.TABLES
			where TABLE_SCHEMA = "riashop"
		'));

		return $w_bdd['weight'];
	} */