<?php

	/**	\file rebuild-restrictions-cache.php
	 *
	 *	Ce script est actuellement lancé chaque jour pour reconstruire le cache des droits d'accès au catalogue.
	 *	Il est possible de le lancer pour un locataire spécifié en indiquant son identifiant en premier argument.
	 *	Le second argument permet de spécifier si le bench doit être affiché directement ("true") ou dirigé vers le fichier "/var/log/php/bench_cron.log" (toutes les autres valeurs)
	 */

	if (!isset($ar_params)) {
		die("L'exécution de ce script nécessite l'appel de execute-script.php." . PHP_EOL);
	}

	require_once('prd/restrictions.inc.php');

	foreach( $configs as $config ){
		$rcontext = prd_restrictions_get_distinct_usr();
		while( $context = ria_mysql_fetch_assoc($rcontext) ){
			prd_restrictions_build_cache( $context['fld_id'], $context['value'], $context['wst_id'], true );
		}

		prd_restrictions_purge_cache( true );
	}