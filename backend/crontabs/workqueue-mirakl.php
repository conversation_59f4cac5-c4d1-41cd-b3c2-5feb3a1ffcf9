<?php

/** \file workqueue-mirakl.php
 *	\ingroup crontabs rdc
 * 	Ce script est destiné à traiter les tâches contenues dans la file d'attente Mirakl - Rue du Commerce.
 *	Les tâches en question sont des demandes de création de produit, de mise à jour ou de suppression.
 *	Il est lancé automatiquement deux fois par heure aux minutes 29 et 59.
 *	Le script peut reçevoir des arguments :
 *		- int tnt_id : id du tenant (Optionnel)
 *		- string "test"	: activation du mode de verbose/debug (optionnel)
 *		- bool ignore_stock : ignore la quantité en stock (optionnel)
 *		- bool ignore_mandatory : ignore les erreurs de champs obligatoires pour l'ajout de produits
 *	
 *	ex : php workqueue-mirakl.php 16 test 1 1
 *	Exécutera le code pour le tenant 16, en mode de verbose/debug, en ignorant le stock et les champs obligatoires
 *
 *
 */

set_include_path(dirname(__FILE__) . '/../include/');

require_once( 'define.inc.php' );
require_once( 'comparators.inc.php' );
require_once( 'comparators/ctr.mirakl.inc.php' );

unset($config);

if( isset($argv[1]) && (!is_numeric($argv[1]) || $argv[1]<0) ){
	die('Veuillez renseigner un identifiant de tenant valide (numéric supérieur à zéro).'.PHP_EOL);
}
exit;
$tnt 				= isset($argv[1]) && is_numeric($argv[1]) && $argv[1]>0 ? $argv[1] : 0;
$test 				= isset($argv[2]) && $argv[2] == "test" ? true : false;
$ignore_stock 		= isset($argv[3]) && $argv[3] == "1" ? true : false;
$ignore_mandatory 	= isset($argv[4]) && $argv[4] == "1" ? true : false;

unset($config);

$configs = cfg_variables_get_all_tenants( $tnt );
if( !is_array($configs) || !sizeof($configs) ){
	return false;
}

// Traitement
foreach( $configs as $config ){

	// Vérifie que la place de marché est bien activée pour ce client, dans le cas contraire, passe au client suivant.			
	if( !ctr_comparators_actived(CTR_RUEDUCOMMERCE_MIRAKL) ){
		continue;
	}

	$import_count = 0;
	$tasked = array();
	$result = tsk_comparators_get(CTR_RUEDUCOMMERCE_MIRAKL, true);
	//	Pour toutes les tâches, on les réorganise dans un seul tableau, par import_id
	while ($row = ria_mysql_fetch_assoc($result) ) {
		$prd = ria_mysql_fetch_assoc( prd_products_get($row['prd_id']) );
		if( !array_key_exists($row['import_id'], $tasked) ){

			$tasked[ $row['import_id'] ] = array( 
				'object'	=> 	($row['action'] == "add-prd") ? "product" : "offer", 
				'products'	=> 	array(
					$prd['ref'] => array(
						"prd_id"	=>	$prd['id'],
						"prd_ref"	=>	$prd['ref'],
						"prd_name"	=>	$prd['name'], 
						"tsk_id"	=>	$row['id'], 
						"in_error"	=>	""
					)
				)
			);
		}
		else{
			$tasked[ $row["import_id"] ]['products'][ $prd['ref'] ] = array(
				"prd_id"	=>	$prd['id'],
				"prd_ref"	=>	$prd['ref'],
				"prd_name"	=>	$prd['name'], 
				"tsk_id"	=>	$row['id'],
				"in_error"	=>	""
			);
		}
	}

	
	$RdC = new RdC_Mirakl( true, $test );
	
	//	Pour chaque import, on récupère les résultats des rapports d'erreur, puis on injecte les erreurs aux produits/offres correspondant
	foreach( $tasked as $import_id => $single_task ){
		$import_count++;
		//	En fonction du type d'import, on récupère le statut d'import
		$import_status = $RdC->getImportStatus( $import_id, $single_task['object'] );

		//	Les rapports ne sont consultables que si le champ <status> est "COMPLETE", la documentation indique que le statut COMPLETE peut ne pas être atteint, car le rapport n'est pas obligatoire.
		//	Dans ce cas, le statut reste "SENT", il est probable que si le statut reste "SENT", il n'y ait pas d'erreur dans l'import
		if($import_status['status'] == "COMPLETE" || $import_status['status'] == "SENT"){
			$error_report_status = $RdC->createErrorReportStatus($import_status, $single_task["object"]);
			$array_error = $RdC->createErrorReport( $import_id, $single_task['object'], $error_report_status );
			
			//	On parcourt les produits de l'import et on cherche les erreurs correpondant à prd_ref
			foreach($tasked[$import_id]['products'] as $prd_ref => $prds){
				if( array_key_exists($prd_ref, $array_error) ){
					//	Si la clé a été trouvée, le produit existe et est retrouvé avec $prd_ref
					if($array_error[$prd_ref]['in_error'] != false){
						$tasked[$import_id]['products'][$prd_ref]['error'] = $array_error[$prd_ref]['error'];
					}
					$tasked[$import_id]['products'][$prd_ref]['in_error'] = $array_error[$prd_ref]['in_error'];
				}
			}
		}else{
			//	Le statut de l'import n'est pas COMPLETE, on stoppe alors les traitements suivants sur cet import_id et on laisse la tâche dans le même état pour que l'import soit revérifié dans l'itération suivante
			if( $RdC->sandbox ){
				print "Le rapport de l'import ".$import_id." n'est pas encore prêt\n";
			}
			unset($tasked[$import_id]);
		}
	}

	$errors_found = array();

	//	On rassemble les erreurs semblables, et on retrie les produits/offres en fonction de leurs erreurs en vue d'un affichage trié par erreur
	//	On procède au passage à la validation des tâches accomplies en fonction des codes de retour
	foreach( $tasked as $import_id => $obj ){
		//	Parcours de tous les produits de l'import
		foreach( $obj['products'] as $product ){
			if( $product['in_error'] == true ){
				foreach($product['error'] as $error){
					if( $obj["object"] == "offer" &&  $error['code'] == "NOCODE" && $error['desc'] == "The product does not exist" ){
						//	Si c'est une offer et que l'erreur = produit inexistant, on le créée
						tsk_comparators_add( CTR_RUEDUCOMMERCE_MIRAKL, $product['prd_id'], "add-prd" );
						tsk_comparators_del( $product['tsk_id'] );
					}else{
						$unique_error = md5($error['code'].'|'.$error['desc']);
						if( !array_key_exists($unique_error, $errors_found) ){
							//	Si l'erreur n'a pas déjà été rencontrée, on la rajoute au tableau et on y inscrit le produit trouvé
							$errors_found[$unique_error] = $RdC->generateErrorCollection($error, $product, $obj["object"]);
						}else{
							//	Si l'erreur a déjà été trouvée et ajoutée précédemment, on inscrit l'offre ou le produit actuel sur cette dernière
							$errors_found[$unique_error]['products'][] = $product;
						}

						//	Après ajout de l'erreur pour affichage, on supprime la tâche pour que l'import ne soit pas relu
						tsk_comparators_del($product['tsk_id']);

						//	On peut ensuite recréer la tâche pour relancer l'action
						// Todo : créer une fonction de gestion des codes d'erreur
						//tsk_comparators_add(CTR_RUEDUCOMMERCE_MIRAKL, $product['prd_id'], "add");
					}
				}
			}

			//	Attention, si l'erreur n'est pas settée et que le produit est une offre, le produit est en succès
			//	Les offres ne possèdent pas de rapport justifiant du succès
			elseif( $product['in_error'] == false || ( $product['in_error'] == "" && $obj['object'] == "offer" ) ){
				if($obj["object"] == "offer"){
					//	Si l'action d'offre est réussie, on la supprime
					tsk_comparators_del($product['tsk_id']);
				}
				elseif($obj["object"] == "product"){
					//	Si l'action de produit est réussie, on la supprime
					tsk_comparators_del($product['tsk_id']);
					tsk_comparators_add(CTR_RUEDUCOMMERCE_MIRAKL, $product['prd_id'], "add");
				}
				elseif($obj["object"] == "update-priceqte"){
					//	L'update s'est correctement déroulée (update-priceqte lancées par crontabs/update-priceandquantity-mirakl.php)
					tsk_comparators_del($product['tsk_id']);
				}
			}
		}	
	}
	

	//	Création du mail
	if(!empty($tasked)){
		$body = "Les imports suivants ont été traités en totalité ou en partie :\n";
		foreach(array_keys($tasked) as $import_id){
			$body .= "\t".$import_id."\n";
		}
		if( !empty($errors_found) ){
			foreach($errors_found as $md5 => $error_bundle){
				$body .= "Le code d'erreur : ".$error_bundle['error_code']." - ".$error_bundle['error_desc']." est déclenché pour les produits ci-dessous :\n";
				foreach($error_bundle["products"] as $key => $product){
					$body .= "\t".$error_bundle["object"]." - prd_ref : ".$product["prd_ref"]." - ".$product['prd_name']."\n";
				}
				$body .= "\n\n";
			}
			$headers = "Content-Type: text/plain; charset=utf-8" . "\r\n";
			mail("<EMAIL>;<EMAIL>", "workqueue-mirakl rapport des erreurs d'import", $body, $headers);
			if( $RdC->sandbox ){
				print "\nUn mail recensant les erreurs des rapports d'import a été envoyé\n\n";
			}
		}
		elseif($import_count > 0){
			if( $RdC->sandbox ){
				print "\nAucune erreur trouvée parmi les ".$import_count." rapports d'erreur d'import traités\n";
			}
		}
		
	}
	else{
		if( $RdC->sandbox ){
			print "\nAucun rapport d'import n'a été traité (sur les ".$import_count." imports trouvés)\n";
		}
	}


	unset($tasked);
	/*		
	 *	Traitement des produits/offres non envoyés à mirakl (date_completed == null)
	 *	Leurs rapports seront lus à la prochaine itération
	 */
	//	Certains produits peuvent avoir été ajoutés durant la lecture des rapports précédemment réalisée
	//	L'API offres n'a pas l'air d'accepter le XML, on enverra du JSON

	$tasked = array();
	$result = tsk_comparators_get(CTR_RUEDUCOMMERCE_MIRAKL, false);
	while ($row = ria_mysql_fetch_assoc($result)) {
	    $tasked[] = $row;
	}
	$xml_add_prd_content = "";
	$xml_add_content = "";
	$json_offers_content = array();

	//	On stockera tous les id des produits ou offres ajoutés pour créer les tâches
	$ids_offers = array();
	$ids_add_prd = array();

	$i = 0;
	$generationErrors = array();
	foreach($tasked as $tsk){
		$i++;
		//	Le produit en cours est récupéré via la fonction RdC_Mirakl::getAllProducts(), qui renvoie le produit avec les informations nécéssaires à l'export
		$prds = $RdC->getAllProducts(true, $tsk["prd_id"]);
		$single_prd = $prds[$tsk["prd_id"]];
		switch( $tsk['action'] ){
			case "add":
				$retour = $RdC->generatedOfferXML($single_prd, $ignore_stock);
				$err_code = $RdC->processXMLGeneratorErrors($retour);
				if($err_code != ""){
					$generationErrors[$err_code][] = array("product" => $single_prd, "task" => $tsk ); 
					//	Si erreur, on jette la tâche
					tsk_comparators_del($tsk['id']);
				}
				else{
					$xml_add_content .= $retour;
					$ids_offers[] = $tsk["id"];
				}
				break;
			case "update-price":
			case "update-qte":
			case "update-priceqte":
			case "update":
				$json_offers_content[] = $RdC->generatedOfferJSONArray($single_prd, $ignore_stock, "update");
				$ids_offers[] = $tsk["id"];
				break;
			case "delete":
				$json_offers_content[] = $RdC->generatedOfferJSONArray($single_prd, $ignore_stock, "delete");
				$ids_offers[] = $tsk["id"];
				break;
			case "add-prd":
				$retour = $RdC->generatedProductXML($single_prd, $ignore_stock, $ignore_mandatory);
				$err_code = $RdC->processXMLGeneratorErrors($retour);
				if($err_code != ""){
					$generationErrors[$err_code][] = array("product" => $single_prd, "task" => $tsk ); 
					//	Si erreur, on jette la tâche (un mail notifiera de l'échec)
					tsk_comparators_del($tsk['id']);
				}
				else{
					$xml_add_prd_content .= $retour;
					$ids_add_prd[] = $tsk["id"];
				}
				break;
			default:
				//	autres actions ?
				break;
		}
	}

	/*	On envoie 1 xml pour tous les produits, 1 XML pour l'ajout des offres et 1 JSON pour l'update/delete des offres	*/
	//	On place l'array d'offres dans un autre tableau pour obtenir le bon format de JSON
	if( !empty($json_offers_content) ){
		$json = array();
		$json['offers'] = $json_offers_content;
		$new_import_id_offers = $RdC->sendOfferJSON(json_encode($json));
	}
	//	On concatène les composantes du xml et les envoie à Mirakl via sendOfferXML(); ou sendProductXML();
	if( trim($xml_add_prd_content) != '' ){
		$new_import_id_add_prd = $RdC->sendProductXML($xml_add_prd_content);
	}
	if( trim($xml_add_content) != '' ){
		$new_import_id_offers = $RdC->sendOfferXML($xml_add_content);
	}

	//	Les imports mirekl ont été envoyés par l'API, il reste à répercuter l'état des nouveaux imports dans la table tsk_comparators
	foreach( $ids_offers as $id_offer ){
		tsk_comparators_set_completed($id_offer);
		tsk_comparators_set_import_id($id_offer, $new_import_id_offers);
	}
	foreach( $ids_add_prd as $id_add_prd ){
		tsk_comparators_set_completed($id_add_prd);
		tsk_comparators_set_import_id($id_add_prd, $new_import_id_add_prd);
	}

	if(!empty($generationErrors)){
		$headers = "Content-Type: text/plain; charset=utf-8" . "\r\n";
		
		$body = "Les imports suivants ont eu des erreurs avant envoi à mirakl (aucun envoi effectué) :\n";
		foreach($generationErrors as $err_code => $prdtsk){
			$body .= "\n\tErreur : ".$err_code."\n";
			foreach($prdtsk as $singleprdtsk){
				$prd = $singleprdtsk['product']['product'];
				$tsk = $singleprdtsk['task'];
				$body .="\t\tprd_ref : ".$prd['ref']." - ".$prd["name"]."\n";
			}
		}

		mail("<EMAIL>;<EMAIL>", "workqueue-mirakl erreurs generation", $body, $headers);
		if($RdC->sandbox){
			print "\nUn mail recensant les erreurs de génération d'import a été envoyé\n";
		}
	}
	if($i > 0){
		if($RdC->sandbox){
			print "\nAucune erreur de génération n'a été détectée avant l'envoi à mirakl\n";
		}
	}
	else{
		if($RdC->sandbox){
			print "\nRien n'a été envoyé à Mirakl\n";
		}
	}
}

