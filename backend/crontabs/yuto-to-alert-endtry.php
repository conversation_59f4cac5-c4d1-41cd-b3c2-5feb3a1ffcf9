<?php
/** \file yuto-to-alert-endtry.php
 * 	Ce script est chargé d'envoyer un mail après la fin de la période d'essai gratuite à Yuto si aucun abonnement a été prit
 */

if (!isset($ar_params)) {
	error_log("L'exécution de ce script nécessite l'appel de execute-script.php." . PHP_EOL);
	exit;
}

require_once('devices.inc.php');
require_once('users.inc.php');

// Récupération des différentes configurations mails pour Yuto vente en ligne
$cfg_emails = cfg_emails_yuto_get();

foreach( $configs as $config ){
	global $ria_db_connect;

	// Récupère le dernier abonnement en date
	$sub = dev_subscribtions_yuto_get(true);
	if( !is_array($sub) || !count($sub) ){
		continue;
	}

	// Récupère la date de fin de l'abonnement
	$date_end = new Datetime(dateparse($sub['date_end']));

	// Si l'abonnement est toujours, on ne vas pas plus loin
	if( $date_end->getTimestamp() >= strtotime(date('Y-m-d')) ){
		continue;
	}

	// Si l'abonnement est terminé, on désactive automatiquement toutes les tablettes de branchés
	// Cela vaut aussi pour les périodes d'essai étant terminé
	$r_device = dev_devices_get(0, 0, '', -1, '=', false, false, true);
	if( $r_device ){
		while( $device = ria_mysql_fetch_assoc($r_device) ){
			dev_devices_deactivate($device['id']);
		}
	}

	// Un mail est envoyé le lendemain de la fin de période d'essai afin de relancer les administrateurs sur l'activation de l'abonnement
	if( is_numeric($sub['testing']) && $sub['testing'] > 0 ){
		// Le mail est envoyé à tous les administrateurs
		$r_user = gu_users_get(0, '', '', PRF_ADMIN);
		if( !$r_user || !ria_mysql_num_rows($r_user) ){
			continue;
		}

		// Le mail n'est envoyé qu'au lendemain
		$date_end->modify('+ 1 day');

		if( $date_end->getTimestamp() == strtotime(date('Y-m-d')) ){
			// Récupère le type d'abonnement Essentiel ou Business
			$package = ucfirst(RegisterGCP::getPackage($config['tnt_id']));

			// Tous les administrateurs seront en destinataire du mail
			while( $user = ria_mysql_fetch_assoc($r_user) ){
				// Retire les super-admins de l'envoi
				if( !is_numeric($user['tenant']) || $user['tenant'] <= 0 ){
					continue;
				}

				// Désinscription à la relance
				if( in_array($user['email'], ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>']) ){
					continue;
				}

				// Création du mail d'alerte
				$email = new Email();
				$email->setSubject('Il est toujours temps d\'activer Yuto '.$package);
				$email->setFrom($cfg_emails['vel-yuto-notif']['from']);
				$email->setTo($user['email']);

				if( trim($cfg_emails['vel-yuto-notif']['bcc']) != '' ){
					$email->addBcc($cfg_emails['vel-yuto-notif']['bcc']);
				}

				// Préparation du contenu du mail
				$html = file_get_contents('/var/www/start.yuto.fr/htdocs/dist/template-email/alert-endtry.html');
				$html = str_replace('[user_surname]', strtolower($user['adr_firstname']) == 'nc'? '' : $user['adr_firstname'], $html);
				$html = str_replace('[date de fin]', $sub['date_end'], $html);
				$html = str_replace('[package]', $package, $html);
				$html = str_replace('[utm_campaign]', 'vel_'.strtolower($package).'_alert_endtry', $html);

				if( $package == 'Business' ){
					$html = str_replace('[url_no_active]', 'https://docs.google.com/forms/d/e/1FAIpQLScEEZQCGWHQBi7hYil_oFeV-ySexnnCfoGrGEqHXKvdtFzoEQ/viewform?usp=sf_link', $html);
				}else{
					$html = str_replace('[url_no_active]', 'https://docs.google.com/forms/d/e/1FAIpQLScMc0pn3ro0IA1qBXiRLYXsICvBX3hR5Sey1x33TShGnYnuJA/viewform?usp=sf_link', $html);
				}

				$email->addHtml($html);
				$email->send();
			}
		}
	}
}
