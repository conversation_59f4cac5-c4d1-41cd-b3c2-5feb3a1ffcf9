<?php
    /** \file reset_seller_when_no_devis.php
     *	
     *  Ce script permet de retirer le lien entre un compte et un représentant si le dernier devis remonte à plus de jours que la valeur renseigné
     *  dans la variable de configuration "yuto_reset_seller"
     */

    if (!isset($ar_params)) {
        die("L'exécution de ce script nécessite l'appel de execute-script.php." . PHP_EOL);
    }

    require_once('orders.inc.php');
    require_once('users.inc.php');

    foreach ($configs as $config) {
        if (!isset($config['yuto_reset_seller']) || !is_numeric($config['yuto_reset_seller']) || $config['yuto_reset_seller'] <= 0) {
            continue;
        }
        
        $date = date('Y-m-d', strtotime(date("Y-m-d").' - '.$config['yuto_reset_seller'].' days'));

        $r_usr = gu_users_get();

        while ($usr = ria_mysql_fetch_assoc($r_usr)) {
            if ($usr['prf_id'] == PRF_SELLER || $usr['seller_id'] == null) { // Si représentant ou aucun représentant de rattaché
                continue;
            }

            $r_order = ord_orders_get_simple(
                array(),
                array('start' => $date, 'state_id' => ord_states_get_ord_valid(true)),
                array('usr_id' => $usr['id'])
            );

            if ($r_order && ria_mysql_num_rows($r_order) == 0) {
                //Retire le lien seulement si le compte à été créé il y a plus de 'yuto_reset_seller' jour
                if( cmp_date($date, $usr['usr_date_created']) ){
                    gu_users_set_seller_id($usr['id'], null);
                }
            }
        }
    }