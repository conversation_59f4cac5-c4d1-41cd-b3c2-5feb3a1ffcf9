<?php
	/** \file send-cart-notify.php
	 *
	 * 	Ce script est chargé de relancer les clients pour le panier non finalisé
	 *
	 */

	if (!isset($ar_params)) {
		die("L'exécution de ce script nécessite l'appel de execute-script.php." . PHP_EOL);
	}

	require_once('email.inc.php');
	require_once('users.inc.php');
	require_once('orders.inc.php');

	foreach( $configs as $config ){
		// Vérifie que la fonctionnalitée est bien activée pour ce client.
		// Dans le cas contraire, passe au client suivant.
		if( !isset($config['cart_notify']) || !$config['cart_notify'] ){
			continue;
		}

		$date_hour_end = strtotime('-'.$config['cart_notify_delay'].' HOUR');
		$date_start = date ( 'Y-m-d', strtotime('-10 HOUR', $date_hour_end));
		$date_end = date ( 'Y-m-d', $date_hour_end);

		$orders = ord_orders_get_with_adresses( 0, 0, array(_STATE_BASKET, _STATE_BASKET_SAVE), '', $date_start, $date_end, false, false, null, false, false, false, false, 0, $config['wst_id'] );

		$emails_to_notify = array();
		if($orders && ria_mysql_num_rows($orders)) {
			while( $order = ria_mysql_fetch_assoc($orders) ){
				if(!$order['user'] || in_array($order['user'], $emails_to_notify)){
					continue;
				}

				// Contrôle que le compte client est bien inscript à l'alert mail (pour certains tenant)
				if( in_array($config['tnt_id'], array(14)) ){
					if( !gu_ord_alerts_exists($order['user'], _STATE_BASKET) ){
						continue;
					}
				}

				// controle les horaires
				if( strtotime($order['date_en']) > $date_hour_end ){
					continue;
				}

				// controle que le cron n'a pas 10 heure de retard
				if( strtotime($order['date_en']) < strtotime('-10 HOUR', $date_hour_end)  ){
					continue;
				}

				// controle si la commande n'a pas été déjà notifié
				$notifed = fld_object_values_get($order['id'],  _FLD_ORD_CART_NOTIFY);
				if( in_array($notifed, array('Oui', 'oui', '1')) ){
					continue;
				}

				// Récupère la date de dernière commande (qui doit être antérieure au panier)
				$last_order = gu_users_get_last_order( $order['user'] );
				if( is_array($last_order) && count($last_order) && array_key_exists('date', $last_order) ){
					if( strtotime($last_order['date']) > strtotime($order['date_en']) ){
						continue;
					}
				}

				if( ord_carts_notify($order['id']) ){
					$emails_to_notify[] = $order['user'];

					// mise à jour du champ pour la commande
					fld_object_values_set($order['id'],  _FLD_ORD_CART_NOTIFY, 'Oui');
				}

			}
		}
	}