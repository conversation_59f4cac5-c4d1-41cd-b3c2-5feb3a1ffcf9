<?php
	/**	\file import-media-youtube.php
	 *	Ce script est lancé à intervalle régulier et permet la récupération des videos/playslist youtube
	 */

	if (!isset($ar_params)) {
		die("L'exécution de ce script nécessite l'appel de execute-script.php." . PHP_EOL);
	}

	require_once('medias.inc.php');
	require_once('Google/Client.php');
	require_once('Google/Service/YouTube.php');
	require_once('Google/Auth/AssertionCredentials.php');

	foreach( $configs as $config ){
		$host_params = doc_hosts_params_get(1, 0);
		if( !is_array($host_params) || !sizeof($host_params) ){
			continue;
		}

		if( !isset($host_params['key_file_path'], $host_params['client_id'], $host_params['service_account_name']) ){
			continue;
		}

		$gclient = new Google_Client();

		$key = file_get_contents( $host_params['key_file_path'] );
		$gclient->setClientId($host_params['client_id']);
		$gclient->setAssertionCredentials(new Google_Auth_AssertionCredentials( $host_params['service_account_name'], array('https://www.googleapis.com/auth/youtube'), $key));

		$youtube = new Google_Service_YouTube($gclient);

		// parcours toutes les chaines 
		$rchannel =	doc_channels_get( HST_YOUTUBE );
		if( !$rchannel || !ria_mysql_num_rows($rchannel) ){
			continue;
		}
		while( $channel = ria_mysql_fetch_assoc($rchannel) ){

			$youtube_playlists = array();

			$pageToken = false;
			$pageTokenCpt= 0;

			$youtube_video_channel = array();

			while( $pageToken!==null ){
				if( $pageTokenCpt++ > 1000 ) break; // pour les boucles infinies

				// parcours les playlists de la chaines
				$playlists = $youtube->playlists->listPlaylists('snippet',array('channelId'=>$channel['import_id'], 'maxResults' => 50, 'pageToken' => $pageToken));
				$pageToken = $playlists->getNextPageToken();

				foreach( $playlists->getItems() as $playlist ){
					$yplaylist = $playlist->getSnippet();

					$playlist_id = false;
					$import_id = $playlist->getID();
					$name = $yplaylist->getTitle();
					$desc = $yplaylist->getDescription();
					$url = '';

					// teste si la playlist est déjà créé 
					$rplaylists = doc_playlists_get( HST_YOUTUBE, 0, 0, null, null, $playlist->getID() ); 
					if( $rplaylists && ria_mysql_num_rows($rplaylists) ){
						$tmp_playlist = ria_mysql_fetch_assoc($rplaylists);

						$playlist_id = $tmp_playlist['id']; 

						// mise à jour de la playlist
						if( !doc_playlists_update( $playlist_id, $name, $desc, $import_id, $url, true ) ){
							die('Erreur de mise à jour de la playlist'.$name);
						}
					}else{
						// création de la playlist
						if( !($playlist_id = doc_playlists_add( HST_YOUTUBE, $name, $desc, $import_id, $url, true )) ){
							die('Erreur de création de la playlist'.$name);
						}
					}

					$youtube_playlists[] = $playlist_id;

					// attache la playlist 
					doc_channels_playlists_add( $channel['id'], $playlist_id );

					// mise à jour des videos de la playlist 
					$youtube_video = array();

					$pageVideoToken = false;
					$pageVideoTokenCpt= 0;

					while( $pageVideoToken!==null ){
						if( $pageVideoTokenCpt++ > 1000 ) break; // pour les boucles infinies

						$videos = $youtube->playlistItems->listPlaylistItems('snippet', array('playlistId'=>$import_id, 'maxResults' => 50, 'pageToken' => $pageVideoToken) );
						$pageVideoToken = $videos->getNextPageToken();

						foreach( $videos->getItems() as $video ){

							$snippet = $video->getSnippet();

							$current_video_id = import_video($channel['id'], $snippet->getResourceId()->getVideoId(), $snippet->getTitle(), $snippet->getDescription(), $snippet->getPublishedAt(), $snippet->getThumbnails() );

							$youtube_video[] = $current_video_id;
							$youtube_video_channel[] = $current_video_id;

							// attache la video à la playlist 
							doc_playlists_medias_add( $playlist_id, $current_video_id );
							doc_playlists_medias_set_pos( $playlist_id, $current_video_id, $snippet->getPosition() );

						}
					}

					// controle les videos de la playlists dans riashop et retire celle qui ne devrais plus y être
					$rvideos = doc_medias_get(HST_YOUTUBE, $playlist_id, $channel['id']); 
					if( $rvideos ){
						while( $video = ria_mysql_fetch_assoc($rvideos) ){
							if( !in_array($video['id'], $youtube_video) ){
								doc_playlists_medias_del($playlist_id,$video['id']);
							}
						}
					}
				}
			}
			// pour chaque playlist qui ne sont plus dans youtube_playlist on les supprimes 
			$rplaylists = doc_playlists_get(HST_YOUTUBE, $channel['id']);
			if( $rplaylists && ria_mysql_num_rows($rplaylists) ){
				while( $playlist = ria_mysql_fetch_assoc($rplaylists) ){
					if( !in_array($playlist['id'], $youtube_playlists) ){
						doc_playlists_del($playlist['id'], true);
					}
				}
			}



			$pageToken = false;
			$pageTokenCpt= 0;
			while( $pageToken!==null ){
				if( $pageTokenCpt++ > 1000 ) break; // pour les boucles infinies

				// parcours les videos de la chaines
				$videos = $youtube->search->listSearch('snippet',array('channelId' => $channel['import_id'], 'maxResults' => 50, 'pageToken' => $pageToken));
				$pageToken = $videos->getNextPageToken();
				foreach( $videos->getItems() as $video ){
					$snippet = $video->getSnippet();
					$current_video_id = import_video($channel['id'], $video->getId()->getVideoId(), $snippet->getTitle(), $snippet->getDescription(), $snippet->getPublishedAt(), $snippet->getThumbnails() );
					$youtube_video_channel[] = $current_video_id;
				}
			}
			// controle les videos de la chaine dans riashop et retire celle qui ne devrais plus y être
			$rvideos = doc_medias_get(HST_YOUTUBE, null, $channel['id']); 
			if( $rvideos ){
				while( $video = ria_mysql_fetch_assoc($rvideos) ){
					if( !in_array($video['id'], $youtube_video_channel) ){
						doc_medias_del($video['id']);
					}
				}
			}

		}
	}
	
	function import_video($channel_id, $videoid, $name, $desc, $publish, $thumbnails){

		$current_video_id = false;
		$url = 'https://www.youtube.com/watch?v='.$videoid;

		$rvideo = doc_medias_get( HST_YOUTUBE, 0, 0, 0, null, $videoid );
		if( $rvideo && ria_mysql_num_rows($rvideo) ){
			$current_video = ria_mysql_fetch_assoc($rvideo);
			$current_video_id = $current_video['id'];

			// mise à jour des informations 
			if( !doc_medias_update($current_video['id'], $name, $desc, $videoid, $url, null, date('d/m/Y H:i:s',strtotime($publish))) ){
				die('Erreur lors de la mise à jour de la video '.$videoid);
			}
		}else{
			if( !($current_video_id = doc_medias_add(HST_YOUTUBE, $name, $desc, $videoid, $url, 0, date('d/m/Y H:i:s',strtotime($publish)))) ){
				die('Erreur lors de l\'ajout de la video '.$videoid);
			}
		}

		// affecte la video à la chaine
		doc_channels_medias_add($channel_id, $current_video_id);

		// importation de l'image 
		$size = false; 
		if( $thumbnails ){
			if( $thumbnails->getMaxres() ){
				$size = $thumbnails->getMaxres();
			}
			elseif( $thumbnails->getHigh() ){
				$size = $thumbnails->getHigh();
			}
			elseif( $thumbnails->getDefault() ){
				$size = $thumbnails->getDefault();
			}
			$tmp_file = tempnam(sys_get_temp_dir(), 'youtube'); 

			if( !copy($size->getUrl(), $tmp_file ) ){
				die('Erreur de copie de l\'image en local');
			}

			doc_medias_image_add( $current_video_id, $tmp_file); 
		}

		return $current_video_id;
	}