<?php
	/** \file send-birthday-promo.php
	 *
	 * 	Ce script est executé chaque jours afin de faire bénéficier d'une offre promotionel certain utilisateur dont c'est l'anniversaire
	 *	Tient compte aussi des points attribués directement le jour anniversaire d'un internaute
	 *
	 *  Variables de configuration requises :
	 * 		- dob_promo_activated
	 * 		- dob_promo_config
	 *
	 *  Json Exemple : dob_promo_config = {"days":"1234","type":"1","config":{"code":{"value":"200","type":"0","on":"min-line"},"srv":["-1","274","275","286","129","128"]}}
	 */

	if (!isset($ar_params)) {
		die("L'exécution de ce script nécessite l'appel de execute-script.php." . PHP_EOL);
	}

	require_once('promotions.inc.php');

	foreach( $configs as $config ){
		$profile = 0;

		// Hack Chadog - Seule les professionnels sont concernés par ce code anniversaire
		if( $config['tnt_id'] == 171 ){
			$profile = PRF_CUST_PRO;
		}

		$r_users = gu_users_get( 0, '', '', $profile, '', 0, '', false, false, false, false, '', false, 0, '', 0, false, false, null, 0, false, false, null, false, 0, 0, true );

		if (!$r_users || !ria_mysql_num_rows($r_users)) {
			continue;
		}

		// Attribution des poins directs (renseigné dans l'onglet "Action" du paramétrage du programme de fidélité)
		if( isset($config['rwd_reward_actived']) && $config['rwd_reward_actived'] ){
			var_dump( ria_mysql_num_rows($r_users) );
			continue;

				while ($usr = ria_mysql_fetch_assoc($r_users)) {
				if (!rwd_users_is_enabled($usr['id'])) {
					continue;
				}

				rwd_actions_apply( 'RWA_BIRTHDAY', CLS_USER, array($usr['id']), $params=array('forced_user_id' => $usr['id']) );
			}
		}

		if (!isset($config['dob_promo_activated'], $config['dob_promo_config']) || !$config['dob_promo_activated'] || trim($config['dob_promo_config']) == '') {
			continue;
		}

		// Contrôle du paramètre
		$birthday_configs = json_decode( $config['dob_promo_config'], true );
		if( !is_array($birthday_configs) || !sizeof($birthday_configs) ){
			continue;
		}

		if(!isset($birthday_configs["config"]) || !is_array($birthday_configs["config"])){
			continue;
		}

		if ($birthday_configs['type'] == 'port') {
			$birthday_configs['type'] = _PMT_TYPE_CODE;
		}

		$cod_id = pmt_codes_add('Anniversaire du '.date("d-m-Y"), $birthday_configs["type"], pmt_codes_generated(), 'Code correspondant à la promotion d\'anniversaire');
		if(!$cod_id){
			error_log(__FILE__.':'.__LINE__.' Impossible de créer le code promotion pour l\'anniversaire.');
			continue;
		}

		// Création du lien entre le code promotion anniversaire et le site internet pour lequel le code est créé
		if( !pmt_websites_add($cod_id, [$config['wst_id']]) ){
			pmt_codes_del( $cod_id );
			error_log(__FILE__.':'.__LINE__.' Impossible de créer la liason entre le code promotion et le site internet "'.$birthday_configs['type'].'"');
			continue;
		}

		$cfg = $birthday_configs["config"];

		// Période d'activité du code promotion
		$date_start = date('Y-m-d 00:00:00');
		$date_stop  = date('Y-m-d 23:59:59', strtotime('+'.$birthday_configs['days'].' days'));

		$free_shipping = array_key_exists('srv', $cfg) && is_array($cfg['srv']) && count($cfg['srv']);

		$pmt = false;
		switch ($birthday_configs['type']) {
			case 'port' :
			case _PMT_TYPE_CODE:
				if (!isset($cfg["code"]["on"]) || !trim($cfg["code"]["on"])) {
					$cfg["code"]["on"] = 'order';
				}

				$pmt = pmt_offers_add( $cod_id, _PMT_TYPE_CODE, $cfg["code"]["value"], $cfg["code"]["type"], 1, 0, 0, 0, $cfg["code"]["on"], $date_start, $date_stop, 0, false, $free_shipping, true);
				break;
			case _PMT_TYPE_PRD:
				$pmt = pmt_offers_add( $cod_id, _PMT_TYPE_PRD, 0, 0, 1, 0, 1, 0, null, $date_start, $date_stop, 1, false, $free_shipping, false, false, false, array($cfg["product"]["id"] =>1));
				break;
			default:
				break;
		}

		if (!$pmt) {
			pmt_codes_del( $cod_id );
			error_log(__FILE__.':'.__LINE__.' Type de promotion anniversaire non prit en charge "'.$birthday_configs['type'].'"');
			continue;
		}

		// Minimum de commande
		if($birthday_configs['min-ttc'] > 0){
			$grp_cdt = pmt_code_groups_add($cod_id);

			if (!$grp_cdt || !pmt_code_conditions_add( $cod_id, 2 , ">=" , $grp_cdt, $birthday_configs["min-ttc"], "order")) {
				pmt_codes_del( $cod_id );
				error_log(__FILE__.':'.__LINE__.' Impossible de créer la condition du minimum de commande "'.$birthday_configs['min-ttc'].'"');
				continue;
			}
		}

		if($free_shipping){
			$res = true;
			foreach ($cfg["srv"] as $srv_id) {
				if (!pmt_codes_add_service($cod_id, $srv_id)) {
					$res = false;
				}
			}

			if (!$res) {
				pmt_codes_del( $cod_id );
				error_log(__FILE__.':'.__LINE__.' Impossible de créer les conditions sur les frais de port offerts');
				continue;
			}
		}

		ria_mysql_data_seek( $r_users, 0 );

		while ($usr = ria_mysql_fetch_assoc($r_users)) {
			if (!rwd_users_is_enabled($usr['id'])) {
				continue;
			}

			// Pour Chadog, seuls les revendeurs (catégorie tarifaire) recevront le code promotion pour l'anniversaire
			if( $config['tnt_id'] == 171 ){
				if( $usr['prc_id'] != 6569 ){
					continue;
				}
			}

			pmt_users_add( $cod_id, $usr["id"], true);
			pmt_codes_send( $cod_id, "dob-promo", $usr["id"]);
		}
	}