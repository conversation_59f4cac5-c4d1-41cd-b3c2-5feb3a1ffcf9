<?php

	/** \file rebuild-documents-preview.php
	 *
	 * 	Ce script est destiné à reconstruire les miniatures de documents.
	 *	Il est destiné à être lancé automatiquement.
	 *
	 * 	Ce script ne peut être appelé sans identifiant de tenant en paramètre.
	 */

	if( !isset($ar_params) ){
		print "L'exécution de ce script nécessite l'appel de execute-script.php.".PHP_EOL;
		exit;
	}

	require_once('documents.inc.php');

	foreach( $configs as $config ){
		if( !isset($config['doc_preview_types'], $config['doc_preview_convert'], $config['doc_preview_dir'], $config['doc_preview_type_recursive']) ){
			continue;
		}

		if( trim($config['doc_preview_dir']) == '' ){
			continue;
		}

		$ar_types = array(0);
		if( is_array($config['doc_preview_types']) && count($config['doc_preview_types']) ){
			$ar_types = $config['doc_preview_types'];
		}

		$ar_docs = array();
		foreach( $ar_types as $one_type ){

			foreach( $config['i18n_lng_used'] as $lng_code ){
				// Récupération des documents qui ont besoin de reconstruire leur miniature
				$r_document = doc_documents_get(0, $one_type, $config['wst_id'], '', false, $lng_code, $config['doc_preview_type_recursive'], true);
				if( !$r_document && ria_mysql_num_rows($r_document) ){
					continue;
				}

				while( $document = ria_mysql_fetch_assoc($r_document) ){
					// Emplacement du document
					$path = $config['doc_dir'].'/'.($lng_code!=$config['i18n_lng'] ? $lng_code.'-' : '').$document['id'];
					if( !is_file($path) ){
						$path = $config['doc_dir'].'/'.$document['id'];
						if( !is_file($path) ){
							continue;
						}
					}

					try {

						// Emplacement de la preview
						$pathpreview = $config['doc_preview_dir'].'/'.( $lng_code!=$config['i18n_lng'] ? $lng_code.'-' : '' ).$document['id'].'-'.$document['size'].'.jpg';
						$output=null;
						$retval=null;

						if(!exec('/usr/bin/convert "'.$path.'[0]" '.$config['doc_preview_convert'].' "'.$pathpreview.'"', $output, $retval)){
							if(!empty($output)){
								mail('<EMAIL>', '[ERROR - SCRIPT - TNT_ID:'.$config['tnt_id'].']] rebuild-documents-preview', "Statut : {$retval}; Output : " . print_r($output, true));
							}
							
						}
						doc_document_set_rebuild_preview($document['id'], false);

					}catch(Exception $e){
						mail('<EMAIL>', '[ERROR - SCRIPT - TNT_ID:'.$config['tnt_id'].'] rebuild-documents-preview', $e->getMessage());
					}
				}
			}
		}
	}

