<?php
	/**	\file refresh-stats-pre-caculated.php
	 *	Ce script .....................
	 *
	 */

	if (!isset($ar_params)) {
		print "L'exécution de ce script nécessite l'appel de execute-script.php.".PHP_EOL;
		exit;
	}

	require_once('ord.invoices.inc.php');
	require_once('stats.inc.php');

	$date_start = '';

	foreach( $configs as $config ){
		if( !isset($ar_params['start']) ){
			$date = new DateTime();

			// HACK Zolux/Francodex/ST-Bernard : cause d'une synchro ne pouvant pas tourné toute la journée car serveur client de merde
			if( in_array($config['tnt_id'], [268, 1053, 1118]) ){
				$date->modify('-10 days');
			}else{
				$date->modify('-7 days');
			}

			$date_start = $date->format('Y-m-d');
		}else{
			if( !isdate($ar_params['start']) ){
				return;
			}

			$date_start = $ar_params['start'];
		}

		$r_invoice = ord_invoices_get( 0, 0, 0, false, false, false, false, 0, $date_start );

		if( !$r_invoice ){
			continue;
		}

		$i = 1;
		$count = ria_mysql_num_rows( $r_invoice );
		while( $invoice = ria_mysql_fetch_assoc($r_invoice) ){
			if( isset($ar_params['debug']) ){
				print ($i++).' / '.$count.' => '.$invoice['id'].PHP_EOL;
			}

			stats_invoices_pre_calculted_exec( $invoice['id'], $invoice['usr_id'] );
		}

		if( isset($ar_params['debug']) ){
			print 'Nombre de facture prisent en compte : '.ria_mysql_num_rows( $r_invoice ).PHP_EOL;
		}
	}