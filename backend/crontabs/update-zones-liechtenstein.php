<?php
/** \file update-zones-liechtenstein.php
 *	\ingroup system crontabs
 * 	Ce script est destiné à mettre à jour les zones (Régions, province, Communes, Codes postaux) pour le liechtenstein.
 * 	Description du code postal du liechtenstein
 * 	4 chiffres 
 */
set_include_path(dirname(__FILE__) . '/../include/');

require_once('db.inc.php');
require_once('strings.inc.php');
require_once('sys.zones.inc.php');
require_once('geo-api.inc.php');

/* Identifiants de types de zones :
	*		- Région : 22
	*		- Code postal : 46
	*		- Ville : 23
	*/
	
$ar_region_ids = $ar_departement_ids = $ar_commune_ids = $ar_codepostal_ids = array();
	
$zone_file = api_geogouv_get_zones();
if (trim($zone_file) == '') {
	$error = true;
	error_log(__FILE__.':'.__LINE__.'Aucune données trouvées');
	exit;
}
$zone_file = json_decode($zone_file,true);
foreach ($zone_file as $line) {
	$ville       = $line['place'];
	$zip_code    = $line['zipcode'];
	$zone        = $line['state'];
	$zone_code   = $line['state_code'];
	//Recherche la région en base et l'a créée si elle n'existe pas
	$zone_get = api_geogouv_add_zone(_ZONE_REGION_LIECHTENSTEIN, $zone_code, $zone,0,'LI');
	if (!$zone_get) {
		$error = true;
		error_log(__FILE__.':'.__LINE__.'Erreur lors de la création / récupération de la région : "'.htmlspecialchars($zone_code.' - '.$zone).'"');
		continue;
	}else{
		$ville = strtoupper2($ville);
		print 'Commune : '. $ville .PHP_EOL;
		$zipcode_zone = api_geogouv_add_zone(_ZONE_ZIPCODE_LIECHTENSTEIN, $zip_code, $zip_code, $zone_get);
		if (!$zipcode_zone) {
			$error = true;
			error_log(__FILE__.':'.__LINE__.'Erreur lors de la création / récupération du code postal : "' . htmlspecialchars($zip_code) . '"');
			continue;
		}else{
			// Recherche la commune et l'a créée si elle n'existe pas
			$commune_zone = api_geogouv_add_zone(_ZONE_VILLE_LIECHTENSTEIN, $ville, $ville, $zipcode_zone);
			if (!$commune_zone) {
				$error = true;
				error_log(__FILE__.':'.__LINE__.'Erreur lors de la création / récupération de la commune : "'.htmlspecialchars($zip_code.' - '.$ville).'"');
				continue;
			}
			print 'Code postal : ' . $zip_code . PHP_EOL;
			$ar_codepostal_ids[] = $zipcode_zone;
			$ar_commune_ids[] = $commune_zone;
		}
	}
	$ar_region_ids[] = $zone_get;

}

// Deprecated des zones plus utilisées
if (!isset($error)) {
	sys_zones_set_deprecated_out_ids(_ZONE_REGION_LIECHTENSTEIN, $ar_region_ids);
	sys_zones_set_deprecated_out_ids(_ZONE_VILLE_LIECHTENSTEIN, $ar_commune_ids);
	sys_zones_set_deprecated_out_ids(_ZONE_ZIPCODE_LIECHTENSTEIN, $ar_codepostal_ids);

	// Reconstruction de la hiérarchie
	sys_zones_hierarchy_rebuild(0, 'LI');
}

/**
 * Cette fonction permet la récupération du json de toutes les données de rgion,département,ville et CP de l'allemagne
 * @return [type] [description]
 */
function api_geogouv_get_zones(){

	$url_api = 'https://raw.githubusercontent.com/TrustChainEG/postal-codes-json-xml-csv/master/data/LI/zipcodes.li.json';

	$ch = curl_init();
	curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
	curl_setopt($ch, CURLOPT_URL, $url_api);
	$result = curl_exec($ch);
	curl_close($ch);
	

	return $result;
}

	