<?php
/** \file update-zones-tahiti.php
 *	\ingroup system crontabs
 * 	Ce script est destiné à mettre à jour les zones (Régions, Communes, Codes postaux) pour le tahiti.
 * 	Description du code postal de tahiti
 * 	4 chiffres 
 */

set_include_path(dirname(__FILE__) . '/../include/');
require_once('db.inc.php');
require_once('strings.inc.php');
require_once('sys.zones.inc.php');
require_once('geo-api.inc.php');

/* Identifiants de types de zones :
	*		- Région : 24
	*		- Province : 25
	*		- Code postal : 47
	*		- Ville : 26
	*/
	
$ar_region_ids = $ar_departement_ids = $ar_commune_ids = $ar_codepostal_ids = array();
	
$tahiti = api_tahiti_get_zones();
foreach ($tahiti as $line) {

	$ville            = $line['ville'];
	$zip_code         = $line['zipcode'];
	$departement      = $line['departement'];
	$departement_code = $line['departement'];
	$region           = $line['region'];
	$region_code      = $line['region'];
	//Recherche la région en base et l'a créée si elle n'existe pas
	$region_get = api_geogouv_add_zone(_ZONE_ARCHIPEL_TAHITI, $region_code, $region,0,'PF');
	if (!$region_get) {
		$error = true;
		error_log(__FILE__.':'.__LINE__.'Erreur lors de la création / récupération de la région : "'.htmlspecialchars($region_code.' - '.$region).'"');
		continue;
	}else{
		$departement_get = api_geogouv_add_zone(_ZONE_ILE_TAHITI, $departement_code, $departement, $region_get);
		if (!$departement_get) {
			$error = true;
			error_log(__FILE__.':'.__LINE__.'Erreur lors de la création / récupération du département : "'.htmlspecialchars($departement_code.' - '.$departement).'"');
			continue;
		}else{
			$ville = strtoupper2($ville);
			print 'Commune : '. $ville .PHP_EOL;
			$zipcode_zone = api_geogouv_add_zone(_ZONE_ZIPCODE_TAHITI, $zip_code, $zip_code, $departement_get);
			if (!$zipcode_zone) {
				$error = true;
				error_log(__FILE__.':'.__LINE__.'Erreur lors de la création / récupération du code postal : "' . htmlspecialchars($zip_code) . '"');
				continue;
			}else{
				// Recherche la commune et l'a créée si elle n'existe pas
				$commune_zone = api_geogouv_add_zone(_ZONE_VILLE_TAHITI, $ville, $ville, $zipcode_zone);
				if (!$commune_zone) {
					$error = true;
					error_log(__FILE__.':'.__LINE__.'Erreur lors de la création / récupération de la commune : "'.htmlspecialchars($zip_code.' - '.$ville).'"');
					continue;
				}
				print 'Code postal : ' . $zip_code . PHP_EOL;
				$ar_codepostal_ids[] = $zipcode_zone;
				$ar_commune_ids[] = $commune_zone;
			}
			
		}
	}
	$ar_departement_ids[] = $departement_get;
	$ar_region_ids[] = $region_get;

}

// Deprecated des zones plus utilisées
if (!isset($error)) {
	sys_zones_set_deprecated_out_ids(_ZONE_ARCHIPEL_TAHITI, $ar_region_ids);
	sys_zones_set_deprecated_out_ids(_ZONE_ILE_TAHITI, $ar_departement_ids);
	sys_zones_set_deprecated_out_ids(_ZONE_VILLE_TAHITI, $ar_commune_ids);
	sys_zones_set_deprecated_out_ids(_ZONE_ZIPCODE_TAHITI, $ar_codepostal_ids);

	// Reconstruction de la hiérarchie
	sys_zones_hierarchy_rebuild(0, 'PF');
}


	