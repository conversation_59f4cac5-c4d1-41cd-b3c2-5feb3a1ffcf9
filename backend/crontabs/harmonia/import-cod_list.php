<?php
    /** \file import-cod_list.php
	 *
	 * 	Ce script est destiné à importer les code promo ajoutés ou modifier d'Harmonia.
	 */

    require_once("imports.inc.php");
    require_once("define.inc.php");


    // Lors de l'éxécution de l'import le fichier est de nouveau téléchargé à partir du ftp les modifications sur celui-ci ne sont pas prise en compte
    // Il faut donc créer un import de type file pour pouvoir sauvegarder l'ajout de l'entête au fichier.

    foreach( $configs as $config ){
        $owner_id = 0;

        // Information de connexion au ftp
        $url = "**************";
        $login = "yuto";
        $password = "Y-F2019_!a!";

        $col_sep = ';';
        $text_separator = '"';

        // Le paramètre "no_cod_list" permet d'ignorer la synchro de la liste de code (fld)
        // Identifiant du champ avancé contenant les codes
        $fld = 101478;

        // Nom du fichier sur le FTP contenant la liste des codes
        $filename = "YUTO-SYNC/X3toYUTO/codes_listes.txt";

        // Création du lien avec le FTP
        if( $ftp = ftp_connect($url) ){
            // Connexion au FTP
            if( ftp_login($ftp, $login, $password) ){
                // Active le mode passif (nécéssaire sinon la récupération des éléments du FTP échoue)
                ftp_pasv($ftp, true);

                // Récupère le contenu du fichier du FTP en local pour le traitement
                $res = ftp_get($ftp, $config['doc_dir'].'/temp-codes-lists.txt', $filename, FTP_BINARY);

                // Si le fichier en local a bien été créé alors on peut passer à la suite
                if( $res ){
                    $content = file_get_contents($config['doc_dir'].'/temp-codes-lists.txt');
                    $temp = explode("\n", $content);

                    $ar_val_ids = array();

                    // Insertion de chaque code dans les valeurs de restrictions du champs avancé
                    foreach( $temp as $t ){
                        // Supprime la présence de guillemet
                        $t = str_replace('"', '', $t);
                        if( trim($t) == '' ){
                            continue;
                        }
                        
                        $val_id = fld_restricted_values_get_id($fld, $t);
                        if( is_numeric($val_id) && $val_id > 0 ){
                            $ar_val_ids[] = $val_id;
                        }else{
                            // Création de la nouvelle valeur
                            $add = fld_restricted_values_add($fld, $t);
                            if( is_numeric($add) && $add > 0 ){
                                $ar_val_ids[] = $add;
                            }
                        }    
                    }

                    // Supprime les valeurs qui ne sont plus d'actualité
                    $r_val = fld_restricted_values_get(0, $fld);
                    if( $r_val ){
                        while( $val = ria_mysql_fetch_assoc($r_val) ){
                            if( !in_array($val['id'], $ar_val_ids) ){
                                fld_restricted_values_del($val['id']);
                            }
                        }
                    }
                }
            }

            ftp_close($ftp);
        }
    }