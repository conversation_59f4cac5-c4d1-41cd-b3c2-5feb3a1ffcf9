<?php
	/** \file update-segments-count-objects.php
	 *
	 * 	Ce script permet de mettre à jour le nombre d'objets retournés par chaque segments
	 */

	if (!isset($ar_params)) {
		die("L'exécution de ce script nécessite l'appel de execute-script.php." . PHP_EOL);
	}

	require_once('segments.inc.php');

	foreach( $configs as $config ){
		$rseg = seg_segments_get();

		if( $rseg && ria_mysql_num_rows($rseg) ){
			while( $seg = ria_mysql_fetch_assoc($rseg) ){
				seg_segments_count_objects( $seg['id'], true );
			}
		}
	}