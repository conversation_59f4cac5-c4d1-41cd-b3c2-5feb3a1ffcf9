<?php
/** \file import-orders.php
 *
 * 	Ce script est destiné à synchroniser les commandes de BeezUP vers RiaShop
 *	Il est lancé automatiquement deux fois par heure aux minutes 29 et 59.
 *
 * 	Le script récupère les commandes sur BeezUP sur les 3 derniers jours à la date de lancement.
 */

if (!isset($ar_params)) {
	die("L'exécution de ce script nécessite l'appel de execute-script.php." . PHP_EOL);
}

require_once('comparators/BeezUP.class.php');

foreach( $configs as $config ){
	// Vérifie que la place de marché est bien activée pour ce client, dans le cas contraire, passe au client suivant.
	if( !ctr_comparators_actived(CTR_BEEZUP) ){
		continue;
	}

	$date = new DateTime();
	$date->modify('-7 days');

	if( $config['tnt_id'] == 171 ){
		// Spé temporaire pour Naturanimo - peut-être supprimé après le 16/11/22 sans crainte
		$date2 = new DateTime('2022-11-10 15:00:00');

		$now = new DateTime();

		// limite la récup au plus tard au commande passé après 15h
		if( $date < $date2 ){
			$date = $date2;
		}

		// Si script lancé avant 15h on va pas plus loin
		if( $now < $date2 ){
			continue;
		}
	}

	$beezup = BeezUP::create();
	$beezup->syncOrders( $date->format('Y-m-d') );
}