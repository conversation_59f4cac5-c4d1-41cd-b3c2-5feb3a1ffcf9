<?php

	/** \file update-priceandquantity-ebay.php
	 *
	 *	Ce cron permet de mettre à jour le tarif et la quantité des produits exportés vers la ebay.
	 *	Seuls les produits dont l'une de ces deux informations n'est pas à jour seront actualisés.
	 *
	 */
	if (!isset($ar_params)) {
		die("L'exécution de ce script nécessite l'appel de execute-script.php." . PHP_EOL);
	}

	// Contrôle qu'un script n'est pas déjà lancé
	require_once('tsk.comparators.inc.php');
	require_once('comparators/ctr.ebay.inc.php');


	// active ou non le mode test
	$mode_test = isset($ar_params['test']) && $ar_params['test'] == 'test';

	// Traitement
	foreach( $configs as $config ){
		// Vérifie que la place de marché est bien activée pour ce client, dans le cas contraire, on passe au client suivant.
		if( !ctr_comparators_actived(CTR_EBAY) ){
			continue;
		}

		// Récupère le catalogue exporté vers eBay
		$rcatalog = ctr_catalogs_get( CTR_EBAY, 0, 0, true );
		if( !$rcatalog || !ria_mysql_num_rows($rcatalog) ){
			continue;
		}

		$ebay = new EBay( $mode_test );
		if( !isset($ebay->user_id) || !is_numeric($ebay->user_id) || $ebay->user_id<=0 ){
			// Module RiaShoppping plus suivi, plus d'envoi de message
		}

		$error = array(); $batch_ids = array();
		while( $ctl = ria_mysql_fetch_array($rcatalog) ){
			// Récupère le tarif du produit ainsi que sa quantité en stock
			$rprice = prd_products_get_price( $ctl['prd_id'], $ebay->user_id );
			if( !$rprice || !ria_mysql_num_rows($rprice) ){
				$error[] = 'Produit '.$ctl['prd_id'].', impossible de récupérer le tarif de ce produit';
				continue;
			}

			$price = ria_mysql_fetch_array( $rprice );

			$old_price = 0;
			$pmt = prc_promotions_get( $ctl['prd_id'], $ebay->user_id );
			if( isset($pmt['price_ht'], $pmt['price_ttc']) ){
				$old_price = $price['price_ttc'];
				$price['price_ht'] = $pmt['price_ht'];
				$price['price_ttc'] = $pmt['price_ttc'];
			}

			$stock = 0;

			$dps = prd_deposits_get_main();
			if( !$dps ){
				$dps = 0;
			}

			$rsto = prd_dps_stocks_get( $ctl['prd_id'], $dps );
			if( $rsto && ria_mysql_num_rows($rsto) ){
				$sto = ria_mysql_fetch_assoc( $rsto );

				$stock = $sto['qte'] - $sto['prepa'];
				if( !is_numeric($stock) ){
					continue;
				}
			}

			$forced = false;
			if( $config['tnt_id'] == 16 ){
				if( prd_products_get_countermark($ctl['prd_id']) ){
					$forced = true;
				}
			}

			// Si le stock ou le prix est différent, on met à jour ces informations sur eBay
			if( round($ctl['price_ht'], 2)!=round($price['price_ht'], 2) || $ctl['qte']!=$stock || $forced ){
				if( !$ebay->worqueueEBayProduct($ctl['prd_id'], 'updatepriceqte', $price['price_ttc'], $stock, $old_price) ){
					$error[] = 'Produit '.$ctl['prd_id'].', une erreur s\'est produite lors de la mise à jour du prix / quantité.';
					continue;
				}else{
					ctr_catalogs_update_price( CTR_EBAY, $ctl['prd_id'], $price['price_ht'] );
					ctr_catalogs_update_quantity( CTR_EBAY, $ctl['prd_id'], $stock );
				}
			}

			$batch_ids[] = $ctl['prd_id'];
		}

		if( sizeof($batch_ids) ){
			// création de la tache de mise à jour des tarifs pour les produits envoyés
			$ar_exec = array();
			foreach( $batch_ids as $p ){
				if( ($tsk = tsk_comparators_add( CTR_FNAC, $p, 'update-priceqte' )) ){
					$ar_exec[] = $tsk;
				}
			}

			// mise à jour de la date d'exécution des tâches
			tsk_comparators_set_completed( $ar_exec );
		}

		if( sizeof($error) ){
			// Module RiaShoppping plus suivi, plus d'envoi de message
		}
	}
