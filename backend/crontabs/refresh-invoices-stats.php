<?php

	/**	\file refresh-invoices-stats.php
	 *
	 *	Ce script est lancé que ponctuellement pour reparcourir toutes les factures pour un clients et les envoyer dans le couchdb
	 *
	 */

	set_include_path(dirname(__FILE__) . '/../include/');
	
	require_once( 'db.inc.php' );
	require_once( 'tenants.inc.php' );
	require_once( 'ord.invoices.inc.php' );

	$rinvoices = ord_invoices_get( 0, 0, date('Y') );

	function push_to_worker($ids){
		RiaQueue::getInstance()->addJob(RiaQueue::WORKER_INVOICE_STATS_PUT, array('ids'=>$ids));
	}

	$ids = array();
	while( $inv = ria_mysql_fetch_assoc($rinvoices) ){
		if( sizeof($ids) > 200 ) {
			push_to_worker($ids);
			$ids = array();
		}
		$ids[] = $inv['id'];
	}

	push_to_worker($ids);
