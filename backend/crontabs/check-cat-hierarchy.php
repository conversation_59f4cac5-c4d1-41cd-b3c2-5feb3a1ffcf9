<?php
	/** \file check-cat-hierarchy.php
	 * 	Ce script est destiné à contrôler le contenu de la table prd_cat_hierarchy.
	 *	Pour le bon fonctionnement de l'udf (get_price_ht et get_tva), il est indispensable
	 *	que la table prd_cat_hierarchy ne contienne aucune référence à des catégories supprimées
	 *	(physiquement ou virtuellement).
	 */

	if (!isset($ar_params)) {
		die("L'exécution de ce script nécessite l'appel de execute-script.php." . PHP_EOL);
	}

	foreach( $configs as $config ){
		$rcat = ria_mysql_query('
			select cat_tnt_id as tnt_id, cat_id as id, cat_name as name
			from prd_categories as c
			where '.($config['tnt_id'] ? 'cat_tnt_id = '.$config['tnt_id'].' and ' : '').' cat_date_deleted is not null
				and exists (
					select *
					from prd_cat_hierarchy as h
					where h.cat_tnt_id=c.cat_tnt_id
						and ( cat_child_id=cat_id or cat_parent_id=cat_id )
				);
		');

		if( ria_mysql_num_rows($rcat) ){
			echo 'Des catégories supprimées ont encore des entrées dans prd_cat_hierarchy :';
			while( $cat = ria_mysql_fetch_array($rcat) ){
				echo $cat['tnt_id'], ' ', $cat['id'], ' ', $cat['name'], "\n";
				ria_mysql_query('
					delete from prd_cat_hierarchy
					where cat_tnt_id='.$cat['tnt_id'].'
						and ( cat_parent_id='.$cat['id'].' or cat_child_id='.$cat['id'].' )
				');
			}
		}
	}