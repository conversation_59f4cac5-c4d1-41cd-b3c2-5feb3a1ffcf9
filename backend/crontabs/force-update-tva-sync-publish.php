<?php

set_include_path(dirname(__FILE__) . '/../include/');
require_once( 'db.inc.php' );
require_once( 'cfg.variables.inc.php' );
require_once( 'orders.inc.php' );

// liste des taux modifiés
$rates = array(
	array(1.196, 1.2),
	array(1.07, 1.1)
);

// locataires à modifier + indicateur si le prix de vente absorbe la différence ou non + website pour le chargement de la configuration + ID de dépôt principal
$tenants = array();
$tenants[] = array(1, true, 1, 1); // confirmé (public uniquement pour la MAJ des prix)
$tenants[] = array(4, false, 8, 8); // confirmé
//$tenants[] = array(5, true, 9, 2);
$tenants[] = array(6, true, 10, 1);
//$tenants[] = array(13, true, 19, 43);
//$tenants[] = array(14, true, 20, 968);
//$tenants[] = array(16, true, 23, 59);
$tenants[] = array(21, false, 33, 76); // confirmé
//$tenants[] = array(22, true, 34, 936);

foreach( $tenants as $tenant ){
	
	foreach( $rates as $rate ){
		
		if( $tenant[1] ){
			
			$sql = '
				update prc_prices
				set prc_value = ( ( prc_value * '.$rate[0].' ) / '.$rate[1].' )
				where prc_is_deleted = 0
				and prc_tnt_id = '.$tenant[0].'
				and prc_date_end >= NOW()
				and prc_type_id = 1
				and exists (
					select 1 from prc_tvas
					join prd_products on ptv_prd_id = prd_id and ptv_tnt_id = prd_tnt_id
					left join prd_stocks on prd_id = sto_prd_id and prd_tnt_id = sto_tnt_id and sto_is_deleted=0
					where ptv_tnt_id = prc_tnt_id and ptv_prd_id = prc_prd_id and ptv_date_deleted is null and ptv_tva_rate = '.$rate[0].'
					and ifnull(ptv_cac_id, -1) <= 0 and prd_date_deleted is null and prd_publish = 1
					and ( prd_sleep = 0 or ( sto_dps_id = '.$tenant[3].' and (sto_qte - sto_prepa) > 0 ) )
				)
			';
			
			if( $tenant[0] == 1 ){
				$sql .= '
					and exists (
						select 1 from prc_price_conditions
						where ppc_tnt_id = prc_tnt_id and ppc_prc_id = prc_id
						and ppc_fld_id = 456 and ppc_symbol = "=" and ppc_value = "3"
					)
				';
			}
			
			ria_mysql_query($sql);
			
			if( ria_mysql_errno() ){
				error_log( __FILE__.':'.__LINE__.' - '.mysql_error().' - '.$sql );
				exit;
			}else{
				print "Mise à jour des prix (tenant ".$tenant[0].", taux ".$rate[1].")\n";
			}
			
			if( $tenant[0] == 1 ){
				
				$sql = '
					update prc_prices
					set prc_value = ( ( prc_value * '.$rate[0].' ) / '.$rate[1].' )
					where prc_is_deleted = 0
					and prc_tnt_id = '.$tenant[0].'
					and prc_date_end >= NOW()
					and prc_type_id = 1
					and exists (
						select 1 from prc_tvas
						join prd_products on ptv_prd_id = prd_id and ptv_tnt_id = prd_tnt_id
						left join prd_stocks on prd_id = sto_prd_id and prd_tnt_id = sto_tnt_id and sto_is_deleted=0
						where ptv_tnt_id = prc_tnt_id and ptv_prd_id = prc_prd_id and ptv_date_deleted is null and ptv_tva_rate = '.$rate[0].'
						and ifnull(ptv_cac_id, -1) <= 0 and prd_date_deleted is null and prd_publish = 1
						and ( prd_sleep = 0 or ( sto_dps_id = '.$tenant[3].' and (sto_qte - sto_prepa) > 0 ) )
					) and not exists (
						select 1 from prc_price_conditions
						where ppc_tnt_id = prc_tnt_id and ppc_prc_id = prc_id
					)
				';
				
				ria_mysql_query($sql);
				
				if( ria_mysql_errno() ){
					error_log( __FILE__.':'.__LINE__.' - '.mysql_error().' - '.$sql );
					exit;
				}else{
					print "Mise à jour des prix spec bigship (tenant ".$tenant[0].", taux ".$rate[1].")\n";
				}
			}
			
		}
		
		$sql = '
			update prc_tvas
			set ptv_tva_rate = '.$rate[1].'
			where ptv_tnt_id = '.$tenant[0].' and ptv_date_deleted is null and ptv_tva_rate = '.$rate[0].' and ifnull(ptv_cac_id, -1) <= 0
			and exists (
				select 1 from prd_products
				left join prd_stocks on prd_id = sto_prd_id and prd_tnt_id = sto_tnt_id and sto_is_deleted=0
				where prd_tnt_id = ptv_tnt_id and prd_id = ptv_prd_id
				and prd_date_deleted is null and prd_publish = 1
				and ( prd_sleep = 0 or ( sto_dps_id = '.$tenant[3].' and (sto_qte - sto_prepa) > 0 ) )
			)
		';
		
		ria_mysql_query($sql);
		
		if( ria_mysql_errno() ){
			error_log( __FILE__.':'.__LINE__.' - '.mysql_error().' - '.$sql );
			exit;
		}else{
			print "Mise à jour de la TVA (tenant ".$tenant[0].", taux ".$rate[1].").\n";
		}
		
	}
	
}

// on boucle une deixème fois pour les paniers (plus lent que la première phase au dessus)
foreach( $tenants as $tenant ){
	
	// commande à l'état de panier, de moins de 3 mois ou client connu
	$sql = '
		select ord_id as id from ord_orders
		where ord_tnt_id = '.$tenant[0].' and ord_state_id in ('.implode(', ', ord_states_get_uncompleted()).') and (
			ord_usr_id is not null or DATEDIFF(now(), ord_date) < 92
		)
	';
	
	$r = ria_mysql_query($sql);
	
	if( $r ){
		
		unset($config);
		$config['tnt_id'] = $tenant[0];
		$config['wst_id'] = $tenant[2];
		
		cfg_variables_load($config);
		
		while( $ord = ria_mysql_fetch_array($r) ){
			ord_orders_refresh($ord['id']);
		}
		
		print "Mise à jour des paniers du tenant ".$config['tnt_id'].".\n";
		
	}else{
		
		if( ria_mysql_errno() ){
			error_log( __FILE__.':'.__LINE__.' - '.mysql_error().' - '.$sql );
			exit;
		}
		
	}

}

print "Fin de procédure\n";

