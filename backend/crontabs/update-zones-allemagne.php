<?php
/** \file update-zones-allemagne.php
 *	\ingroup system crontabs
 * 	Ce script est destiné à mettre à jour les zones (Régions, province, Communes, Codes postaux) pour l'Allemagne.
 * 	Description du code postal Allemand
 * 	5 chiffres 
 * 	1xxxx – Le premier chiffre représente une zone de régions allemandes. Le numéro 1 présente les régions Berlin, Brandebourg, Mecklembourg-Poméranie occidentale et certaines parties de Saxe.
 * 	10xxx – Le deuxième chiffre représente une région. Le numéro 10 présente la région de Berlin.
 *  10115 – Les trois derniers chiffres représentent une certaine ville, commune et groupe de district de livraison. Numéro 115 caractéristiques Berlin-Mitte.
 */

set_include_path(dirname(__FILE__) . '/../include/');

require_once('db.inc.php');
require_once('strings.inc.php');
require_once('sys.zones.inc.php');
require_once('geo-api.inc.php');

/* Identifiants de types de zones :
	*		- Région : 4
	*		- Département : 14
	*		- Code postal : 43
	*		- Ville : 15
	*/
	
$ar_region_ids = $ar_departement_ids = $ar_commune_ids = $ar_codepostal_ids = array();
	
$allemagne = api_geogouv_get_zones();
if (trim($allemagne) == '') {
	$error = true;
	error_log(__FILE__.':'.__LINE__.'Aucune données trouvées');
	exit;
}
$allemagne = json_decode($allemagne,true);
foreach ($allemagne as $line) {
	$ville       = $line['place'];
	$zip_code    = $line['zipcode'];
	$region      = $line['community'];
	$region_code = $line['community_code'];
	$zone        = $line['state'];
	$zone_code   = $line['state_code'];
	//Recherche la région en base et l'a créée si elle n'existe pas
	$zone_get = api_geogouv_add_zone(_ZONE_ZONE_ALLEMAGNE, $zone_code, $zone,0,'DE');
	if (!$zone_get) {
		$error = true;
		error_log(__FILE__.':'.__LINE__.'Erreur lors de la création / récupération de la région : "'.htmlspecialchars($zone_code.' - '.$zone).'"');
		continue;
	}else{
		$region_get = api_geogouv_add_zone(_ZONE_REGION_ALLEMAGNE, $region_code, $region, $zone_get);
		if (!$region_get) {
			$error = true;
			error_log(__FILE__.':'.__LINE__.'Erreur lors de la création / récupération du département : "'.htmlspecialchars($region_code.' - '.$region).'"');
			continue;
		}else{
			$ville = strtoupper2($ville);
			print 'Commune : '. $ville .PHP_EOL;
			$zipcode_zone = api_geogouv_add_zone(_ZONE_ZIPCODE_ALLEMAGNE, $zip_code, $zip_code, $region_get);
			if (!$zipcode_zone) {
				$error = true;
				error_log(__FILE__.':'.__LINE__.'Erreur lors de la création / récupération du code postal : "' . htmlspecialchars($zip_code) . '"');
				continue;
			}else{
				// Recherche la commune et l'a créée si elle n'existe pas
				$commune_zone = api_geogouv_add_zone(_ZONE_VILLE_ALLEMAGNE, $ville, $ville, $zipcode_zone);
				if (!$commune_zone) {
					$error = true;
					error_log(__FILE__.':'.__LINE__.'Erreur lors de la création / récupération de la commune : "'.htmlspecialchars($zip_code.' - '.$ville).'"');
					continue;
				}
				print 'Code postal : ' . $zip_code . PHP_EOL;
				$ar_codepostal_ids[] = $zipcode_zone;
				$ar_commune_ids[] = $commune_zone;
			}
			
		}
	}
	$ar_departement_ids[] = $region_get;
	$ar_region_ids[] = $zone_get;

}

// Deprecated des zones plus utilisées
if (!isset($error)) {
	sys_zones_set_deprecated_out_ids(_ZONE_ZONE_ALLEMAGNE, $ar_region_ids);
	sys_zones_set_deprecated_out_ids(_ZONE_REGION_ALLEMAGNE, $ar_departement_ids);
	sys_zones_set_deprecated_out_ids(_ZONE_VILLE_ALLEMAGNE, $ar_commune_ids);
	sys_zones_set_deprecated_out_ids(_ZONE_ZIPCODE_ALLEMAGNE, $ar_codepostal_ids);

	// Reconstruction de la hiérarchie
	sys_zones_hierarchy_rebuild(0, 'DE');
}

/**
 * Cette fonction permet la récupération du json de toutes les données de région, département, ville et CP de l'Allemagne
 * @return [type] [description]
 */
function api_geogouv_get_zones(){

	$url_api = 'https://raw.githubusercontent.com/TrustChainEG/postal-codes-json-xml-csv/master/data/DE/zipcodes.de.json';

	$ch = curl_init();
	curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
	curl_setopt($ch, CURLOPT_URL, $url_api);
	$result = curl_exec($ch);
	curl_close($ch);
	

	return $result;
}

	