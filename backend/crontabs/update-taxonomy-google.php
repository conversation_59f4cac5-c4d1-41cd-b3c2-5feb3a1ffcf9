<?php
	/** \file update-taxonomy-google.php
	 *
	 * 	Ce script est destiné à mettre à jour automatiquement les familles de Google Shopping
	 *	pour que ces familles puissent être sélectionnées dans notre back-office
	 *
	 */

	set_include_path(dirname(__FILE__) . '/../include/');
	require_once('define.inc.php');
	require_once('comparators.inc.php');
	require_once('email.inc.php');
	require_once('GoogleShoppingTaxonomy.inc.php');

	// Crée un tableau de toutes les familles array( id=>name ) et des identifiants parent( id=>parent )
	$families = array(); $infos = array();
	$rcat = ctr_categories_get( CTR_GOOGLE );
	if( $rcat ){
		while( $cat = ria_mysql_fetch_array($rcat) ){
			$families[ $cat['id'] ] = strtolower2( $cat['name'] );
			$infos[ $cat['id'] ] = array('parent'=>$cat['parent'], 'disabled'=>$cat['is_disabled']);
		}
	}

	$taxo = new GoogleShoppingTaxonomy();

	$idCategorie = array();
	$add = $move = $del = array();

	$used_fml = array();
	$entries = $taxo->getEntries();

	if( is_array($entries) && sizeof($entries) ){
		foreach( $taxo->getEntries() as $family ){

			if( !trim($family) ){
				continue;
			}

			// découpe la chaine pour récupérer les sous familles
			$names = explode( ' > ', $family );
			$sfs = explode( ' > ', strtolower2($family) );

			$parentID = 0; $pos = 0;
			foreach( $sfs as $sf ){

				$name = $names[ $pos ];
				$catID = in_array($sf, $families) ? array_search( $sf, $families ) : 0;

				if( !$catID ){

					// La famille n'existe pas encore de notre côté, elle doit être créée
					$catID = ctr_categories_add( CTR_GOOGLE, $name, '', $parentID );
					if( $catID ){
						//mettre à jours les tableaux $families et $infos
						$families[ $catID ] = $sf;
						$infos[ $catID ] = array( 'parent'=>$parentID, 'disabled'=>0 );

						$add[] = array( 'id'=>$catID, 'name'=>$name );
					}

				}else{

					// Réactive une catégorie désactivée de notre côté
					ctr_categories_update_disabled( CTR_GOOGLE, $catID, false );

					if( $parentID!=$infos[$catID]['parent'] ) { // la catégorie existe, mais n'est plus classée au même endroit

						// Déplace la catégorie à son nouvel emplacement
						ctr_categories_update_parent( CTR_GOOGLE, $catID, $parentID );

						$move[ $catID ] = array(
							'name' => $sf,
							'id0' => $infos[ $catID ]['parent'],
							'name0' => isset($families[ $infos[$catID]['parent'] ]) ? $families[ $infos[$catID]['parent'] ] : '',
							'id1' => $parentID,
							'name1' => isset( $families[$parentID] ) ? $families[$parentID] : ''
						);

						$infos[ $catID ]['parent'] = $parentID;
					}
				}

				$parentID = $catID;
				$pos++;

				$used_fml[] = $catID;
			}

		}

		$ar_keys = array_keys( $families );
		$used_fml = array_unique( $used_fml );

		// Désactivation des catégories qui n'existent plus
		$ar_diffs = array_diff($ar_keys, $used_fml);
		foreach( $ar_diffs as $cat ){
			ctr_categories_update_disabled( CTR_GOOGLE, $cat );
		}

		// Reconstruit la hiérarchie des familles du comparateur de prix
		ctr_categories_hierarchy_rebuild( CTR_GOOGLE );
	}

	// Si des mises à jour sont intervenues, informe par email les administrateurs
	if( sizeof($add) || sizeof($move) || sizeof($ar_diffs) ){

		$email = new Email();
		$email->setSubject('Mise à jour des familles de Google Shopping');
		$email->setFrom('root');
		$email->addTo('<EMAIL>');
		$email->addCC('<EMAIL>');
		$email->addCC('<EMAIL>');

		if( sizeof($add) ){
			$email->addBlankTextLine();
			$email->addTextLine('Nouvelles familles : ');
			foreach( $add as $i ){
				$email->addTextLine('	- ('.$i['id'].') '.utf8_decode($i['name']));
			}
		}
		if( sizeof($move) ){
			$email->addBlankTextLine();
			$email->addTextLine('Déplacements des familles suivantes : ');
			foreach( $move as $id=>$m ){
				$email->addTextLine('	- ('.$id.') '.$m['name']);
				$email->addTextLine('		de ('.$m['id0'].') '.$m['name0'].' vers ('.$m['id1'].') '.$m['name1']);
			}
		}
		if( sizeof($ar_diffs) ){
			$email->addBlankTextLine();
			$email->addTextLine('Familles désactivées : ');
			foreach( $ar_diffs as $id ){
				$email->addTextLine('	- ('.$id.') '.$families[$id]);
			}
			$email->addTextLine('Nombre de catégorie désactivées : '.sizeof($ar_diffs));
		}

		$email->send();
	}
