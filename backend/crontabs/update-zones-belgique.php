<?php
/** \file update-zones-belgique.php
 *	\ingroup system crontabs
 * 	Ce script est destiné à mettre à jour les zones (Régions, district, Communes, Codes postaux) pour la belgique.
 * 	Description du code postal de la Belgique
 * 	4 chiffres 
 * Les deux premiers chiffres représentent une province belge spécifique. Les codes postaux entre 30xx et 34xx caractérisent la province du Brabant flamand
 * Le troisième chiffre représente le bureau de poste
 * Le quatrième chiffre représente un bureau de livraison belge spécifique
 */
set_include_path(dirname(__FILE__) . '/../include/');

require_once('db.inc.php');
require_once('strings.inc.php');
require_once('sys.zones.inc.php');
require_once('geo-api.inc.php');

$file_text = api_geoname_get_zones('BE');
/* Identifiants de types de zones :
	*		- Région : 40
	*		- Province : 41
	*		- Code postal : 53
	*		- Ville : 42
	*/

if($file_text != ''){
	$cpt = 1;
	foreach ($file_text as $line) {
		$line = explode('	', $line);
		$ville       = $line[2];
		$zip_code    = $line[1];
		$region      = $line[5];
		$region_code = $line[6];
		$zone        = $line[3];
		$zone_code   = $line[4];

		//Recherche la région en base et l'a créée si elle n'existe pas
		$zone_get = api_geogouv_add_zone(_ZONE_REGION_BELGIQUE, $zone_code, $zone,0,'BE');
		if (!$zone_get) {
			$error = true;
			error_log(__FILE__.':'.__LINE__.'Erreur lors de la création / récupération de la région : "'.htmlspecialchars($zone_code.' - '.$zone).'"');
			continue;
		}

		$region_get = api_geogouv_add_zone(_ZONE_PROV_BELGIQUE, $region_code, $region, $zone_get);
		if (!$region_get) {
			$error = true;
			error_log(__FILE__.':'.__LINE__.'Erreur lors de la création / récupération du département : "'.htmlspecialchars($region_code.' - '.$region).'"');
			continue;
		}
		$ar_departement_ids[] = $region_get;
		$ar_region_ids[] = $zone_get;

		$ville = strtoupper2($ville);
		print 'Commune : '. $ville .PHP_EOL;
		$zipcode_zone = api_geogouv_add_zone(_ZONE_ZIPCODE_BELGIQUE, $zip_code, $zip_code, $region_get);
		if (!$zipcode_zone) {
			$error = true;
			error_log(__FILE__.':'.__LINE__.'Erreur lors de la création / récupération du code postal : "' . htmlspecialchars($zip_code) . '"');
			continue;
		}
		// Recherche la commune et l'a créée si elle n'existe pas
		$commune_zone = api_geogouv_add_zone(_ZONE_VILLE_BELGIQUE, $ville, $ville, $zipcode_zone);
		if (!$commune_zone) {
			$error = true;
			error_log(__FILE__.':'.__LINE__.'Erreur lors de la création / récupération de la commune : "'.htmlspecialchars($zip_code.' - '.$ville).'"');
			continue;
		}
		print 'Code postal : ' . $zip_code . PHP_EOL;
		$ar_codepostal_ids[] = $zipcode_zone;
		$ar_commune_ids[] = $commune_zone;
	}

	// Deprecated des zones plus utilisées
	if (!isset($error)) {
		sys_zones_set_deprecated_out_ids(_ZONE_REGION_BELGIQUE, $ar_region_ids);
		sys_zones_set_deprecated_out_ids(_ZONE_PROV_BELGIQUE, $ar_departement_ids);
		sys_zones_set_deprecated_out_ids(_ZONE_VILLE_BELGIQUE, $ar_commune_ids);
		sys_zones_set_deprecated_out_ids(_ZONE_ZIPCODE_BELGIQUE, $ar_codepostal_ids);
		// Reconstruction de la hiérarchie
		sys_zones_hierarchy_rebuild(0,"BE");
	}

}