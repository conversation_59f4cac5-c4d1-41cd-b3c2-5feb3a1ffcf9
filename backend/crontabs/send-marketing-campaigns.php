<?php
	/**	\file send-marketing-campaigns.php
	 *	\ingroup crontab campaigns
	 *	Ce script est charger d'envoyer les campagnes SMS / E-mail programmé toutes les nuits.
	 */

	if (!isset($ar_params)) {
		die("L'exécution de ce script nécessite l'appel de execute-script.php." . PHP_EOL);
	}

	require_once('Marketing/models/Campaigns.inc.php');

	// check pour la valeur de la campagne
	$cpg_id = 0;
	if (isset($ar_params['cpg'])) {
		if (!is_numeric($ar_params['cpg']) || $ar_params['cpg'] < 0) {
			//print "Veuillez renseigner un identifiant de campagne valide (numéric supérieur à zéro)." . PHP_EOL;
			return;
		}

		$cpg_id = $ar_params['cpg'];
	}

	foreach ($configs as $config) {
		if( !isset($config['marketing_is_active']) || !$config['marketing_is_active'] ){
			continue;
		}
		$date = new DateTime();

		$r_campaign = CampaignsManager::getCampaigns( $cpg_id, false, $date->format("Y-m-d H:i:s"), $date->format("Y-m-d H:i:s"), false );

		if( !$r_campaign ){
			continue;
		}

		while( $campaign = ria_mysql_fetch_assoc($r_campaign) ){

			if( trim( $campaign['period'] ) == '' ){
				continue;
			}

			$dateToSend = new DateTime($campaign['period'].' '.$campaign['period_info']);

			// si le jour ne convient pas on passe
			if( $dateToSend->format('Y-m-d') != $date->format('Y-m-d') ){
				continue;
			}
			// si l'envoie n'est pas dans l'heure on passe
			if( $dateToSend->format('H')-3 != $date->format('H') ){
				continue;
			}

			try{

				$obj = new Campaigns( $campaign );
				$obj->exec();

			}catch (Exception $e) {
				error_log($e->getMessage());
				continue;
			}
		}
	}