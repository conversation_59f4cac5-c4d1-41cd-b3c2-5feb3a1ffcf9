<?php

        if( !isset($_GET['email'], $_GET['ref'], $_GET['title'], $_GET['firstname'], $_GET['lastname'], $_GET['society'],
            $_GET['siret'], $_GET['address1'], $_GET['address2'], $_GET['postal_code'], $_GET['city'], $_GET['country'],
            $_GET['phone'], $_GET['fax'], $_GET['prc']) ){
            return;
        }

        $type = 1;//Particulier
        if( trim($_GET['society']) ){
            if( trim($_GET['firstname']) || trim($_GET['lastname']) ){
                $type = 3;//Professionnel
            }else{
                $type = 2;//Société
            }
        }

        if( $type==1 && !gu_titles_exists( $_GET['title'] ) ){
            return;
        }elseif( $type==2 && !trim($_GET['society']) ){
            return;
        }

        $prf = isset($_GET['prf']) ? $_GET['prf'] : null;
        $can_login = isset($_GET['can_login']) ? $_GET['can_login'] : true;

        if( $prf==1 ){
            error_log(__FILE__.':'.__LINE__.' - création d\'un compte administrateur (tnt = '.$config['tnt_id'].', ref = '.$_GET['ref'].')');
        }
        if( $can_login ){
            if(  !gu_valid_email($_GET['email']) ){
                error_log('[gu_users_add] tenant '.$config['tnt_id'].', email invalide : '.$_GET['email']."\n", 3, '/var/log/php/my_sync_log.log');
                return;
            }
        }

        // pas d'email de superadmin
        if( !gu_users_is_tenant_linked( 0, $_GET['email'], true ) ){
            return;
        }

        $ord_alert = null;
        if( isset($_GET['ord_alert']) ){
            $ord_alert = explode('|', $_GET['ord_alert']);
        }

        if ($config['tnt_id'] == 3 && preg_match("/^PR00.*/", trim($_GET['ref']))) {
            if (!is_null($ord_alert)) {
                $invoice_option_index = array_search(_STATE_INVOICE, $ord_alert);
                if (is_numeric($invoice_option_index)) {
                    unset($ord_alert[$invoice_option_index]);
                }
            }else{
                $ord_alert = array();
            }

            if (empty($ord_alert)) {
                if (isset($config['default_alerts']) && is_array($config['default_alerts'])) {
                    $ord_alert = $config['default_alerts'];
                    $invoice_option_index = array_search(_STATE_INVOICE, $ord_alert);
                    if (is_numeric($invoice_option_index)) {
                        unset($ord_alert[$invoice_option_index]);
                    }

                    if (empty($ord_alert)) {
                        $ord_alert = null;
                    }
                }else{
                    $ord_alert = null;
                }
            }
        }
        $usr = gu_users_add( $_GET['email'], gu_password_create(), $prf, $_GET['ref'], true, 0, 0, null, false, $can_login, 16, true, false, null, $ord_alert );

        if( $usr ){

            $adr = gu_adresses_add( $usr, $type, $_GET['title'], $_GET['firstname'], $_GET['lastname'], $_GET['society'], $_GET['siret'],
                $_GET['address1'], $_GET['address2'], $_GET['postal_code'], $_GET['city'], $_GET['country'], $_GET['phone'], $_GET['fax'] );
            if( !$adr ){
                gu_users_del($usr);
                error_log('[gu_adresses_add] tenant '.$config['tnt_id'].', échec avec les paramètres (usr = '.$usr.', type = '.$type.', get = '.str_replace(array("\r\n", "\r", "\n"), array(' - ', ' - ', ' - '), print_r($_GET, true)).')'."\n", 3, '/var/log/php/my_sync_log.log');
                return;
            }else{
                gu_users_address_set( $usr, $adr );
                gu_users_set_prc( $usr, $_GET['prc'] );
                if( isset($_GET['naf']) ){
                    gu_users_set_naf( $usr, $_GET['naf'] );
                }
                if( isset($_GET['website']) ){
                    gu_users_set_website( $usr, $_GET['website'] );
                }
                if( isset($_GET['taxcode']) ){
                    gu_users_set_taxcode( $usr, $_GET['taxcode'] );
                }
                if( isset($_GET['mobile']) ){
                    gu_adresses_set_mobile( $usr, $adr, $_GET['mobile'] );
                }
                if( isset($_GET['desc']) ){
                    gu_adresses_set_desc( $usr, $adr, $_GET['desc'] );
                }
                if( isset($_REQUEST['date_created']) ){
                    gu_users_set_date_created( $usr, $_REQUEST['date_created'] );
                }
                if( isset($_GET['sleep']) ){
                    fld_object_values_set( $usr, _FLD_USR_SLEEP, $_GET['sleep']==1 ? 'Oui' : null );
                }
                print $usr;
            }

        }else{

            $rusers = gu_users_get( 0,$_GET['email'] );
            $count_users = mysql_num_rows( $rusers );
            if( $count_users == 1 ){
                $usr = mysql_fetch_assoc($rusers);

                $ok_profil = false;
                if( ( $prf == PRF_RESELLER && in_array($usr['prf_id'], array(PRF_CUSTOMER, PRF_CUST_PRO)) ) || ( $prf == PRF_CUST_PRO && $usr['prf_id'] == PRF_CUSTOMER ) || ( $prf == PRF_CUSTOMER && $usr['prf_id'] == PRF_CUST_PRO ) ){
                    // on peut passer de 2 ou 3 à 4, mais pas l'inverse
                    if( gu_users_set_profile( $usr['id'], $prf ) ){
                        $ok_profil = true;
                    }
                }elseif( in_array($usr['prf_id'], array(PRF_ADMIN, PRF_SELLER))){
                    // Il ne faut pas remplacer les informations d'un administrateur (représentant) par les informations d'un client.
                    error_log('[gu_users_add] tenant '.$config['tnt_id'].', le client '.$_GET['email']." tente fusionner avec un administrateur.\n", 3, '/var/log/php/my_sync_log.log');
                    return;
                }elseif( in_array($usr['prf_id'], array(PRF_RESELLER)) && in_array($prf, array(PRF_CUSTOMER, PRF_CUST_PRO)) ){
                    // si coté RiaShop on est un revendeur ou un admin, le profil n'est pas modifié mais on peut passer à la suite
                    $ok_profil = true;
                }
                if( $prf == NULL || $prf == $usr['prf_id'] || $ok_profil ){
                    if( trim($usr['ref']) == '' ){
                        if( gu_users_set_ref( $_GET['email'], $_GET['ref'] ) ){
                            gu_users_set_is_sync( $usr['id'], true );
                            gu_adresses_set_updated( $usr['id'], $usr['adr_invoices'], true );
                            print $usr['id'];
                            return;
                        }else{
                            error_log('[gu_users_add] tenant '.$config['tnt_id'].', échec de la fusion pour une raison inconnue (sync : email = '.$_GET['email'].', ref = '.$_GET['ref'].'), (RIA ref = '.$usr['ref'].')'."\n", 3, '/var/log/php/my_sync_log.log');
                        }
                    }elseif( trim($usr['ref']) == trim($_GET['ref']) ){
                        // les comptes sont strictement les mêmes
                        gu_users_set_is_sync( $usr['id'], true );

                        if( isset($_GET['sleep']) ){
                            fld_object_values_set( $usr, _FLD_USR_SLEEP, $_GET['sleep']==1 ? 'Oui' : null );
                        }

                        print $usr['id'];
                        return;
                    }else{
                        error_log('[gu_users_add] tenant '.$config['tnt_id'].', code client non vide et différent pour les comptes à fusionner (sync : email = '.$_GET['email'].', ref = '.$_GET['ref'].'), (RIA ref = '.$usr['ref'].')'."\n", 3, '/var/log/php/my_sync_log.log');
                    }
                }else{
                    error_log('[gu_users_add] tenant '.$config['tnt_id'].', probleme de prf_id entre les comptes à fusionner ('.$_GET['email'].', profil sync : '.$prf.', profil RIA : '.$usr['prf_id'].')'."\n", 3, '/var/log/php/my_sync_log.log');
                }

            }elseif( $count_users > 1 ){
                error_log('[gu_users_add] tenant '.$config['tnt_id'].', email utilisée plusieurs fois : '.$_GET['email']."\n", 3, '/var/log/php/my_sync_log.log');
            }else{
                error_log('[gu_users_add] tenant '.$config['tnt_id'].', erreur inconnue'."\n", 3, '/var/log/php/my_sync_log.log');
            }

            print 'user add failed';

        }