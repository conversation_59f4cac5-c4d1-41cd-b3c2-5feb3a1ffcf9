<?xml version="1.0"?>
<psalm
    name="SimpleSAMLphp testsuite"
    useDocblockTypes="true"
    errorLevel="4"
    reportMixedIssues="false"
    hideExternalErrors="true"
    allowStringToStandInForClass="true"
>
    <projectFiles>
        <directory name="tests" />

        <!-- Ignore certain directories -->
        <ignoreFiles>
            <directory name="vendor" />
        </ignoreFiles>
    </projectFiles>

    <stubs>
        <file name="vendor/simplesamlphp/simplesamlphp-test-framework/stubs/memcache.php" />
        <file name="vendor/simplesamlphp/simplesamlphp-test-framework/stubs/memcached.php" />
    </stubs>
</psalm>
