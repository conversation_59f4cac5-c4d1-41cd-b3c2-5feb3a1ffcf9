<?xml version="1.0"?>
<ruleset name="SimpleSAMLphp authlinkedin module ruleset">
    <description>
        By default it is less stringent about long lines than other coding standards
    </description>

    <file>config</file>
    <file>src</file>
    <file>tests</file>
    <!-- Use this to exclude paths. You can have multiple patterns -->
    <!--<exclude-pattern>*/tests/*</exclude-pattern>-->
    <!--<exclude-pattern>*/other/*</exclude-pattern>-->

    <!-- This is the rule we inherit from. If you want to exclude some specific rules, see the docs on how to do that -->
    <rule ref="PSR12"/>
    <rule ref="vendor/simplesamlphp/simplesamlphp-test-framework/phpcs-simplesamlphp.xml"/>
</ruleset>
