---

# To get started with Dependabot version updates, you'll need to specify which
# package ecosystems to update and where the package manifests are located.
# Please see the documentation for all configuration options:
# https://docs.github.com/github/administering-a-repository/configuration-options-for-dependency-updates

version: 2
updates:
  - package-ecosystem: "github-actions"  # See documentation for possible values
    directory: "/"  # Location of package manifests
    schedule:
      interval: "weekly"
    groups:
      all-actions:
        patterns: ["*"]

  - package-ecosystem: "composer"  # See documentation for possible values
    directory: "/"  # Location of package manifests
    schedule:
      interval: "weekly"
    allow:
      # Allow both direct and indirect updates for all packages
      - dependency-type: "all"
    ignore:
      - dependency-name: "*"
        update-types: ["version-update:semver-major"]
    groups:
      production-dependencies:
        dependency-type: "production"
        exclude-patterns:
          - "symfony/*"
      development-dependencies:
        dependency-type: "development"
      symfony:
        patterns:
          - "symfony/*"
