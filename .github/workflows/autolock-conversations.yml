---

name: 'Lock Threads'

on:  # yamllint disable-line rule:truthy
  schedule:
    - cron: '0 0 * * *'
  workflow_dispatch:

permissions:
  issues: write
  pull-requests: write

concurrency:
  group: lock

jobs:
  action:
    runs-on: ubuntu-latest
    steps:
      - uses: dessant/lock-threads@v5
        with:
          issue-inactive-days: '90'
          pr-inactive-days: '90'
          log-output: true
